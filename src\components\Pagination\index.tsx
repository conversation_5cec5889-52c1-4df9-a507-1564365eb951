import styles from "./pagination.module.scss";
import { Fade } from "@mui/material";
import { CaretLeft, CaretRight } from "../svgs";
import { useState, useEffect } from "react";

const Pagination = ({ currentPage, setCurrentPage, numberOfPages }: any) => {
  const [pages, setPages] = useState([] as any);

  useEffect(() => {
    if (numberOfPages <= 6) {
      setPages(Array.from(Array(numberOfPages).keys()));
    } else if (pages.length === 0 || currentPage < 5) {
      setPages([0, 1, 2, 3, 4, -1, numberOfPages - 1]);
    } else if (currentPage > numberOfPages - 4) {
      setPages([
        0,
        -1,
        numberOfPages - 5,
        numberOfPages - 4,
        numberOfPages - 3,
        numberOfPages - 2,
        numberOfPages - 1,
      ]);
    } else {
      setPages([
        0,
        -1,
        currentPage - 2,
        currentPage - 1,
        currentPage,
        -1,
        numberOfPages - 1,
      ]);
    }
  }, [currentPage, numberOfPages]);

  return (
    <div className={styles.pageNumbers}>
      <Fade in={currentPage !== 1}>
        <div
          className={styles.pageArrowButton}
          onClick={() => {
            if (currentPage !== 1) {
              setCurrentPage(currentPage - 1);
            }
          }}
        >
          <CaretLeft />
        </div>
      </Fade>
      {pages.map((num: number, index: number) => {
        if (num === -1) {
          return (
            <div key={`dots-${index}`} className={styles.dots}>
              •••
            </div>
          );
        } else {
          return (
            <div
              className={`${styles.pageNumber} ${
                currentPage === num + 1 && styles.activePageNumber
              }`}
              onClick={() => {
                setCurrentPage(num + 1);
              }}
              key={`pagination-button-` + num}
            >
              {num + 1}
            </div>
          );
        }
      })}
      <Fade in={currentPage !== numberOfPages && numberOfPages !== 0}>
        <div
          className={styles.pageArrowButton}
          onClick={() => {
            if (currentPage !== numberOfPages) {
              setCurrentPage(currentPage + 1);
            }
          }}
        >
          <CaretRight />
        </div>
      </Fade>
    </div>
  );
};

export default Pagination;
