import { useEffect, useState } from "react";
import { Input } from "../Input";
import Modal from "../Modal";
import SelectInput from "../SelectInput";
import SingleDatePicker from "../SingleDatePicker";
import styles from "./edit-promotion-modal.module.scss";
import {
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
} from "../utils/InputHandlers";
import { validateAll } from "indicative/validator";
import SegmentedControl from "../SegmentedControl";
import { ApiPut } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import Toggle from "../Toggle";
import DualModeInput from "../DualModeInput";
import { adjustPromoStartDate } from "../utils/promoUtils";

const fields = [
  "promoName",
  "promoStatus",
  "promoType",
  "promoAmount",
  "priceToDiscount",
  "feeToDiscount",
  "startDate",
  "expiryDate",
  "promoCode",
  "promoAmountType",
  "isReusable",
  "maxCodeUses",
];

const rules = getRules(fields);
const messages = getMessages(fields);

const EditPromotionModal = ({ show, setShow, promoDetails, refresh }: any) => {
  const [data, setData] = useState(createStateObject(fields));
  const [submitting, setSubmitting] = useState(false);
  const dispatch = useDispatch();

  // populate values
  useEffect(() => {
    if (show && promoDetails) {
      const existingData = {
        promoName: promoDetails.name,
        promoStatus: promoDetails.status,
        promoType: promoDetails.type,
        promoAmount: promoDetails.amount,
        promoAmountType: promoDetails.amountType,
        feeToDiscount:
          promoDetails.type === "FEE_DISCOUNT"
            ? promoDetails.feeToDiscount
            : "",
        priceToDiscount:
          promoDetails.type === "ACCOUNT_DISCOUNT"
            ? promoDetails.feeToDiscount
            : "",
        startDate: promoDetails.startDate,
        expiryDate: promoDetails.expiryDate,
        promoCode: promoDetails.promoCode,
        isReusable: !!promoDetails.maxCodeUses,
        maxCodeUses: promoDetails.maxCodeUses,
      };

      setData(existingData);
    }
  }, [setData, promoDetails, show]);

  const reset = () => {
    setData(createStateObject(fields, ""));
  };

  /* Conditional field booleans */
  const isPriceDiscount = data.promoType === "ACCOUNT_DISCOUNT";
  const isFeeDiscount = data.promoType === "FEE_DISCOUNT";
  const isReusable = !!data.isReusable;
  const isPercentage = data.promoAmountType === "PERCENTAGE";
  const isFixed = data.promoAmountType === "FIXED";

  const editDiscount = () => {
    const testData = {
      promoName: data.promoName.trim(),
      promoStatus: data.promoStatus,
      promoType: data.promoType,
      promoAmount: data.promoAmount,
      promoAmountType: data.promoAmountType,
      priceToDiscount: isFixed ? "TOTAL" : data.priceToDiscount || undefined,
      feeToDiscount: data.feeToDiscount,
      startDate: adjustPromoStartDate(data.startDate),
      expiryDate: data.expiryDate,
      promoCode: data.promoCode,
      isReusable: data.isReusable || false,
      maxCodeUses: data.isReusable ? Number(data.maxCodeUses) : 0,
    };

    validateAll(testData, rules, messages)
      .then((response) => {
        setSubmitting(true);
        const updatedData = {
          type: testData.promoType,
          name: testData.promoName,
          amount: testData.promoAmount,
          amountType: testData.promoAmountType,
          promoCode: testData.promoCode,
          feeToDiscount: isPriceDiscount
            ? testData.priceToDiscount
            : testData.feeToDiscount,
          startDate: testData.startDate,
          expiryDate: testData.expiryDate,
          isReusable: testData.isReusable,
          maxCodeUses: testData.maxCodeUses,
          status: testData.promoStatus,
        };

        ApiPut(`/promotions/${promoDetails.id}`, updatedData)
          .then(() => {
            dispatch({
              type: "notify",
              payload: {
                error: false,
                heading: "Success",
                message: "Changes saved",
              },
            });
            setShow(false);
            reset();
            refresh?.();
          })
          .catch((error) => {
            dispatch({
              type: "notify",
              payload: {
                error: true,
                heading: "Something went wrong",
                message: error.response.data.message,
              },
            });
          })
          .finally(() => {
            setSubmitting(false);
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  return (
    <Modal
      saveButton={<>Edit Discount</>}
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={editDiscount}
      close={() => {
        setShow(false);
        reset();
      }}
      loading={submitting}
      fullsize
      title="Edit Promotion Discount"
    >
      <div className={`${styles.main} normal-select-input`}>
        <Input
          label="Promo Name"
          value={data.promoName}
          onChange={(e: any) => {
            handleInputChange("promoName", e, data, setData);
          }}
          error={data.errors?.promoName}
        />
        <SelectInput
          placeholder="Status"
          selected={data.promoStatus}
          options={[
            {
              label: "Active",
              value: true,
            },
            {
              label: "Inactive",
              value: false,
            },
          ]}
          onChange={(value: any) => {
            setData({
              ...data,
              promoStatus: value,
              errors: {
                ...data.errors,
                promoStatus: "",
              },
            });
          }}
          error={data.errors?.promoStatus}
        />
        <SelectInput
          placeholder="Select Promo Type"
          selected={data.promoType}
          options={[
            {
              label: "Fee Discount",
              value: "FEE_DISCOUNT",
            },
            {
              label: "Discount",
              value: "ACCOUNT_DISCOUNT",
            },
          ]}
          onChange={(value: any) => {
            setData({
              ...data,
              promoType: value,
              errors: {
                ...data.errors,
                promoType: "",
              },
            });
          }}
          error={data.errors?.promoType}
        />

        {isFeeDiscount && (
          <SelectInput
            placeholder="Select Fee to Discount"
            selected={data.feeToDiscount}
            options={[
              {
                label: "Activation",
                value: "ACTIVATION",
              },
              {
                label: "Regulatory",
                value: "REGULATORY",
              },
            ]}
            onChange={(value: any) => {
              setData({
                ...data,
                feeToDiscount: value,
                errors: {
                  ...data.errors,
                  feeToDiscount: "",
                },
              });
            }}
            error={data.errors?.feeToDiscount}
          />
        )}

        <DualModeInput
          label="Discount amount"
          value={data.promoAmount}
          activeModeValue={data.promoAmountType}
          onChange={(e) => {
            handleInputChange(
              "promoAmount",
              { target: { value: e.value } },
              data,
              setData,
            );
            handleInputChange(
              "promoAmountType",
              { target: { value: e.mode.value } },
              data,
              setData,
            );
          }}
          modes={
            [
              { value: "FIXED", label: "$" },
              {
                value: "PERCENTAGE",
                label: "%",
                transformer: (value) => {
                  if (Number(value) > 100 || Number(value) < 0)
                    return data.promoAmount;
                  return value;
                },
              },
            ] as const
          }
          onModeChange={(modeValue) => {
            handleInputChange(
              "promoAmountType",
              { target: { value: modeValue } },
              data,
              setData,
            );
          }}
          error={data.errors?.promoAmount}
        />

        {isPriceDiscount && isPercentage && (
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <span>Apply to:</span>
            <SegmentedControl
              selectedValue={data.priceToDiscount}
              options={[
                { label: "Total", value: "TOTAL" },
                { label: "Subtotal", value: "SUBTOTAL" },
              ]}
              onChange={(value: any) => {
                setData({
                  ...data,
                  errors: {
                    ...data.errors,
                    priceToDiscount: "",
                  },
                  priceToDiscount: value,
                });
              }}
            />
          </div>
        )}

        <SingleDatePicker
          date={data.startDate ? new Date(data.startDate) : undefined}
          setDate={(date: any) => {
            setData({
              ...data,
              errors: {
                ...data.errors,
                startDate: "",
              },
              startDate: date,
            });
          }}
          label="Start Date"
          future
          error={data.errors?.startDate}
        />

        <SingleDatePicker
          date={data.expiryDate ? new Date(data.expiryDate) : undefined}
          minDate={data.startDate}
          setDate={(date: any) => {
            setData({
              ...data,
              errors: {
                ...data.errors,
                expiryDate: "",
              },
              expiryDate: date,
            });
          }}
          label="Expiry Date"
          future
          error={data.errors?.expiryDate}
        />

        <Input
          label="Promo Code"
          value={data.promoCode}
          onChange={(e: any) => {
            handleInputChange("promoCode", e, data, setData);
          }}
          error={data.errors?.promoCode}
        />

        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <span>Code Reusable</span>

          <div style={{ display: "flex", gap: 4, alignItems: "center" }}>
            <span>{data.isReusable ? "Yes" : "No"}</span>
            <Toggle
              onChange={() => {
                setData({
                  ...data,
                  isReusable: !data.isReusable,
                  errors: {
                    ...data.errors,
                  },
                });
              }}
              on={data.isReusable}
            />
          </div>
        </div>

        {isReusable && (
          <Input
            label="Max Code Uses"
            value={data.maxCodeUses}
            onChange={(e: any) => {
              handleInputChange("maxCodeUses", e, data, setData);
            }}
            number
            error={data.errors?.maxCodeUses}
          />
        )}
      </div>
    </Modal>
  );
};

export default EditPromotionModal;
