import { Collapse } from "@mui/material";
import styles from "./remove-filters-bar.module.scss";
import Button from "../Button";
import { AnimatePresence } from "framer-motion";
import RemoveFilter from "../RemoveFilter";
import formatDate from "../utils/formatDate";
import {
  handleFilterChange,
  handleRemoveFilterType,
} from "../utils/searchAndFilter";
import { statuses } from "../StatusPill";

const RemoveFiltersBar = ({
  filters,
  setFilters,
  resetFilters,
  grey,
  group,
  orders,
}: any) => {
  const groupLabels = {
    userName: "users",
    userEmail: "emails",
    action: "activity types",
    ipAddress: "IPs",
  } as any;

  return (
    <Collapse
      in={Object.keys(filters).some((key: string) => {
        if (Array.isArray(filters[key])) {
          return filters[key].length !== 0;
        } else {
          return filters[key].start && filters[key].end;
        }
      })}
    >
      <div className={styles.removeFilters}>
        <Button
          style={{
            padding: 0,
            fontWeight: 500,
            fontSize: 14,
            marginRight: 24,
            marginBottom: 8,
          }}
          color="quaternary"
          onClick={resetFilters}
        >
          Clear all
        </Button>
        <AnimatePresence>
          {Object.keys(filters).map((filterType: string) => {
            if (
              filterType === "time" ||
              filterType === "date" ||
              filterType === "creationDate" ||
              filterType === "activationDate"
            ) {
              if (filters[filterType].start && filters[filterType].end) {
                return (
                  <RemoveFilter
                    type={filterType}
                    handleRemoveFilter={(x: any, y: any) => {
                      setFilters({
                        ...filters,
                        [filterType]: {
                          start: null,
                          end: null,
                        },
                      });
                    }}
                    key={"remove-" + filterType}
                    filter={filterType}
                    grey={grey}
                  >
                    {filters[filterType].start === filters[filterType].end
                      ? formatDate(filters[filterType].start, true)
                      : `${formatDate(
                          filters[filterType].start,
                          true
                        )} - ${formatDate(filters[filterType].end, true)}`}
                  </RemoveFilter>
                );
              }
            } else {
              if (group && filters[filterType].length > 1) {
                return (
                  <RemoveFilter
                    type={filterType}
                    handleRemoveFilter={handleRemoveFilterType}
                    filterObject={filters}
                    setFilterObject={setFilters}
                    key={"remove-" + filterType}
                    grey={grey}
                    group
                  >
                    {filters[filterType].length} {groupLabels[filterType]}{" "}
                    selected
                  </RemoveFilter>
                );
              } else {
                return filters[filterType].map((filter: any) => (
                  <RemoveFilter
                    type={filterType}
                    handleRemoveFilter={handleFilterChange}
                    filterObject={filters}
                    setFilterObject={setFilters}
                    key={"remove-" + filter}
                    filter={filter}
                    status={
                      orders && filterType === "status"
                        ? filter
                        : filterType === "subscriberStatus" ||
                          filterType === "status"
                        ? statuses.indexOf(filter)
                        : null
                    }
                    grey={grey}
                    sim={filterType === "simType" ? filter : null}
                    order={orders}
                  >
                    {filter}
                  </RemoveFilter>
                ));
              }
            }
          })}
        </AnimatePresence>
      </div>
    </Collapse>
  );
};

export default RemoveFiltersBar;
