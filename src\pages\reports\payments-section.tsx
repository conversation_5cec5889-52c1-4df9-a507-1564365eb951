import { useParams } from "react-router-dom";
import styles from "../../styles/reports.module.scss";
import { useDispatch } from "react-redux";
import { Calendar, Export } from "../../components/svgs";
import { useEffect, useState } from "react";
import UserSkeleton from "../../components/UserSkeleton";
import { padArrayToLength } from "../../components/utils/padArray";
import { paymentsFields } from "../../components/utils/reportFields";
import Pagination from "../../components/Pagination";
import { ApiGetWithId } from "../api/api";
import moment from "moment";
import DatePicker from "../../components/DatePicker";
import formatDate from "../../components/utils/formatDate";
import PaymentStatus from "../../components/PaymentStatus";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  Filler,
} from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";
import { Pie, Line } from "react-chartjs-2";
import {
  formatChartDate,
  formatChartDateLabel,
} from "../../components/utils/formatDate";
import Button from "../../components/Button";
import { exportCsv } from "../../components/utils/exportCsv";

ChartJS.register(
  ArcElement,
  Tooltip,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  PointElement,
  LineElement,
  Filler,
);

type PaymentRange =
  | "today"
  | "yesterday"
  | "last-7-days"
  | "last-month"
  | "custom";

const PaymentsSection = ({ typeSelection }: any) => {
  const { mvnoId } = useParams();
  const dispatch = useDispatch();

  const [reportData, setReportData] = useState([] as any);
  const [chartData, setChartData] = useState(null as any);
  const [exportData, setExportData] = useState(null as any);

  const constantOptions = {
    responsive: true,
    scales: {
      x: {
        ticks: {
          color: "#1a1a1a",
          padding: 16,
          font: {
            size: 12,
            family: "Poppins",
          },
          autoSkip: true,
          callback(val: any, index: any): string {
            return formatChartDateLabel(
              (this as any).getLabelForValue(val),
            ) as any;
          },
        },
        border: {
          display: false,
        },
        grid: {
          color: "rgba(181, 181, 181, 0)",
        },
        offset: chartData?.labels.length === 1,
      },
      y: {
        ticks: {
          color: "#1a1a1a",
          padding: 16,
          font: {
            size: 12,
            family: "Poppins",
          },
          autoSkip: true,
          beginAtZero: true,
          callback: (value: number) => {
            if (value % 1 === 0) {
              return value === 0
                ? value
                : value >= 1000
                  ? "$" + value / 1000 + "k"
                  : "$" + value;
            }
          },
        },
        border: {
          display: false,
        },
        grid: {
          color: "#B5B5B5",
          drawTicks: false,
          display: true,
          lineWidth: 0.5,
        },
        beginAtZero: true,
      },
    },
    plugins: {
      tooltip: {
        callbacks: {
          title: (tooltipItem: any, data: any) => {
            return formatChartDate(tooltipItem[0].label);
          },
        },
        padding: 16,
        bodySpacing: 12,
        boxPadding: 6,
        cornerRadius: 8,
        bodyFont: {
          size: 12,
          family: "Poppins",
        },
      },
    },
  } as any;

  const [paymentsSelection, setPaymentsSelection] =
    useState<PaymentRange>("last-7-days");

  const [dateRange, setDateRange] = useState({
    start: null,
    end: null,
  } as any);

  useEffect(() => {
    if (dateRange.start && dateRange.end) {
      setPaymentsSelection("custom");
    }
  }, [dateRange]);

  useEffect(() => {
    if (paymentsSelection !== "custom") {
      setDateRange({
        start: null,
        end: null,
      });
    }
  }, [paymentsSelection]);

  const itemsPerPage = 9;

  const [loading, setLoading] = useState(true);
  const [chartsLoading, setChartsLoading] = useState(true);

  const [currentPage, setCurrentPage] = useState(1);
  const [numberOfPages, setNumberOfPages] = useState(1);

  useEffect(() => {
    if (typeSelection === "payments") {
      let startDate, endDate;
      if (paymentsSelection === "custom") {
        startDate = moment(dateRange.start).format("YYYY-MM-DD");
        endDate = moment(dateRange.end).format("YYYY-MM-DD");
      } else {
        startDate = moment()
          .subtract(paymentsRangeBoundaries[paymentsSelection].start, "d")
          .format("YYYY-MM-DD");
        endDate = moment()
          .subtract(paymentsRangeBoundaries[paymentsSelection].end, "d")
          .format("YYYY-MM-DD");
      }

      setLoading(true);
      ApiGetWithId(
        `/accounts/payment-audit?pageSize=${itemsPerPage}&currentPage=${currentPage - 1}&startDate=${startDate}&endDate=${endDate}`,
        mvnoId,
      ).then((response) => {
        setLoading(false);
        setNumberOfPages(response.data.totalPages);
        if (currentPage > response.data.totalPages) {
          setCurrentPage(1);
        }
        setReportData(response.data.content);
      });
    }
  }, [typeSelection, paymentsSelection, currentPage]);

  useEffect(() => {
    if (typeSelection === "payments") {
      let startDate, endDate;
      if (paymentsSelection === "custom") {
        if (!dateRange.start || !dateRange.end) return;
        startDate = moment(dateRange.start).format("YYYY-MM-DD");
        endDate = moment(dateRange.end).format("YYYY-MM-DD");
      } else {
        startDate = moment()
          .subtract(paymentsRangeBoundaries[paymentsSelection].start, "d")
          .format("YYYY-MM-DD");
        endDate = moment()
          .subtract(paymentsRangeBoundaries[paymentsSelection].end, "d")
          .format("YYYY-MM-DD");
      }

      setChartsLoading(true);
      ApiGetWithId(
        `/accounts/payment-audits?startDate=${startDate}&endDate=${endDate}`,
        mvnoId,
      ).then((response) => {
        setExportData(response.data);
        if (paymentsSelection === "custom") {
          const now = moment();
          setChartData(
            formatPaymentsForChart(
              response.data,
              now.diff(moment(dateRange.start), "d"),
              now.diff(moment(dateRange.end), "d"),
            ),
          );
        } else {
          setChartData(
            formatPaymentsForChart(
              response.data,
              paymentsRangeBoundaries[paymentsSelection].start,
              paymentsRangeBoundaries[paymentsSelection].end,
            ),
          );
        }
        setChartsLoading(false);
      });
    }
  }, [typeSelection, paymentsSelection, dateRange]);

  const paymentsRangeOptions = [
    {
      label: "Today",
      key: "today",
    },
    {
      label: "Yesterday",
      key: "yesterday",
    },
    {
      label: "Last 7 days",
      key: "last-7-days",
    },
    {
      label: "Last month",
      key: "last-month",
    },
  ];

  const paymentsRangeBoundaries = {
    today: {
      start: 0,
      end: 0,
    },
    yesterday: {
      start: 1,
      end: 1,
    },
    "last-7-days": {
      start: 6,
      end: 0,
    },
    "last-month": {
      start: 30,
      end: 0,
    },
    custom: {
      start: 0,
      end: 0,
    },
  };

  const [totals, setTotals] = useState({
    successful: 0,
    failed: 0,
    upcoming: 0,
  });

  const [numberOfStatus, setNumberOfStatus] = useState({
    successful: 0,
    failed: 0,
  });

  const formatPaymentsForChart = (data: any, start: number, end: number) => {
    let days = [];
    for (let i = start; i >= end; i--) {
      days.push(moment().subtract(i, "d"));
    }

    let sortedData = {
      successful: [],
      failed: [],
      upcoming: [],
    } as any;

    days.forEach((day: any) => {
      let paymentsOnDay = data.filter((item: any) =>
        moment(item.createdAt).isSame(day, "day"),
      );
      let successfulOnDay = paymentsOnDay.filter(
        (item: any) =>
          item.status === "Success" || item.status === "Successful",
      );
      let failedOnDay = paymentsOnDay.filter(
        (item: any) => item.status === "Failed",
      );
      let upcomingOnDay = paymentsOnDay.filter(
        (item: any) => item.status === "Upcoming",
      );
      let successfulAmount = 0;
      let failedAmount = 0;
      let upcomingAmount = 0;
      successfulOnDay.forEach((item: any) => {
        successfulAmount += item.amount;
      });
      failedOnDay.forEach((item: any) => {
        failedAmount += item.amount;
      });
      upcomingOnDay.forEach((item: any) => {
        upcomingAmount += item.amount;
      });
      sortedData.successful.push(successfulAmount);
      sortedData.failed.push(failedAmount);
      sortedData.upcoming.push(upcomingAmount);
    });

    const totalSuccessful = sortedData.successful.reduce(
      (partialSum: number, i: number) => partialSum + i,
      0,
    );
    const totalFailed = sortedData.failed.reduce(
      (partialSum: number, i: number) => partialSum + i,
      0,
    );
    const totalUpcoming = sortedData.upcoming.reduce(
      (partialSum: number, i: number) => partialSum + i,
      0,
    );

    setTotals({
      successful: totalSuccessful,
      failed: totalFailed,
      upcoming: totalUpcoming,
    });

    const numberOfSuccessful = data.filter(
      (item: any) => item.status === "Success" || item.status === "Successful",
    ).length;
    const numberOfFailed = data.filter(
      (item: any) => item.status === "Failed",
    ).length;

    setNumberOfStatus({
      successful: numberOfSuccessful,
      failed: numberOfFailed,
    });

    return {
      labels: days.map((day) => day.format("YYYY-MM-DD")),
      datasets: [
        {
          label: "Successful",
          data: sortedData.successful,
          fill: true,
          borderColor: "rgba(6, 193, 131, 0.6)",
          borderWidth: 5,
          pointStyle: days.length === (1 as any),
          backgroundColor: "rgba(0,0,0,0)",
        },
        {
          label: "Failed",
          data: sortedData.failed,
          fill: true,
          borderColor: "rgba(234, 61, 92, 0.6)",
          borderWidth: 5,
          pointStyle: days.length === (1 as any),
          backgroundColor: "rgba(0,0,0,0)",
        },
        {
          label: "Upcoming",
          data: sortedData.upcoming,
          fill: true,
          borderColor: "rgba(242, 164, 70, 0.6)",
          borderWidth: 5,
          pointStyle: days.length === (1 as any),
          backgroundColor: "rgba(0,0,0,0)",
        },
      ],
    };
  };

  return (
    <>
      <div className={styles.paymentsInsights}>
        <Button
          disabled={chartsLoading}
          style={{ position: "absolute", right: 0, top: -70 }}
          onClick={() => {
            exportCsv(paymentsFields, exportData, dispatch);
          }}
        >
          <Export /> Export
        </Button>
        <div className={styles.paymentsTitleRow}>
          <h3 className={styles.paymentsHeading}>Insights</h3>
          <div className={styles.rangeButtons}>
            {paymentsRangeOptions.map((option: any) => (
              <div
                className={`${styles.button} ${
                  paymentsSelection === option.key && styles.active
                }`}
                onClick={() => {
                  setPaymentsSelection(option.key);
                }}
                color="secondary"
                style={{ marginRight: 12 }}
              >
                {option.label}
              </div>
            ))}
            <DatePicker
              label={
                <div
                  className={`${styles.button} ${
                    paymentsSelection === "custom" && styles.active
                  }`}
                  color="secondary"
                >
                  <Calendar />{" "}
                  {paymentsSelection === "custom" &&
                  dateRange.start &&
                  dateRange.end
                    ? `${formatDate(dateRange.start)} - ${formatDate(
                        dateRange.end,
                      )}`
                    : "DD/MM/YY - DD/MM/YY"}
                </div>
              }
              masterFrom={dateRange.start}
              masterUntil={dateRange.end}
              onChange={(newFrom: Date, newUntil: Date) => {
                setDateRange({
                  start: newFrom,
                  end: newUntil,
                });
              }}
              reports
            />
          </div>
        </div>
        <div
          className={styles.charts}
          style={{ opacity: chartsLoading ? 0.5 : 1 }}
        >
          <div className={styles.lineChart}>
            {chartData && <Line options={constantOptions} data={chartData} />}
          </div>
          <div className={styles.doughnut}>
            <Pie
              plugins={[ChartDataLabels]}
              options={{
                plugins: {
                  tooltip: {
                    padding: 16,
                    bodySpacing: 12,
                    boxPadding: 6,
                    cornerRadius: 8,
                    bodyFont: {
                      size: 12,
                      family: "Poppins",
                    },
                  },
                  datalabels: {
                    anchor: (context) => {
                      if (
                        context.dataset.data.some((item: any) => item === 0)
                      ) {
                        return "start";
                      } else {
                        return "center";
                      }
                    },
                    color: "#1A1A1A",
                    font: {
                      weight: 600,
                      size: 26,
                      family: "Poppins",
                    },
                    display: (context) => {
                      return context.dataset.data[context.dataIndex] !== 0;
                    },
                    formatter: (value, context) => {
                      const total = context.dataset.data.reduce(
                        (partialSum: any, i: any) => partialSum + i,
                        0,
                      );
                      return `${Math.round((value / total) * 100)}%`;
                    },
                  },
                },
              }}
              data={{
                labels: ["Successful", "Failed"],
                datasets: [
                  {
                    label: "# of payments",
                    data: [numberOfStatus.successful, numberOfStatus.failed],
                    backgroundColor: ["#06C183", "#EA3D5C"],
                    borderWidth: 0,
                  },
                ],
              }}
            />
          </div>
          <div className={styles.pending}>
            <div className={styles.label}>Upcoming</div>
            <div className={styles.amount}>
              ${totals.upcoming.toLocaleString()}
            </div>
          </div>
        </div>
        <div className={styles.legendRow}>
          <div className={styles.legend}>
            <div className={styles.legendItem}>
              <div className={`${styles.indicator} ${styles.successful}`} />
              Successful
            </div>
            <div className={styles.legendItem}>
              <div className={`${styles.indicator} ${styles.failed}`} />
              Failed
            </div>
            <div className={styles.legendItem}>
              <div className={`${styles.indicator} ${styles.pending}`} />
              Upcoming
            </div>
          </div>
          <div className={styles.totals}>
            <div className={styles.totalLabel}>Total:</div>
            <div className={styles.totalItem}>
              <div className={`${styles.indicator} ${styles.successful}`} />$
              {totals.successful.toLocaleString()}
            </div>
            <div className={styles.totalItem}>
              <div className={`${styles.indicator} ${styles.failed}`} />$
              {totals.failed.toLocaleString()}
            </div>
            <div className={styles.totalItem}>
              <div className={`${styles.indicator} ${styles.pending}`} />$
              {totals.upcoming.toLocaleString()}
            </div>
          </div>
        </div>
      </div>
      <div className={styles.mainTile}>
        <div className={styles.content}>
          <div className={`${styles.tableContainer} table-scroll`}>
            <table>
              <thead>
                <tr>
                  {paymentsFields.map((field) => (
                    <th>{field.label}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {!loading ? (
                  reportData.length !== 0 ? (
                    padArrayToLength(reportData, itemsPerPage, null).map(
                      (item: any, index: number) => {
                        if (item === null) {
                          return (
                            <tr
                              key={`blank-row-${index}`}
                              style={{
                                visibility: "hidden",
                                pointerEvents: "none",
                              }}
                            ></tr>
                          );
                        } else {
                          return (
                            <tr key={"report-row-" + index}>
                              {paymentsFields.map((field: any) => {
                                if (
                                  field.key === "amount" ||
                                  field.key === "taxAmount" ||
                                  field.key === "regulatoryFee"
                                ) {
                                  return (
                                    <td>
                                      $
                                      {item[field.key] === null
                                        ? "0.00"
                                        : item[field.key]?.toFixed(2)}
                                    </td>
                                  );
                                } else if (field.key === "status") {
                                  return (
                                    <td>
                                      <PaymentStatus status={item[field.key]} />
                                    </td>
                                  );
                                } else if (field.key === "createdAt") {
                                  return (
                                    <td>
                                      {formatDate(item[field.key], false, true)}
                                    </td>
                                  );
                                } else {
                                  return <td>{item[field.key] || "-"}</td>;
                                }
                              })}
                            </tr>
                          );
                        }
                      },
                    )
                  ) : (
                    <tr style={{ background: "none" }}>
                      <td colSpan={12}>
                        <div className={styles.noneFound}>
                          <img src="/none_found.svg" />
                          <h3>No entries found for this time period</h3>
                        </div>
                      </td>
                    </tr>
                  )
                ) : (
                  Array.from({ length: itemsPerPage }, (v, i) => i).map((i) => (
                    <UserSkeleton
                      key={"report-skeleton-" + i}
                      noOfStandard={6}
                    />
                  ))
                )}
              </tbody>
            </table>
          </div>
          <div className={styles.pagination}>
            <Pagination
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              numberOfPages={numberOfPages}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default PaymentsSection;
