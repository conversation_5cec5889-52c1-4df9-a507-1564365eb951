import styles from "./quick-filter.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown } from "../svgs";
import { useEffect, useRef, useState } from "react";
import Checkbox from "../Checkbox";
import Button from "../Button";

const QuickFilter = ({
  label,
  options,
  selected,
  setSelected,
  onChange,
}: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const [stagedChanges, setStagedChanges] = useState([...selected]);

  useEffect(() => {
    setStagedChanges([...selected]);
  }, [selected]);

  const reset = () => {
    toggleMenu(false);
  };

  return (
    <div className={styles.box}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        }`}
        onClick={() => {
          if (menuProps.state === "closing") {
            toggleMenu(false);
          } else {
            toggleMenu(true);
          }
        }}
      >
        {label}
        <ChevronDown />
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => {
          setStagedChanges([...selected]);
          toggleMenu(false);
        }}
        align="start"
        position="auto"
        viewScroll="close"
        onItemClick={(e) => (e.keepOpen = true)}
      >
        <div className={styles.container}>
          {options.map((item: any) => (
            <MenuItem
              className={`${styles.menuItem} ${
                selected === item && styles.selected
              }`}
              key={item}
            >
              <Checkbox
                checked={stagedChanges.includes(item.key)}
                onClick={() => {
                  if (stagedChanges.includes(item.key)) {
                    setStagedChanges(
                      stagedChanges.filter((change) => change !== item.key)
                    );
                  } else {
                    setStagedChanges([...stagedChanges, item.key]);
                  }
                }}
              />
              {item.label}
            </MenuItem>
          ))}
        </div>
        <div className={styles.buttons}>
          <Button
            color="quaternary"
            style={{
              marginRight: "auto",
              opacity:
                stagedChanges.length !== 0 || selected.length !== 0 ? 1 : 0,
              pointerEvents:
                stagedChanges.length !== 0 || selected.length !== 0
                  ? "all"
                  : "none",
            }}
            onClick={() => {
              setStagedChanges([]);
            }}
          >
            Clear All
          </Button>
          <Button
            onClick={() => {
              setStagedChanges(selected);
              reset();
            }}
            style={{ marginRight: 12, marginLeft: 20, minWidth: 0 }}
            color="secondary"
          >
            Cancel
          </Button>
          <Button
            style={{ minWidth: 0 }}
            onClick={() => {
              setSelected(stagedChanges);
              reset();
            }}
          >
            Apply
          </Button>
        </div>
      </ControlledMenu>
    </div>
  );
};

export default QuickFilter;
