import styles from "./modal.module.scss";
import Button from "../Button";
import { Fade } from "@mui/material";
import { Close } from "../svgs";
import { clearInput } from "../utils/InputHandlers";

const Modal = ({
  show,
  setShow,
  setData,
  close,
  proceed,
  loading,
  secondaryLoading,
  image = "",
  imageStyle,
  saveButton,
  saveButtonColor = "primary",
  secondary,
  onSecondary,
  cancelButton = "Cancel",
  children,
  onClose = () => {},
  scroll = true,
  fullSize,
  title = null,
  subtitle = null,
  clearContainer,
  onCancel = () => {},
  noCloseOnCancel,
  headerBackComponent,
  contentRef,
}: any) => {
  return (
    <Fade in={show} unmountOnExit>
      <div className={`${styles.container} ${clearContainer && styles.clear}`}>
        <div className={styles.modal}>
          {image.length ? (
            <div
              className={`${
                imageStyle ? styles.imgUniqueContainer : styles.imgContainer
              }`}
            >
              <img
                className={`${
                  imageStyle ? styles.imgUnique : styles.illustration
                }`}
                src={image}
              />
            </div>
          ) : (
            ""
          )}
          <div className={styles.main}>
            <div className={styles.top}>
              {headerBackComponent && (
                <div className={styles.headerBack}>{headerBackComponent}</div>
              )}
              <div className={`${styles.close} ${fullSize && styles.fullSize}`}>
                <Button
                  onClick={() => {
                    if (setShow === "change-imei") {
                      clearInput("imei", setData);
                      setShow("");
                    } else if (setShow === "change-iccid") {
                      clearInput("iccid", setData);
                      setShow("");
                    } else {
                      onClose();
                      close();
                    }
                  }}
                  disabled={loading || secondaryLoading}
                  color="tertiary"
                  style={{ padding: 0, marginBottom: 30, height: "initial" }}
                >
                  <Close />
                </Button>
              </div>
            </div>
            {title !== null && (
              <h2
                style={{ marginBottom: subtitle !== null ? 15 : 24 }}
                className={styles.title}
              >
                {title}
              </h2>
            )}
            {subtitle !== null && (
              <div className={styles.subtitle}>{subtitle}</div>
            )}
            <div
              className={`${styles.content} ${fullSize && styles.fullSize} ${
                scroll && "modal-scroll"
              }`}
              ref={contentRef}
            >
              {children}
            </div>
            <div className={styles.buttons}>
              {cancelButton && (
                <Button
                  style={{ minWidth: "initial" }}
                  disabled={loading || secondaryLoading}
                  onClick={() => {
                    onCancel();
                    if (!noCloseOnCancel) {
                      close();
                    }
                  }}
                  color="tertiary"
                >
                  {cancelButton}
                </Button>
              )}
              {secondary && (
                <Button
                  color="secondary"
                  style={{
                    minWidth: "initial",
                    whiteSpace: "nowrap",
                    marginRight: "16px",
                    marginLeft: "auto",
                  }}
                  onClick={() => {
                    if (onSecondary) {
                      onSecondary();
                    }
                  }}
                  loading={secondaryLoading}
                  disabled={loading}
                >
                  {secondary}
                </Button>
              )}
              {saveButton && (
                <Button
                  key={new Date()}
                  color={saveButtonColor || undefined}
                  style={{ minWidth: "initial", whiteSpace: "nowrap" }}
                  onClick={proceed}
                  loading={loading}
                  disabled={secondaryLoading}
                >
                  {saveButton}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </Fade>
  );
};

export default Modal;
