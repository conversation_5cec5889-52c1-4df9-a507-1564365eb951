import { createContext, useEffect, useState, useContext } from "react";
import { ApiGet, ApiGetNoAuth, ApiGetWithId } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import ManageFeatureModal from "../ManageFeaturesModal";
import ActivateSubscriberModal from "../ActivateSubscriberModal";
import EditSubscriptionModal from "../EditSubscriptionModal";
import AddTopupModal from "../AddTopupModal";
import ChangeSubscriptionModal from "../ChangeSubscriptionModal";
import ChangeImeiModal from "../ChangeImeiModal";
import ChangeMdnModal from "../ChangeMdnModal";
import ChangeBillCycleModal from "../ChangeBillCycleModal";
import ChangeIccidModal from "../ChangeIccidModal";
import { ConfirmCancel, ConfirmResumeOrRestore } from "../SubscriberModals";
import { ConfirmSuspend } from "../SubscriberModals";
import AddIccidModal from "../AddIccidModal";
import CancelPortModal from "../CancelPortModal";
import UpdatePortIn from "../UpdatePortIn";
import UpdateTempSubscription from "../UpdateTempSubscription";
import PortInForm from "../PortInForm";
import ConfirmActivateSaved from "../ConfirmActivateSaved";
import ConfirmDeleteSaved from "../ConfirmDeleteSaved";
import PortInErrorModal from "../PortInErrorModal";
import ResubmitActivationNoUpdate from "../ResubmitActivationNoUpdate";
import ResubmitActivation from "../ResubmitActivation";
import EditSubscriberModal from "../EditSubscriberModal";
import AddMissingPortinDetails from "../AddMissingPortinDetails";
import { useParams } from "react-router-dom";
import NicknameModal from "../../components/NicknameModal";

type PlanManageContextType = {
  plan: any;
  currentModal: string | null;
  setCurrentModal: (modal: string | null) => void;
};

const PlanManageContext = createContext<PlanManageContextType>({
  plan: null,
  currentModal: null,
  setCurrentModal: () => {},
});

export const usePlanManageContext = () => {
  const context = useContext(PlanManageContext);

  if (!context) {
    throw new Error(
      "usePlanManageContext must be used within a PlanManageProvider",
    );
  }

  return context;
};

type PlanManageProviderProps = {
  children: React.ReactNode;
  plan: any;
  repopulate: () => void;
  subscriberMid: string;
};

const PlanManageProvider = ({
  children,
  repopulate,
  plan,
  // todo: fetch subscriber info from the parent component instead of this
  subscriberMid,
}: PlanManageProviderProps) => {
  const [currentModal, setCurrentModal] = useState<string | null>(null);

  const dispatch = useDispatch();
  const { mvnoId } = useParams();

  const [subscriberInfo, setSubscriberInfo] = useState<any>();
  const [topups, setTopups] = useState<any>([]);
  const [features, setFeatures] = useState<any>([]);

  const fetchSubscriberInfo = () => {
    // todo: this should be handled in the parent component
    if (!subscriberMid) return;

    ApiGet(`/accounts/by-account/${subscriberMid}`)
      .then((response) => {
        setSubscriberInfo(response.data);
      })
      .catch((error) => {
        console.error("plan manage provider: ", error);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };
  const fetchTopups = () => {
    ApiGetWithId(`/products/topups`, mvnoId)
      .then((response) => {
        setTopups(response.data);
      })
      .catch((error) => {
        console.error("plan manage provider: ", error);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };
  const fetchFeatures = () => {
    ApiGetNoAuth("/accounts/features/all")
      .then((response) => {
        setFeatures(response.data);
      })
      .catch((error) => {
        console.error("plan manage provider: ", error);
      });
  };

  useEffect(() => {
    fetchSubscriberInfo();
    fetchTopups();
    fetchFeatures();
  }, []);

  return (
    <PlanManageContext.Provider value={{ plan, currentModal, setCurrentModal }}>
      <>
        <ActivateSubscriberModal
          show={currentModal === "add-subscription"}
          setShow={setCurrentModal}
          complete={() => {
            console.log("");
          }}
          addingNew
          mid={subscriberInfo ? subscriberInfo.mid : 0}
          repopulate={repopulate}
        />
        {plan?.features && (
          <ManageFeatureModal
            features={features}
            sub={plan}
            repopulate={repopulate}
            show={currentModal === "manage-features"}
            setShow={setCurrentModal}
          />
        )}
        <EditSubscriptionModal
          show={currentModal === "edit-subscription"}
          setShow={setCurrentModal}
          topups={topups}
        />
        <ChangeSubscriptionModal
          show={currentModal === "change-subscription"}
          setShow={setCurrentModal}
          plan={plan}
          repopulate={repopulate}
        />
        <AddTopupModal
          show={currentModal === "add-top-up"}
          setShow={setCurrentModal}
          plan={plan}
          repopulate={repopulate}
          topups={topups}
        />
        <ChangeImeiModal
          show={currentModal === "change-imei"}
          setShow={setCurrentModal}
          plan={plan}
          repopulate={repopulate}
          subInfo={subscriberInfo}
        />
        <ChangeIccidModal
          show={currentModal === "change-iccid"}
          setShow={setCurrentModal}
          plan={plan}
          subInfo={subscriberInfo}
          repopulate={repopulate}
        />
        <ChangeMdnModal
          show={currentModal === "change-mdn"}
          setShow={setCurrentModal}
          plan={plan}
          repopulate={repopulate}
        />
        <ChangeBillCycleModal
          show={currentModal === "change-bill-cycle"}
          setShow={setCurrentModal}
        />
        <ConfirmSuspend
          show={currentModal === "confirm-suspend"}
          close={() => {
            setCurrentModal("");
          }}
          number={plan?.mdn || plan?.subscriberNumber || null}
          repopulate={repopulate}
        />
        <ConfirmResumeOrRestore
          show={currentModal === "confirm-restore"}
          close={() => {
            setCurrentModal("");
          }}
          number={plan?.mdn || plan?.subscriberNumber || null}
          type="restore"
          repopulate={repopulate}
        />
        <ConfirmResumeOrRestore
          show={currentModal === "confirm-resume"}
          close={() => {
            setCurrentModal("");
          }}
          number={plan?.mdn || plan?.subscriberNumber || null}
          type="resume"
          repopulate={repopulate}
        />
        <ConfirmCancel
          show={currentModal === "confirm-cancel"}
          close={() => {
            setCurrentModal("");
          }}
          number={plan?.mdn || plan?.subscriberNumber || null}
          repopulate={repopulate}
        />
        <CancelPortModal
          show={currentModal === "cancel-port"}
          setShow={setCurrentModal}
          repopulate={repopulate}
          portData={{
            attDetails: {
              ...(plan?.portInRequest || {}),
            },
            ...plan,
          }}
        />
        <AddIccidModal
          show={currentModal === "add-iccid"}
          setShow={setCurrentModal}
          repopulate={repopulate}
          plan={plan}
          name={
            subscriberInfo?.subscriberFirstName +
            " " +
            subscriberInfo?.subscriberLastName
          }
        />
        {subscriberInfo && (
          <PortInForm
            show={currentModal === "new-port"}
            setShow={setCurrentModal}
            data={subscriberInfo}
            repopulate={repopulate}
            accountDataProvided
            checkEligibility
          />
        )}
        <UpdateTempSubscription
          show={currentModal === "update-temp-subscription"}
          setShow={setCurrentModal}
          subData={plan}
          repopulate={repopulate}
        />
        <UpdatePortIn
          show={currentModal === "update-port"}
          setShow={setCurrentModal}
          updating
          accountDataProvided
          msisdn={plan?.portInRequest?.msisdn || ""}
          zipCode={plan?.zipCode || ""}
          portData={{
            attDetails: {
              ...(plan?.portInRequest || {}),
            },
            ...plan,
          }}
          temp={plan?.type === "tempPortingProcess"}
          repopulate={repopulate}
        />
        <PortInErrorModal
          show={currentModal === "view-port-fail"}
          setShow={setCurrentModal}
          error={
            plan?.failureReason || plan?.portInRequest?.failureReason || ""
          }
          msisdn={plan?.portInRequest?.msisdn || plan?.attDetails?.msisdn || ""}
          zipCode={plan?.zipCode || ""}
          portingLineView
          handleFinish={() => {
            setCurrentModal("");
          }}
        />
        <ConfirmDeleteSaved
          show={currentModal === "confirm-delete-saved"}
          setShow={setCurrentModal}
          plan={plan}
          repopulate={repopulate}
        />
        <ConfirmActivateSaved
          show={currentModal === "confirm-activate-saved"}
          setShow={setCurrentModal}
          plan={plan}
          repopulate={repopulate}
          accountId={subscriberInfo ? subscriberInfo.accountId : 0}
        />
        <AddMissingPortinDetails
          show={currentModal === "add-missing-port"}
          setShow={setCurrentModal}
          repopulate={repopulate}
          plan={plan}
        />
        {/* todo: doesn't belong here */}
        {/* <EditSubscriberModal
        show={showEditSubscriber}
        setShow={setShowEditSubscriber}
        repopulate={repopulate}
        subscriber={subscriberInfo}
      /> */}
        <ResubmitActivation
          show={currentModal === "resubmit-activation"}
          setShow={setCurrentModal}
          repopulate={repopulate}
          subData={plan}
        />
        <ResubmitActivationNoUpdate
          show={currentModal === "resubmit-activation-no-update"}
          setShow={setCurrentModal}
          repopulate={repopulate}
          subData={plan}
        />
        <NicknameModal
          show={currentModal === "nickname"}
          setShow={setCurrentModal}
          repopulate={repopulate}
          plan={plan}
        />
      </>
      {children}
    </PlanManageContext.Provider>
  );
};

export default PlanManageProvider;
