import React, { useEffect, useState } from "react";
import styles from "../../styles/support.module.scss";
import SearchSection from "../../components/SearchSection";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { ApiGet } from "../api/api";
import { motion } from "framer-motion";
import FaqSkeleton from "../../components/FaqSkeleton";
import Faq from "../../components/Faq";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import GuideVideo from "../../components/GuideVideo";

const Support = () => {
  const dispatch = useDispatch();

  const supportTypes = [
    {
      label: "FAQ",
      key: "faq",
    },
    {
      label: "Guides",
      key: "guides",
    },
  ];

  const Guides = [
    {
      title: "How to manage features",
      video:
        "https://public-airespring-bucket.s3.us-east-2.amazonaws.com/Support+Videos/Manage+features.mp4",
    },
  ];

  const [queryDisplay, setQueryDisplay] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    ApiGet("/faqs")
      .then((response) => {
        console.log(response);
        setFaqs(response.data);
        setFilteredFaqs(response.data);
      })
      .catch((error) => {
        console.log(error);
      });

    ApiGet("/videos")
      .then((response) => {
        console.log(response);
        setVideos(response.data);
        setFilteredVideos(response.data);
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  const [typeSelection, setTypeSelection] = useState("faq");

  const [faqs, setFaqs] = useState(null as any);
  const [videos, setVideos] = useState(null as any);

  const [filteredFaqs, setFilteredFaqs] = useState([] as any);
  const [filteredVideos, setFilteredVideos] = useState([] as any);

  useEffect(() => {
    console.log(filteredFaqs);
  }, [filteredFaqs]);

  return (
    <div className={styles.main}>
      <SearchSection
        data={typeSelection === "faq" ? faqs : videos}
        setFilteredData={
          typeSelection === "faq" ? setFilteredFaqs : setFilteredVideos
        }
        setQueryDisplay={setQueryDisplay}
        placeholder="Search"
        id="faq-search"
        setCurrentPage={setCurrentPage}
      />
      <div className={styles.titleBar}>
        <h3>Support</h3>
      </div>
      <div className={styles.mainTile}>
        <div style={{ paddingLeft: 40 }} className={styles.selectionWrapper}>
          {supportTypes.map((type: any) => (
            <div
              className={`${styles.selection} ${
                typeSelection === type.key && styles.activeSelection
              }`}
              style={{ marginRight: 16 }}
              onClick={() => {
                setTypeSelection(type.key);
              }}
            >
              <span>{type.label}</span>
              {typeSelection === type.key && (
                <motion.div
                  className={styles.background}
                  layoutId="underline"
                />
              )}
            </div>
          ))}
        </div>

        <SwitchTransition>
          <CSSTransition
            key={typeSelection}
            addEndListener={(node, done) =>
              node.addEventListener("transitionend", done, false)
            }
            classNames="fade"
          >
            {typeSelection === "faq" ? (
              <div className={`${styles.faq} modal-scroll`}>
                {faqs ? (
                  filteredFaqs.length > 0 ? (
                    filteredFaqs.map((item: any) => (
                      <Faq item={item} key={`faq-${item.question}`} />
                    ))
                  ) : (
                    <div className={styles.none}>
                      <img src="/none_found.svg" className={styles.noneImage} />
                      <span className={styles.noneText}>
                        No FAQs matching "{queryDisplay}"
                      </span>
                    </div>
                  )
                ) : (
                  Array.from({ length: 10 }).map(() => <FaqSkeleton />)
                )}
              </div>
            ) : (
              <div className={`${styles.guides} modal-scroll`}>
                {videos ? (
                  filteredVideos.length > 0 ? (
                    filteredVideos.map((item: any) => (
                      <GuideVideo item={item} key={`video-${item.title}`} />
                    ))
                  ) : (
                    <div className={styles.none}>
                      <img src="/none_found.svg" className={styles.noneImage} />
                      <span className={styles.noneText}>
                        No guides matching "{queryDisplay}"
                      </span>
                    </div>
                  )
                ) : (
                  ""
                )}
              </div>
            )}
          </CSSTransition>
        </SwitchTransition>
      </div>
    </div>
  );
};

export default Support;
