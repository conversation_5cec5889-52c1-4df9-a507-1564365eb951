@use "./theme.scss" as *;

.main {
  padding: 24px 40px;
}

.mvneMain {
  padding: 24px 40px;
  min-height: 100vh;
  background: #f1f1f1;
}

.topBar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1300px;
  margin: 0 auto;
  margin-bottom: 21px;
  h2 {
    margin: 0;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
  }
  .back {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.1s ease;
    &:hover {
      color: $orange;
    }
    svg {
      vertical-align: middle;
    }
  }
}

.mainTile {
  width: 100%;
  max-width: 1300px;
  margin: 0 auto;
  height: calc(100vh - 140px);
  background: #fff;
  border-radius: 24px;
  padding: 83px 24px 83px 0;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.grid {
  max-width: 100%;
  display: flex;
  align-items: center;
  justify-content: end;
  grid-column-gap: 60px;
  margin-right: auto;
  height: 100%;
}

.illustration {
  border-right: 1px solid #000;
  max-height: 100%;
  max-width: 600px;
  display: block;
}

.userDetails {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 250px;
}

.nameContainer {
  display: flex;
  align-items: center;
  margin: 16px 0 12px 0;
  .name {
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    margin-right: 12px;
  }
  .editButton {
    padding: 0;
    border: none;
    background: none;
    svg {
      vertical-align: middle;
    }
    cursor: pointer;
    &:hover {
      color: $orange;
    }
  }
}

.email {
  margin-bottom: 24px;
  font-size: 14px;
  line-height: 21px;
}
