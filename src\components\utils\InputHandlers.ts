import {
  checkAddress,
  checkName,
  checkPhone,
  checkPostcode,
  isNumeric,
} from "./CardDetailsCheckers";

const rules = {
  dueDate: "required",
  email: "required|email",
  password: "required",
  confirmPassword: "required",
  newPassword: "required",
  confirmNewPassword: "required",
  name: "required|min:2",
  imei: "required|min:15",
  sim: "required|min:18",
  mdn: "required",
  iccid: "required|min:18",
  firstName: "required",
  lastName: "required",
  role: "required",
  address: "required",
  streetNumber: "required",
  streetName: "required",
  city: "required|min:2",
  state: "required",
  zipCode: "required|min:5|max:5",
  //contactNumber: "required|min:7",
  portInNumber: "required|min:7",
  localId: "required",
  networkId: "required",
  billingAccountNumber: "required",
  //billingAccountPassword: "required",
  authorizationName: "required",
  npaNxx: "required",
  fromLine: "required",
  subject: "required",
  description: "required",
  agentId: "required",
  category: "required",
  status: "required",
  priority: "required",
  serviceArea: "required",
  note: "required",
  commercialName: "required",
  commercialPrice: "required",
  commercialDescription: "required",
  channelName: "required",
  channelDescription: "required",
  channelEmail: "required|email",
  channelPhoneNumber: "required",
  channel: "required",

  promoName: "required",
  promoStatus: "required",
  promoType: "required",
  promoAmount: "required|number|above:0",
  startDate: "required",
  expiryDate: "required",
  promoCode: "required",

  nickname: "required",
} as any;

const messages = {
  "dueDate.required": "Please select a due date",
  "email.required":
    "Please enter a valid email address. Example: <EMAIL>",
  "email.email":
    "Please enter a valid email address. Example: <EMAIL>",
  "password.required": "Please enter your password",
  "confirmPassword.required": "Please confirm your password",
  "newPassword.required": "Please enter a password",
  "confirmNewPassword.required": "Please confirm your password",
  "name.required": "Please enter name",
  "name.min": "Please enter name",
  "imei.required": "Please enter IMEI",
  "imei.min": "IMEI should be 15-17 digits",
  "sim.required": "Please enter SIM number",
  "sim.min": "SIM number should be 18-22 digits",
  "iccid.required": "Please enter ICCID number",
  "iccid.min": "ICCID number should be 18-22 digits",
  "mdn.required": "Please enter MDN",
  "firstName.required": "Please enter a first name",
  "lastName.required": "Please enter a last name",
  "role.required": "Please select a role",
  "address.required": "Please enter your address",
  "streetNumber.required": "Please enter street number",
  "streetName.required": "Please enter street name",
  "city.required": "Please enter city",
  "city.min": "Please enter city",
  "zipCode.required": "Please enter 5 digit ZIP Code",
  "zipCode.min": "Please enter 5 digit ZIP Code",
  "zipCode.max": "Please enter 5 digit ZIP Code",
  "state.required": "Please select a state",
  //"contactNumber.required": "Please enter a phone number",
  //"contactNumber.min": "Please enter a valid phone number",
  "portInNumber.required": "Please enter port in number",
  "portInNumber.min": "Please enter a valid number",
  "localId.required": "Please enter local ID",
  "networkId.required": "Please enter network ID",
  "billingAccountNumber.required": "Please enter BAN",
  "authorizationName.required": "Please enter authorization name",
  "npaNxx.required": "Please enter NPA-NXX",
  "fromLine.required": "Please enter the From Line",
  "subject.required": "Please enter a subject",
  "description.required": "Please enter a description",
  "agentId.required": "Please select an assignee",
  "category.required": "Please select a category",
  "status.required": "Please select a status",
  "priority.required": "Please select a priority",
  "serviceArea.required": "Please enter Service Area",
  //"billingAccountPassword.required": "Please enter PIN/Password",
  "commercialName.required": "Please add a commercial name",
  "commercialPrice.required": "Please add a commercial price",
  "commercialDescription.required": "Please add a commercial description",
  "note.required": "Please add your note",
  "channelName.required": "Please add channel name",
  "channelDescription.required": "Please add channel description",
  "channelEmail.required": "Please add an email address",
  "channelEmail.email": "Please enter a valid email address",
  "channelPhoneNumber.required": "Please add a phone number",
  "channel.required": "Please select a channel",

  "promoName.required": "Please enter a name",
  "promoStatus.required": "Please select a status",
  "promoType.required": "Please select a promo type",
  "promoAmount.required": "Please enter a discount amount",
  "promoAmount.above": "Please enter a valid discount amount",
  "startDate.required": "Please enter a start date",
  "expiryDate.required": "Please enter an expiry date",
  "promoCode.required": "Please enter a promo code",

  "nickname.required": "Please enter a nickname",
} as any;

export const labels = {
  firstName: "First Name",
  lastName: "Last Name",
  role: "Role",
  email: "Email Address",
  password: "Password",
  newPassword: "Password",
  confirmPassword: "Confirm Password",
  address: "Street Address",
  streetNumber: "Street Number",
  streetDirection: "Street Direction",
  streetName: "Street Name",
  city: "City",
  state: "State",
  zipCode: "Zip Code",
  contactNumber: "Contact Number",
  imei: "IMEI",
  portInNumber: "Port In Number",
  iccid: "ICCID",
  mdn: "New Subscriber Number",
  localId: "Local ID",
  networkId: "Network ID",
  billingAccountNumber: "Billing Account Number",
  authorizationName: "Authorization Name",
  npaNxx: "NPA-NXX ",
  fromLine: "From Line",
  subject: "Subject",
  description: "Description",
  agentId: "Assignee",
  category: "Category",
  status: "Status",
  priority: "Priority",
  dueDate: "Due Date",
  serviceArea: "Service Area",
  billingAccountPassword: "PIN/Password",
  commercialName: "Commercial Name",
  commercialPrice: "Commercial Price",
  commercialDescription: "Commercial Description",
  note: "Note",
  channelName: "Name",
  channelDescription: "Description",
  channelEmail: "Email Address",
  channelPhoneNumber: "Phone Number",
  nickname: "Device Nickname",
} as any;

export const placeholders = {
  email: "Enter user's email address",
  password: "Enter your password",
  confirmPassword: "Confirm your password",
  newPassword: "New Password",
  confirmNewPassword: "Confirm New Password",
  name: "Name",
  imei: "Network recognized device IMEI",
  portInNumber: "Enter port in number",
  sim: "SIM Number",
  iccid: "ICCID",
  mdn: "MDN",
  firstName: "Enter first name",
  lastName: "Enter last name",
  role: "Role",
  address: "Street Address",
  streetNumber: "Street Number",
  streetDirection: "Street Direction",
  streetName: "Street Name",
  city: "City",
  state: "State",
  zipCode: "Zip Code",
  contactNumber: "Contact Number",
  localId: "Local ID",
  networkId: "Network ID",
  billingAccountNumber: "Billing Account Number",
  authorizationName: "Authorization Name",
  npaNxx: "NPA-NXX ",
  fromLine: "From Line",
  subject: "Subject",
  description: "Description",
  agentId: "Assignee",
  category: "Category",
  status: "Status",
  priority: "Priority",
  dueDate: "Due Date",
  serviceArea: "Service Area",
  billingAccountPassword: "PIN/Password",
  commercialName: "Commercial Name",
  commercialPrice: "Commercial Price",
  commercialDescription: "Commercial Description",
  note: "Add your note here",
  channelName: "Name",
  channelDescription: "Description",
  channelEmail: "Email Address",
  channelPhoneNumber: "Phone Number",
  nickname: "Device Nickname",
} as any;

export const getRules = (fields: any) => {
  let obj = {} as any;
  fields.forEach((field: string) => {
    if (field in rules) obj[field] = rules[field];
  });
  return obj;
};

export const getMessages = (fields: any) => {
  let messageKeys = Object.keys(messages);
  messageKeys = messageKeys.filter((key: any) =>
    fields.some((field: any) => key.includes(field)),
  );
  let obj = {} as any;
  messageKeys.forEach((key: string) => {
    obj[key] = messages[key];
  });
  return obj;
};

export const createStateObject = (arr: any, init = "") => {
  let obj = {} as any;
  let err = {} as any;
  for (let i = 0; i < arr.length; i++) {
    if (arr[i] === "status") {
      obj[arr[i]] = 2;
    } else if (
      arr[i] === "commercialName" ||
      arr[i] === "commercialPrice" ||
      arr[i] === "commercialDescription"
    ) {
      obj[arr[i]] = " ";
    } else {
      obj[arr[i]] = init;
    }
    err[arr[i]] = "";
  }
  let objWithErrors = {
    ...obj,
    errors: err,
  };
  return objWithErrors;
};

export const clearInput = (prop: any, setState: any) => {
  setState((prev: any) => {
    return {
      ...prev,
      [prop]: "",
      errors: {
        ...prev.errors,
        [prop]: "",
      },
    };
  });
};

export const displayErrors = (errors: any, setState: any) => {
  let formattedErrors = {} as any;
  errors.forEach((error: any) => {
    formattedErrors[error.field] = error.message;
  });
  setState((prev: any) => {
    return {
      ...prev,
      errors: formattedErrors,
    };
  });
};

// Handles change in card information inputs
export const handleInputChange = (
  prop: string,
  e: any,
  state: any,
  setState: any,
  subscriber = false,
) => {
  const set = (value: any) => {
    setState((prev: any) => {
      return {
        ...prev,
        [prop]: value,
        errors: {
          ...prev.errors,
          [prop]: "",
        },
      };
    });
  };

  if (
    prop === "name" ||
    prop === "firstName" ||
    prop === "lastName" ||
    prop === "city"
  ) {
    if (subscriber) {
      if (checkAddress(e.target.value) && e.target.value.length <= 60) {
        set(e.target.value);
      }
    } else {
      if (checkName(e.target.value) && e.target.value.length <= 60) {
        set(e.target.value);
      }
    }
  } else if (prop === "imei") {
    if (
      (e.target.value.length <= 17 && isNumeric(e.target.value)) ||
      e.target.value === ""
    ) {
      set(e.target.value);
    }
  } else if (prop === "zipCode") {
    if (
      (e.target.value.length <= 5 && isNumeric(e.target.value)) ||
      e.target.value === ""
    ) {
      set(e.target.value);
    }
  } else if (prop === "iccid" || prop === "mdn" || prop === "ban") {
    if (
      (e.target.value.length <= 22 && isNumeric(e.target.value)) ||
      e.target.value === ""
    ) {
      set(e.target.value);
    }
  } else if (prop === "streetNumber") {
    if (e.target.value.length <= 15 && checkPostcode(e.target.value)) {
      set(e.target.value);
    }
  } else if (prop === "contactNumber") {
    if (e.target.value.length <= 30 && checkPhone(e.target.value)) {
      set(e.target.value);
    }
  } else if (prop === "portInNumber") {
    if (
      e.target.value.length <= 15 &&
      (isNumeric(e.target.value) || e.target.value === "")
    ) {
      set(e.target.value);
    }
  } else if (
    prop === "commercialDescription" ||
    prop === "description" ||
    prop === "note"
  ) {
    if (e.target.value.length <= 5000) {
      set(e.target.value);
    }
  } else if (prop === "commercialPrice") {
    let decimals = e.target.value.split(".");
    if (decimals.length === 1 || decimals[1].length <= 2) {
      set(e.target.value);
    }
  } else {
    if (e.target.value.length <= 60) {
      set(e.target.value);
    }
  }
};
