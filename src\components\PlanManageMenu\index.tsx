// Subscription manage trigger button and menu

import styles from "./plan-manage-menu.module.scss";
import {
  GearIcon,
  TrashIcon,
  PlugsConnectedIcon,
  Plus,
  Receipt,
  Pause,
  Swap,
  Sparkle,
  Play,
  X,
  PaperPlaneTilt,
  MagicWand,
  PencilSimpleLine,
} from "../svgs";
import {
  ControlledMenu,
  MenuItem as MenuItemPrimitive,
  useMenuState,
} from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { useMemo, useRef } from "react";
import { getStatusesForPlan, PlanStatus } from "../utils/planUtils";
import { uniq } from "underscore";
import { usePlanManageContext } from "../PlanManageContext";

const MenuItem = ({
  icon,
  label,
  onClick,
}: {
  icon: React.ReactNode | undefined;
  label: string | undefined;
  onClick: (() => void) | undefined;
}) => {
  return (
    <MenuItemPrimitive className={styles.menuItem} onClick={onClick}>
      {icon}
      <span>{label}</span>
    </MenuItemPrimitive>
  );
};

type PlanManageMenuProps = {
  plan: any;
  // Allows to set the current plan when the menu is opened. Only used in the grid view
  setCurrentPlan?: (plan: any) => void;
};

const PlanManageMenu = ({ plan, setCurrentPlan }: PlanManageMenuProps) => {
  const ref = useRef(null);
  const [menuProps, toggleMenu] = useMenuState({ transition: true });
  const allMenuItems = useAllMenuItems();
  const planStatuses = getStatusesForPlan(plan);
  const menuItemsForStatuses = useMemo(() => {
    return planStatuses.flatMap((status) => menuItemsForStatus(status, plan));
  }, [planStatuses]);

  // Extra filter logic for menu status. 
  // Include any additional logic based on plan data here
  const filterMenuItems = (menuItems: MenuItemKeys[]) => {
    return menuItems.filter((item) => {
      // Hide 'add iccid' and 'remove subscription' for rejected plans
      if (plan.subscriberNumberStatus === "Rejected") {
        if (item === "add-iccid" || item === "remove-subscription") {
          return false;
        }
      }
      return true;
    });
  };

  const renderMenuItems = useMemo(() => {
    return uniq(filterMenuItems(menuItemsForStatuses)).map((item) => {
      const menuItem = allMenuItems.find((menuItem) => menuItem.key === item);
      return (
        <MenuItem
          key={item}
          icon={menuItem?.icon}
          label={menuItem?.label}
          onClick={menuItem?.onClick}
        />
      );
    });
  }, [menuItemsForStatuses, allMenuItems]);

  if (!renderMenuItems.length) {
    return null;
  }

  return (
    <div className={styles.container}>
      <button
        ref={ref}
        className={styles.manageBtn}
        onClick={() => {
          setCurrentPlan?.(plan);
          toggleMenu(true);
        }}
      >
        <GearIcon />
        <span>Manage</span>
      </button>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        align="end"
        viewScroll="auto"
        position="auto"
        className="subscription-manage-menu"
      >
        <div
          className={styles.menuItemsGrid}
          style={{
            display: "grid",
            gridTemplateColumns: `repeat(${Math.ceil(renderMenuItems.length / 6)}, 1fr)`,
          }}
        >
          {renderMenuItems}
        </div>
      </ControlledMenu>
    </div>
  );
};

export default PlanManageMenu;

const useAllMenuItems = () => {
  const { setCurrentModal } = usePlanManageContext();

  const menuItems = [
    {
      key: "activate-subscription" as const,
      icon: <PlugsConnectedIcon />,
      label: "Activate Subscription",
      onClick: () => {
        setCurrentModal("confirm-activate-saved");
      },
    },
    {
      key: "resume-subscription" as const,
      icon: <Play />,
      label: "Resume Subscription",
      onClick: () => {
        setCurrentModal("confirm-resume");
      },
    },
    {
      key: "add-device-nickname" as const,
      icon: <Plus />,
      label: "Add MDN Nickname",
      onClick: () => {
        setCurrentModal("change-device-nickname");
      },
    },
    {
      key: "remove-subscription" as const,
      icon: <TrashIcon />,
      label: "Remove Subscription",
      onClick: () => {
        setCurrentModal("confirm-delete-saved");
      },
    },
    {
      key: "edit-subscription-details" as const,
      icon: <PencilSimpleLine />,
      label: "Edit Subscription Details",
      onClick: () => {
        setCurrentModal("edit-subscription");
      },
    },
    {
      key: "add-topups" as const,
      icon: <Plus />,
      label: "Add Topups",
      onClick: () => {
        setCurrentModal("add-top-up");
      },
    },
    {
      key: "change-imei" as const,
      icon: <PencilSimpleLine />,
      label: "Change IMEI",
      onClick: () => {
        setCurrentModal("change-imei");
      },
    },
    // todo: not implemented yet
    // {
    //   key: "change-address-and-mdn" as const,
    //   icon: <PencilSimpleLine />,
    //   label: "Change Address and MDN",
    //   onClick: () => {
    //     setCurrentModal("edit-subscription");
    //   },
    // },
    // todo: not implemented yet
    // {
    //   key: "change-billing-cycle" as const,
    //   icon: <Receipt />,
    //   label: "Change Billing Cycle",
    //   onClick: () => {
    //     setCurrentModal("change-bill-cycle");
    //   },
    // },
    {
      key: "suspend-subscription" as const,
      icon: <Pause />,
      label: "Suspend Subscription",
      onClick: () => {
        setCurrentModal("confirm-suspend");
      },
    },
    {
      key: "change-subscription" as const,
      icon: <Swap />,
      label: "Change Subscription",
      onClick: () => {
        setCurrentModal("change-subscription");
      },
    },
    {
      key: "change-mdn" as const,
      icon: <PencilSimpleLine />,
      label: "Change MDN",
      onClick: () => {
        setCurrentModal("change-mdn");
      },
    },
    {
      key: "change-iccid" as const,
      icon: <PencilSimpleLine />,
      label: "Change ICCID",
      onClick: () => {
        setCurrentModal("change-iccid");
      },
    },
    // Features are better managed in the configurations tab
    // as they are not returned in the subscription data for the
    // grid view
    // {
    //   key: "edit-features" as const,
    //   icon: <Sparkle />,
    //   label: "Edit Features",
    //   onClick: () => {
    //     setCurrentModal("manage-features");
    //     setSubscription(subscription);
    //   },
    // },
    {
      key: "cancel-subscription" as const,
      icon: <TrashIcon />,
      label: "Cancel Subscription",
      onClick: () => {
        setCurrentModal("confirm-cancel");
      },
    },
    {
      key: "suspend" as const,
      icon: <Pause />,
      label: "Suspend",
      onClick: () => {
        setCurrentModal("confirm-suspend");
      },
    },
    {
      key: "add-portin-details" as const,
      icon: <Plus />,
      label: "Add Portin Details",
      onClick: () => {
        setCurrentModal("add-missing-port");
      },
    },
    {
      key: "add-iccid" as const,
      icon: <Plus />,
      label: "Add ICCID",
      onClick: () => {
        setCurrentModal("add-iccid");
      },
    },
    {
      key: "resubmit" as const,
      icon: <PaperPlaneTilt />,
      label: "Resubmit",
      onClick: () => {
        setCurrentModal("resubmit-activation-no-update");
      },
    },
    {
      key: "update-and-resubmit" as const,
      icon: <Plus />,
      label: "Update & Resubmit",
      onClick: () => {
        setCurrentModal("resubmit-activation");
      },
    },
    {
      key: "restore-subscription" as const,
      icon: <MagicWand />,
      label: "Restore Subscription",
      onClick: () => {
        setCurrentModal("confirm-restore");
      },
    },
    {
      key: "change-device-nickname" as const,
      icon: <Plus />,
      label: "Change Device Nickname",
      onClick: () => {
        setCurrentModal("nickname");
      },
    },
    {
      key: "update-port" as const,
      icon: <PencilSimpleLine />,
      label: "Edit Porting Details",
      onClick: () => {
        setCurrentModal("update-port");
      },
    },
  ];

  return menuItems;
};

type MenuItems = ReturnType<typeof useAllMenuItems>;
type MenuItemKeys = MenuItems[number]["key"];

type MenuItemsForStatus = Record<PlanStatus, MenuItemKeys[]>;

const menuItemsForStatus = (status: PlanStatus, plan: any): MenuItemKeys[] => {
  return (
    {
      active: [
        "edit-subscription-details",
        "change-imei",
        "add-topups",
        "change-subscription",
        "change-mdn",
        "change-device-nickname",
        "change-iccid",
        // todo: not implemented yet
        // "change-address-and-mdn",
        // todo: not implemented yet
        // "change-billing-cycle",
        "suspend-subscription",
        "cancel-subscription",
      ],
      "ready-to-activate-temp-subscription": [
        "activate-subscription",
        "add-device-nickname",
        "remove-subscription",
      ],
      "ready-to-activate-temp-portin": [
        "activate-subscription",
        "update-port",
        "change-device-nickname",
        "remove-subscription",
      ],
      suspended: [
        "restore-subscription",
        "change-device-nickname",
        "cancel-subscription",
      ],
      "iccid-required": [
        "add-iccid",
        ...(plan?.type === "tempPortingProcess"
          ? ["update-port" as const]
          : []),
        "change-device-nickname",
        "remove-subscription",
      ],
      "cancelled-resume-available": [
        "resume-subscription",
        "change-device-nickname",
      ],
      "cancelled-resume-not-available": [],
      "pending-activation": [],
      "pending-ban-change": [],
      rejected: ["resubmit", "update-and-resubmit", "change-device-nickname"],
      "missing-porting-details": [
        "add-portin-details",
        "change-device-nickname",
      ],
      "port-out": [],
      "port-in-in-progress": [],
      "port-in-completed": [],
      "port-in-failed": [],
      "port-in-confirmed": [],
      "port-in-cancelled": [],
      "port-in-incomplete": [],
    } satisfies MenuItemsForStatus
  )[status] as MenuItemKeys[];
};
