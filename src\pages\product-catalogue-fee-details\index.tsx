import { Link, useParams } from "react-router-dom";
import SearchSection from "../../components/SearchSection";
import styles from "../../styles/product-catalogue-fee-detail.module.scss";
import Button from "../../components/Button";
import { ArrowRight, Delete, Pencil } from "../../components/svgs";
import Pagination from "../../components/Pagination";
import { useCallback, useEffect, useState } from "react";
import EditFeeModal from "../../components/EditFeeModal";
import DeleteFeeModal from "../../components/DeleteFeeModal";
import { ApiGet } from "../api/api";
import { useDispatch } from "react-redux";
import Spinner from "../../components/Spinner";
import { formatDateWithTime } from "../../components/utils/formatDate";
import TrueFalseStatus from "../../components/TrueFalseStatus";

const ProductCatalogueFeeDetails = () => {
  const logs: any = [];
  const { id: feeId, mvnoId } = useParams();

  const [detailsLoading, setDetailsLoading] = useState(true);

  const [fetchError, setFetchError] = useState(false);

  const [feeDetails, setFeeDetails] = useState(null as any);

  const dispatch = useDispatch();

  const fetchFeeDetails = useCallback((id: string) => {
    setDetailsLoading(true);
    setFetchError(false);
    ApiGet(`/fees/${id}`)
      .then((response) => {
        setFeeDetails(response.data);
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "Something went wrong",
            message: error.response?.data?.message || "Unknown error.",
          },
        });
        setFetchError(true);
      })
      .finally(() => {
        setDetailsLoading(false);
      });
  }, []);

  useEffect(() => {
    if (feeId) {
      fetchFeeDetails(feeId);
    }
  }, [feeId]);

  const [showEditFeeModal, setShowEditFeeModal] = useState(false);

  const [showDeleteFeeModal, setShowDeleteFeeModal] = useState(false);

  return (
    <div className={styles.main}>
      <SearchSection
        data={logs}
        setFilteredData={() => {}}
        setQueryDisplay={() => {}}
        placeholder="Search"
        id="fee-activity-log-search"
        setCurrentPage={() => {}}
      />
      <EditFeeModal
        show={showEditFeeModal}
        setShow={setShowEditFeeModal}
        fee={feeDetails}
        refreshData={fetchFeeDetails}
      />
      <DeleteFeeModal
        show={showDeleteFeeModal}
        setShow={setShowDeleteFeeModal}
        fee={feeDetails}
      />

      {detailsLoading ? (
        <div
          style={{
            display: "flex",
            height: "50vh",
            alignItems: "center",
            justifyContent: "center",
          }}
          className={`${styles.container} ${styles.loadingView}`}
        >
          <Spinner />
        </div>
      ) : fetchError ? (
        <div className={styles.loadingError}>
          <img src="/error_robot.svg" />
          <div>
            <h3>An unexpected error occurred</h3>
            <p>Please try again later</p>
          </div>
        </div>
      ) : (
        <>
          <div className={styles.header}>
            <div className={styles.breadcrumbs}>
              <Link to={`/${mvnoId}/product-catalogue?tab=fees`}>
                Product Catalogue - Fees{" "}
              </Link>
              <span>/ {feeDetails?.name}</span>
            </div>

            <div className={styles.actionButtons}>
              <Button
                color="danger"
                onClick={() => setShowDeleteFeeModal(true)}
              >
                <Delete />
                Delete
              </Button>
              <Button onClick={() => setShowEditFeeModal(true)}>
                <Pencil />
                Edit Details
              </Button>
            </div>
          </div>
          <div className={styles.container}>
            <div className={styles.detailTop}>
              <TrueFalseStatus status={feeDetails?.status as any} />
              <div className={styles.detailDates}>
                <p>Created on: {formatDateWithTime(feeDetails?.dateCreated)}</p>
                {feeDetails?.dateModified && (
                  <p>
                    Last Modified:{" "}
                    {formatDateWithTime(feeDetails?.dateModified)}
                  </p>
                )}
              </div>
            </div>

            <h1>{feeDetails?.name}</h1>
            <div className={styles.detailItemCardsContainer}>
              <div className={styles.detailItemCard}>
                <span>Fee Type</span>
                <h3>{feeDetails?.type}</h3>
              </div>
              <div className={styles.detailItemCard}>
                <span>Amount</span>
                <h3>${feeDetails?.amount}</h3>
              </div>
            </div>
          </div>

          <div className={`${styles.container} ${styles.activityLog}`}>
            <h2>Activity Log</h2>
            <table>
              <thead>
                <tr>
                  <th>Email</th>
                  <th>Timestamp</th>
                  <th>Change</th>
                </tr>
              </thead>
              <tbody>
                {feeDetails?.activityLog?.length ? (
                  <>
                    {feeDetails?.activityLog.map((log: any) => (
                      <tr key={log.id}>
                        <td>{log.email}</td>
                        <td>{log.timestamp}</td>
                        <td className={styles.logTransitionCell}>
                          <span>{log.field}</span>
                          <div className={styles.logValueTransitionContainer}>
                            <p>{log.oldValue}</p>
                            <ArrowRight width={16} height={16} />
                            <p>{log.newValue}</p>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </>
                ) : (
                  <tr style={{ background: "none" }}>
                    <td colSpan={100}>
                      <div className={styles.noneFound}>
                        <img src="/none_found.svg" />
                        <h3>No activity log for this fee</h3>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>

            {feeDetails?.activityLog?.length ? (
              <div className={`${styles.paginationContainer} pagination`}>
                <Pagination
                  numberOfPages={5}
                  currentPage={1}
                  setCurrentPage={() => {}}
                />
              </div>
            ) : null}
          </div>
        </>
      )}
    </div>
  );
};

export default ProductCatalogueFeeDetails;
