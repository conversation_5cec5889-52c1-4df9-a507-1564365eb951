import { useNavigate, useSearchParams } from "react-router-dom";
import FirstPasswordChange from "../../components/FirstPasswordChange";
import styles from "../../styles/login.module.scss";
import { useEffect } from "react";

const NewLogin = () => {
  const navigate = useNavigate();

  const handleBackToLogin = () => {
    navigate("/login");
  };

  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    let authToken = searchParams.get("auth");
    if (authToken) {
      if (authToken[authToken.length - 1] === "/") {
        authToken = authToken.slice(0, authToken.length - 1);
      }
      localStorage.removeItem("crmUserInfo");
      localStorage.setItem("token", authToken);
    }
  }, [searchParams]);

  return (
    <div className={styles.container}>
      <div className={styles.main}>
        <div className={styles.logos}>
          <img src="/Logo.png" className={styles.logo} />
          <img src="/AireSpring-Logo.png" width="142" />
        </div>
        <div className={styles.formContainer}>
          <FirstPasswordChange proceed={handleBackToLogin} />
        </div>
      </div>
      <img src="/Login_Graphic.svg" className={styles.graphic} />
    </div>
  );
};

export default NewLogin;
