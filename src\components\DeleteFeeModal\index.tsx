import Modal from "../Modal";
import styles from "./delete-fee-modal.module.scss";
import { Delete } from "../svgs";
import { ApiDelete } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { formatDateWithTime } from "../utils/formatDate";

const DeleteFeeModal = ({ show, setShow, fee }: any) => {
  const dispatch = useDispatch();

  const navigate = useNavigate();

  const [deleting, setDeleting] = useState(false);

  const deleteFee = () => {
    setDeleting(true);
    ApiDelete(`/fees/${fee.id}`, {})
      .then((response) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: "Fee deleted successfully",
            message: response.data.message,
          },
        });
        setShow(false);
        navigate(-1);
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "Something went wrong",
            message: error.response?.data?.message || "Unknown error.",
          },
        });
      })
      .finally(() => {
        setDeleting(false);
      });
  };

  return (
    <Modal
      saveButton={
        <>
          <Delete />
          Yes, Delete fee
        </>
      }
      image="/delete_user_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={deleteFee}
      loading={deleting}
      close={() => {
        setShow(false);
      }}
      fullsize
      title="Delete Fee"
    >
      <div className={`${styles.feeDetailsContainer}`}>
        <table>
          <tbody>
            <tr>
              <td>Fee Type</td>
              <td>{fee?.type}</td>
            </tr>
            <tr>
              <td>Fee Name</td>
              <td>{fee?.name}</td>
            </tr>
            <tr>
              <td>Amount</td>
              <td>${fee?.amount}</td>
            </tr>
            <tr>
              <td>Created on</td>
              <td>{formatDateWithTime(fee?.dateCreated)}</td>
            </tr>
            <tr>
              <td>Status</td>
              <td>{fee?.status ? "Active" : "Inactive"}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </Modal>
  );
};

export default DeleteFeeModal;
