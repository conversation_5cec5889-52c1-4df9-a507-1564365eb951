import { useEffect, useState } from "react";
import TableControl from "../TableControl";
import UserSkeleton from "../UserSkeleton";
import styles from "./payment-history.module.scss";
import { populateOrders } from "../utils/dummySubscriptions";
import { padArrayToLength } from "../utils/padArray";
import formatDate from "../utils/formatDate";
import { getCardNetworkImage } from "../utils/getCardNetworkImage";

const PaymentHistory = () => {
  const [initialLoading, setInitialLoading] = useState(false);

  const [transactions, setTransactions] = useState([]);

  const [maxPage, setMaxPage] = useState(1);

  const [currentPage, setCurrentPage] = useState(1);

  const [perPage, setPerPage] = useState(15);

  const [queryDisplay, setQueryDisplay] = useState("");

  useEffect(() => {
    setTransactions(populateOrders(perPage));
  }, [currentPage, perPage]);

  return (
    <div className={styles.panel}>
      <div className={`${styles.tableContainer} table-scroll`}>
        <table>
          <thead>
            <tr>
              <th>Trans. ID</th>
              <th>Order ID</th>
              <th>Date</th>
              <th>Result</th>
              <th>Items</th>
              <th>Amount</th>
              <th>Card</th>
            </tr>
          </thead>
          <tbody>
            {!initialLoading ? (
              transactions.length !== 0 ? (
                padArrayToLength(transactions, perPage, null).map(
                  (subItem: any) => {
                    if (subItem === null) {
                      return (
                        <tr
                          style={{
                            visibility: "hidden",
                            pointerEvents: "none",
                          }}
                        ></tr>
                      );
                    } else {
                      return (
                        <tr
                          key={"user-row-" + subItem.mdn}
                          style={{ cursor: "pointer" }}
                        >
                          <td>{subItem.transactionId}</td>
                          <td>{subItem.orderId}</td>
                          <td>{formatDate(subItem.date)}</td>
                          <td>
                            <span
                              style={{
                                color: subItem?.result ? "#037B53" : "#EA3D5C",
                                fontWeight: 600,
                              }}
                            >
                              {subItem?.result ? "Success" : "Fail"}
                            </span>
                          </td>
                          <td>{subItem.product}</td>
                          <td>${subItem.amount.toFixed(2)}</td>
                          <td>
                            <div className={styles.cardInfoContainer}>
                              {getCardNetworkImage(subItem)} ••••{" "}
                              {subItem.last4Digits}
                            </div>
                          </td>
                        </tr>
                      );
                    }
                  },
                )
              ) : (
                <tr style={{ background: "none" }}>
                  <td colSpan={100}>
                    <div className={styles.noneFound}>
                      <img src="/none_found.svg" />
                      <h3>
                        We couldn't find anything matching
                        {queryDisplay ? <>"{queryDisplay}"</> : "."}
                      </h3>
                    </div>
                  </td>
                </tr>
              )
            ) : (
              Array.from({ length: perPage }, (v, i) => i).map((i) => (
                <UserSkeleton
                  key={"user-skeleton-" + i}
                  noOfStandard={7}
                  showActionBar
                />
              ))
            )}
          </tbody>
        </table>
      </div>
      <div className={styles.pagination}>
        <TableControl
          itemsPerPage={perPage}
          setItemsPerPage={setPerPage}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          numberOfPages={maxPage}
          label="transactions"
        />
      </div>
    </div>
  );
};

export default PaymentHistory;
