import { useState } from "react";
import styles from "./text-area.module.scss";
import { Fade, Collapse } from "@mui/material";

const TextArea = ({
  label,
  placeholder,
  value,
  onChange,
  error,
  onKeyDown,
  clear,
  disabled,
  readonly,
  white,
  id = null,
  autoHeight,
}: any) => {
  return (
    <form
      style={{ width: "100%" }}
      autoComplete="none"
      onKeyPress={(event: any) => {
        return event.keyCode != 13;
      }}
      onSubmit={(e) => {
        e.preventDefault();
      }}
    >
      <div className={styles.inputContainer}>
        <div className={styles.inputWrapper}>
          <textarea
            id={id}
            className={`modal-scroll ${styles.input} ${
              readonly && styles.readonly
            } ${error && styles.error} ${autoHeight && styles.autoHeight}`}
            value={value}
            placeholder={placeholder}
            onChange={(e: any) => {
              onChange(e);
            }}
            disabled={disabled || readonly}
          />
          <div
            className={`${styles.label} ${readonly && styles.readonly} ${
              value && styles.hasValue
            } ${error && styles.labelError} ${white && styles.white}`}
          >
            {label}
          </div>
          <img
            src="/input_clear.svg"
            className={styles.clearIcon}
            onMouseDown={(e) => {
              e.preventDefault();
            }}
            onClick={clear}
            style={{ right: 20 }}
          />
          <Fade in={error ? true : false}>
            <img
              src="/input_error.svg"
              className={styles.errorIcon}
              onMouseDown={(e) => {
                e.preventDefault();
              }}
              style={{ right: 20 }}
            />
          </Fade>
        </div>
        <Collapse in={error ? true : false}>
          <p className={styles.errorText} id={`${id}-error`}>
            {error || <br />}
          </p>
        </Collapse>
      </div>
    </form>
  );
};

export default TextArea;
