.toggleContainer {
  display: flex;
  border-radius: 9999px;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 400px;
  height: 50px;

  &.primary {
    background-color: #fcc9a5;

    .toggleOption {
      color: #000;

      &.selected {
        color: #fff;
      }
    }

    .selectedIndicator {
      background: #f47d27;
    }
  }

  &.secondary {
    background-color: #e0dcdc;

    .toggleOption {
      color: #000;

      &.selected {
        color: #fff;
      }
    }

    .selectedIndicator {
      background: #000000;
    }
  }
}

.toggleOption {
  flex: 1;
  border: none;
  background: transparent;
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
  position: relative;

  &:focus {
    outline: none;
  }

  .label {
    position: relative;
    z-index: 10;
  }

  .selectedIndicator {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    background: black;
    border-radius: 9999px;
    z-index: 0;
    box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.15);
  }
}
