import styles from "./status-badge.module.scss";

export const convertStatus = (str: string, flat: boolean) => {
  switch (str) {
    case "ICCIDREQUIRED":
      return "ICCID Required";
    case "BANCHANGE":
      return "BAN Change";
    case "READY":
      return "Ready";
    case "ICCIDANDPORTINREQUIRED":
      return flat ? (
        "ICCID & Port in Required"
      ) : (
        <>
          ICCID & Port in
          <br />
          Required
        </>
      );
    case "PORTINREQUIRED":
      return flat ? (
        "Port in Required"
      ) : (
        <>
          Port in
          <br />
          Required
        </>
      );
    default:
      return "";
  }
};

const OrderStatusBadge = ({ status, flat = false }: any) => {
  return (
    <div
      className={`${styles.main} ${
        convertStatus(status, flat) === "ICCID Required"
          ? styles.iccid
          : convertStatus(status, flat) === "Ready"
          ? styles.ready
          : convertStatus(status, flat) === "BAN Change"
          ? styles.banChange
          : status === "ICCIDANDPORTINREQUIRED"
          ? styles.iccidAndPortin
          : status === "PORTINREQUIRED"
          ? styles.portin
          : styles.default
      }`}
    >
      {convertStatus(status, flat)}
    </div>
  );
};

export default OrderStatusBadge;
