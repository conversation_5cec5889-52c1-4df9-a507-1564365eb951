@use "./theme.scss" as *;

.container {
  height: 100vh;
  display: flex;
  justify-content: space-between;
  background: $off-white;
}

.graphic {
  height: 100%;
}

.logos {
  display: flex;
  align-items: center;
}

.logo {
  width: 147px;
  margin-right: 33px;
}

.main {
  padding: 90px;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.formContainer {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.forgotPassword {
  width: 100%;
  display: flex;
  justify-content: center;
}

.form {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  h2 {
    font-size: 48px;
    font-weight: 700;
    line-height: 72px;
    margin: 0;
    margin-bottom: 50px;
    text-align: center;
  }
}
