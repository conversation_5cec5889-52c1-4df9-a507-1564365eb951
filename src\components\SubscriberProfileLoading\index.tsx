import styles from "./subscriber-profile-loading.module.scss";
import Shimmer from "../Shimmer";

const SubscriberProfileLoading = () => {
  return (
    <div className={styles.main}>
      {/* Subscriber Name */}
      <div
        className={styles.box}
        style={{ width: "240px", height: "30px", marginBottom: "14px" }}
      >
        <Shimmer />
      </div>
      <div className={styles.flex}>
        {/* Tabs */}
        <div
          className={styles.box}
          style={{ width: "146px", height: "47px", borderRadius: "23.5px" }}
        >
          <Shimmer />
        </div>
        <div
          className={styles.box}
          style={{ width: "110px", height: "21px", marginLeft: "24px" }}
        >
          <Shimmer />
        </div>

        {/* Add Subscription Button */}
        <div
          className={styles.box}
          style={{
            marginLeft: "auto",
            width: "220px",
            height: "50px",
            borderRadius: "25px",
          }}
        >
          <Shimmer />
        </div>
      </div>

      {/* Search, Sort, Filter Section */}
      {/* <div className={styles.flex} style={{ marginTop: "16px" }}>
        <div
          className={styles.box}
          style={{ width: "387px", height: "57px", borderRadius: "57px" }}
        >
          <Shimmer />
        </div>
        <div
          className={styles.box}
          style={{ marginLeft: "auto", width: "180px", height: "32px" }}
        >
          <Shimmer />
        </div>
        <div
          className={styles.box}
          style={{ marginLeft: "24px", width: "180px", height: "32px" }}
        >
          <Shimmer />
        </div>
      </div> */}

      {/* Subscriptions Grid */}
      <div className={styles.plansGrid}>
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className={styles.planTile}>
            <div className={styles.flex}>
              {/* Plan Status */}
              <div
                className={styles.box}
                style={{ width: "82px", height: "44px", borderRadius: "8px" }}
              >
                <Shimmer />
              </div>
              {/* Plan Manage Button */}
              <div
                className={styles.box}
                style={{
                  width: "132px",
                  height: "44px",
                  marginLeft: "auto",
                  borderRadius: "8px",
                }}
              >
                <Shimmer />
              </div>
            </div>

            {/* Plan Name */}
            <div
              className={styles.box}
              style={{ width: "80%", height: "20px", marginTop: "16px" }}
            >
              <Shimmer />
            </div>

            {/* MDN and Nickname */}
            <div className={styles.flex} style={{ marginTop: "8px" }}>
              <div
                className={styles.box}
                style={{ width: "132px", height: "16px" }}
              >
                <Shimmer />
              </div>
              <div
                className={styles.box}
                style={{ width: "112px", height: "16px", marginLeft: "auto" }}
              >
                <Shimmer />
              </div>
            </div>

            {/* Plan Properties */}
            <div className={styles.planProps} style={{ marginTop: "12px" }}>
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i}>
                  <div
                    className={styles.box}
                    style={{ width: "100px", height: "10px" }}
                  >
                    <Shimmer />
                  </div>
                  <div
                    className={styles.box}
                    style={{ width: "128px", height: "14px", marginTop: "4px" }}
                  >
                    <Shimmer />
                  </div>
                </div>
              ))}
            </div>

            {/* See Details Button */}
            <div
              className={styles.box}
              style={{
                width: "132px",
                height: "41px",
                marginTop: "16px",
                borderRadius: "16px",
              }}
            >
              <Shimmer />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SubscriberProfileLoading;
