import styles from "../../styles/channel-management.module.scss";
import { Pencil, Delete, Plus } from "../../components/svgs";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Button from "../../components/Button";
import Pagination from "../../components/Pagination";
import Tooltip from "../../components/Tooltip";
import UserSkeleton from "../../components/UserSkeleton";
import { padArrayToLength } from "../../components/utils/padArray";
import { ApiGet, ApiPatch, ApiPut } from "../api/api";
import { useNavigate, useParams } from "react-router-dom";
import RadioSelect from "../../components/RadioSelect";
import AddChannelModal from "../../components/AddChannelModal";
import EditChannelModal from "../../components/EditChannelModal";
import DeleteChannelModal from "../../components/DeleteChannelModal";
import TrueFalseStatus from "../../components/TrueFalseStatus";
import UserMenu from "../../components/UserMenu";

const ChannelManagement = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [initialLoading, setInitialLoading] = useState(true);

  const { mvnoId } = useParams();
  const { userInfo } = useSelector((state: any) => state);

  const [channels, setChannels] = useState([] as any);

  const repopulate = () => {
    setInitialLoading(true);
    ApiGet(`/channels/mvno/${mvnoId}`)
      .then((response) => {
        setInitialLoading(false);
        setChannels(response.data);
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  useEffect(repopulate, []);

  const [currentPage, setCurrentPage] = useState(1);
  const channelsPerPage = 10;

  /***********   Add Channel     ***********/

  const [showAddChannelModal, setShowAddChannelModal] = useState(false);

  const [activeChannel, setActiveChannel] = useState(null as any);

  /********       Delete Channel         **********/

  const [showDeleteChannelModal, setShowDeleteChannelModal] = useState(false);

  // Handles deleting channel
  const handleDeleteChannel = (channel: any) => {
    setActiveChannel(channel);
    setShowDeleteChannelModal(true);
  };

  /********       Edit Channel         **********/

  const [showEditChannelModal, setShowEditChannelModal] = useState(false);

  // Handles edit user
  const handleEditChannel = (channel: any) => {
    setActiveChannel(channel);
    setShowEditChannelModal(true);
  };

  const handleSelectChannel = (channel: any) => {
    navigate(`/${mvnoId}/channel-management/${channel.id}`);
  };

  const handleChangeStatus = (channel: any, newStatus: any) => {
    ApiPut(`/channels/${channel.id}/status`, {
      status: newStatus,
    })
      .then((response) => {
        repopulate();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <div className={styles.main}>
      <AddChannelModal
        show={showAddChannelModal}
        setShow={setShowAddChannelModal}
        repopulate={repopulate}
      />
      <EditChannelModal
        show={showEditChannelModal}
        setShow={setShowEditChannelModal}
        channel={activeChannel}
        repopulate={repopulate}
      />
      <DeleteChannelModal
        show={showDeleteChannelModal}
        setShow={setShowDeleteChannelModal}
        channel={activeChannel}
        repopulate={repopulate}
      />
      <div
        style={{ display: "flex", justifyContent: "flex-end", width: "100%" }}
      >
        <UserMenu />
      </div>
      <div className={styles.titleBar}>
        <h3>Channel Management</h3>
        <Button
          style={{ marginLeft: "auto" }}
          onClick={() => {
            setShowAddChannelModal(true);
          }}
        >
          <Plus />
          Add Channel
        </Button>
      </div>
      <div className={styles.usersPanel}>
        <div className={`${styles.tableContainer} table-scroll select`}>
          <table>
            <thead>
              <tr>
                <th>Channel Name</th>
                <th>Description</th>
                {/*<th>Email</th>
                <th>Phone Number</th>*/}
                <th>Subscribers</th>
                <th>Subscriptions</th>
                {userInfo.roleName !== "Agent" && <th>Status</th>}
                <th></th>
              </tr>
            </thead>
            <tbody>
              {!initialLoading ? (
                channels.length !== 0 ? (
                  padArrayToLength(
                    channels.slice(
                      (currentPage - 1) * channelsPerPage,
                      currentPage * channelsPerPage,
                    ),
                    channelsPerPage,
                    null,
                  ).map((singleChannel: any) => {
                    if (singleChannel === null) {
                      return (
                        <tr
                          style={{
                            visibility: "hidden",
                            pointerEvents: "none",
                          }}
                        ></tr>
                      );
                    } else {
                      return (
                        <tr
                          onClick={() => {
                            if (
                              userInfo?.roleName === "Agent" &&
                              !singleChannel.status
                            ) {
                              dispatch({
                                type: "notify",
                                payload: {
                                  error: true,
                                  message: "This channel has been deactivated",
                                },
                              });
                            } else {
                              handleSelectChannel(singleChannel);
                            }
                          }}
                          key={"user-row-" + singleChannel.id}
                          style={{
                            cursor:
                              userInfo?.roleName === "Agent" &&
                              !singleChannel.status
                                ? "auto"
                                : "pointer",
                            opacity:
                              userInfo?.roleName === "Agent" &&
                              !singleChannel.status
                                ? 0.5
                                : 1,
                          }}
                        >
                          <td>{singleChannel.name}</td>
                          <td
                            style={{
                              maxWidth: 165,
                              overflow: "hidden",
                              textOverflow: "ellipsis",
                            }}
                          >
                            {singleChannel.description}
                          </td>
                          {/*<td>{singleChannel.email}</td>
                          <td>{singleChannel.phoneNumber}</td>*/}
                          <td>{singleChannel.subscribers}</td>
                          <td>{singleChannel.subscriptions}</td>
                          {userInfo.roleName !== "Agent" ? (
                            <>
                              <td>
                                <div
                                  style={{
                                    display: "flex",
                                    justifyContent: "flex-start",
                                  }}
                                >
                                  <RadioSelect
                                    label={
                                      <TrueFalseStatus
                                        status={singleChannel.status}
                                      />
                                    }
                                    options={[
                                      {
                                        label: <TrueFalseStatus status />,
                                        key: true,
                                      },
                                      {
                                        label: <TrueFalseStatus />,
                                        key: false,
                                      },
                                    ]}
                                    selected={singleChannel.status}
                                    onChange={(e: any) => {
                                      handleChangeStatus(singleChannel, e);
                                    }}
                                  />
                                </div>
                              </td>
                              <td>
                                <div className={styles.actionPanel}>
                                  <Tooltip
                                    show
                                    text="Edit Channel"
                                    style={{ marginRight: 8 }}
                                  >
                                    <button
                                      className={styles.actionButton}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleEditChannel(singleChannel);
                                      }}
                                    >
                                      <Pencil />
                                    </button>
                                  </Tooltip>
                                  <Tooltip
                                    show
                                    text="Delete Channel"
                                    style={
                                      singleChannel.userId === userInfo.mid
                                        ? {
                                            pointerEvents: "none",
                                            visibility: "hidden",
                                          }
                                        : {}
                                    }
                                  >
                                    <button
                                      className={styles.actionButton}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleDeleteChannel(singleChannel);
                                      }}
                                    >
                                      <Delete />
                                    </button>
                                  </Tooltip>
                                </div>
                              </td>
                            </>
                          ) : (
                            <td />
                          )}
                        </tr>
                      );
                    }
                  })
                ) : (
                  <tr style={{ background: "none" }}>
                    <td colSpan={100}>
                      <div className={styles.noneFound}>
                        <img src="/none_found.svg" />
                        <h3>We couldn't find any channels</h3>
                      </div>
                    </td>
                  </tr>
                )
              ) : (
                Array.from({ length: channelsPerPage }, (v, i) => i).map(
                  (i) => (
                    <UserSkeleton
                      key={"user-skeleton-" + i}
                      noOfStandard={5}
                      showActionBar
                    />
                  ),
                )
              )}
            </tbody>
          </table>
        </div>
        <div className={styles.pagination}>
          <Pagination
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            numberOfPages={Math.ceil(channels.length / channelsPerPage)}
          />
        </div>
      </div>
    </div>
  );
};

export default ChannelManagement;
