import { useEffect, useState } from "react";
import styles from "../../styles/user-management.module.scss";
import { useParams } from "react-router-dom";
import MultiSelect, { MultiSelectOption } from "../../components/MultiSelect";
import DatePicker from "../../components/DatePicker";
import RemoveFiltersBar from "../../components/RemoveFiltersBar";
import UserSkeleton from "../../components/UserSkeleton";
import { padArrayToLength } from "../../components/utils/padArray";
import { formatDateWithTime } from "../../components/utils/formatDate";
import { ApiGet } from "../api/api";
import qs from "qs";
import TableControl from "../../components/TableControl";
import SearchBar from "../../components/SearchBar";
import { formatDateForApi } from "../../components/utils/dateHelpers";

const ActivityLogs = () => {
  const [initialLoading, setInitialLoading] = useState(true);

  const fetchActivityLogs = () => {
    const params = {
      currentPage: currentPage - 1,
      pageSize: itemsPerPage,
      search: searchQuery || undefined,
      startDate: formatDateForApi(filters.time.start),
      endDate: formatDateForApi(filters.time.end),
      userNames: filters.userName,
      emails: filters.userEmail,
      actions: filters.action,
      ipAddresses: filters.ipAddress,
    };

    const stringifiedQueryParams = qs.stringify(params, {
      indices: false,
      skipNulls: true,
    });

    setInitialLoading(true);
    ApiGet(`/activity/mvno?${stringifiedQueryParams}`)
      .then((response) => {
        setLogs(response.data.activity);
        setTotalPages(response.data.totalPages);
        setInitialLoading(false);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const [searchInputValue, setSearchInputValue] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  const [logs, setLogs] = useState([] as any);

  const emptyFilters = {
    userName: [],
    userEmail: [],
    time: {
      start: null,
      end: null,
    },
    action: [],
    ipAddress: [],
  } as any;

  const [filters, setFilters] = useState(emptyFilters);

  const resetFilters = () => {
    setFilters(emptyFilters);
  };

  useEffect(() => {
    fetchActivityLogs();
  }, [currentPage]);

  useEffect(() => {
    setCurrentPage(1);
    fetchActivityLogs();
  }, [filters, itemsPerPage, searchQuery]);

  const handleSearch = () => {
    setSearchQuery(searchInputValue);
    setCurrentPage(1);
  };


  const [multiSelectFilterOptionsLoading, setMultiSelectFilterOptionsLoading] = useState({
    userName: false,
    userEmail: false,
    action: false,
    ipAddress: false,
  });
  const [multiSelectFilterOptions, setMultiSelectFilterOptions] = useState<Record<string, MultiSelectOption[]>>({
    userName: [],
    userEmail: [],
    action: [],
    ipAddress: [],
  });

  const fetchMultiSelectFilterOptions = (filterKey: string) => {
    setMultiSelectFilterOptionsLoading({ ...multiSelectFilterOptionsLoading, [filterKey]: true });
    ApiGet(`/activity/mvno/filters?field=${filterKey}`)
      .then((response) => {
        setMultiSelectFilterOptionsLoading({ ...multiSelectFilterOptionsLoading, [filterKey]: false });
        setMultiSelectFilterOptions({ ...multiSelectFilterOptions, [filterKey]: response.data.map((item: any) => ({ key: item, label: item })) });
      })
      .catch((error) => {
        setMultiSelectFilterOptionsLoading({ ...multiSelectFilterOptionsLoading, [filterKey]: false });
      });
  };

  return (
    <div className={styles.main}>
      <SearchBar
        placeholder="Search by Email Address, Name, IP Address"
        id="subscriber-search"
        onSubmit={handleSearch}
        query={searchInputValue}
        setQuery={setSearchInputValue}
      />
      <div className={styles.headingSection}>
        <div className={styles.logHeading}>
          <h2>Activity logs</h2>
        </div>
      </div>
      <div className={`${styles.products} ${styles.usersPanel} filter-cell`}>
        <div className={styles.filters}>
          <div className={styles.label}>Filters</div>
          <MultiSelect
            grid
            label="User"
            options={multiSelectFilterOptions.userName}
            optionsLoading={multiSelectFilterOptionsLoading.userName}
            onOpen={() => fetchMultiSelectFilterOptions("userName")}
            selected={filters.userName}
            setSelected={(state: any) => {
              setFilters({ ...filters, userName: state });
            }}
          />
          <MultiSelect
            twoColumnGrid
            label="Email Address"
            options={multiSelectFilterOptions.userEmail}
            optionsLoading={multiSelectFilterOptionsLoading.userEmail}
            onOpen={() => fetchMultiSelectFilterOptions("userEmail")}
            selected={filters.userEmail}
            setSelected={(state: any) => {
              setFilters({ ...filters, userEmail: state });
            }}
            search
            searchPlaceholder="Search Email Address"
          />
          <DatePicker
            label="Date"
            background
            masterFrom={filters.time.start}
            masterUntil={filters.time.end}
            onChange={(newFrom: Date, newUntil: Date) => {
              setFilters({
                ...filters,
                time: {
                  start: newFrom,
                  end: newUntil,
                },
              });
            }}
          />
          <MultiSelect
            label="Activity type"
            options={multiSelectFilterOptions.action}
            optionsLoading={multiSelectFilterOptionsLoading.action}
            onOpen={() => fetchMultiSelectFilterOptions("action")}
            selected={filters.action}
            setSelected={(state: any) => {
              setFilters({ ...filters, action: state });
            }}
          />
          <MultiSelect
            grid
            label="IP"
            options={multiSelectFilterOptions.ipAddress}
            optionsLoading={multiSelectFilterOptionsLoading.ipAddress}
            onOpen={() => fetchMultiSelectFilterOptions("ipAddress")}
            selected={filters.ipAddress}
            setSelected={(state) => {
              setFilters({ ...filters, ipAddress: state });
            }}
            search
            searchPlaceholder="Search IP"
          />
        </div>
        <RemoveFiltersBar
          filters={filters}
          setFilters={setFilters}
          resetFilters={resetFilters}
          grey
          group
        />
        <div className={`table-scroll ${styles.logs}`}>
          <table>
            <thead>
              <tr>
                <th>Date and time</th>
                <th>IP Address</th>
                <th>User</th>
                <th>Email</th>
                <th>Activity</th>
                {/*<th>Description</th>*/}
              </tr>
            </thead>
            <tbody>
              {!initialLoading ? (
                logs.length !== 0 ? (
                  padArrayToLength(logs, itemsPerPage, null).map(
                    (log: any, i: number) => {
                      if (log === null) {
                        return (
                          <tr
                            key={"row-filler-" + i}
                            style={{ background: "none" }}
                          ></tr>
                        );
                      } else {
                        return (
                          <tr key={"log-row-" + i}>
                            <td>{formatDateWithTime(log.time)}</td>
                            <td>{log.ipAddress}</td>
                            <td>{log.userName}</td>
                            <td>{log.userEmail}</td>
                            <td>{log.action}</td>
                            {/*<td>
                            {"extra" in log ? (
                              <div className={styles.graphic}>
                                {log.data.extra.old}
                                <ArrowRight />
                                {log.data.extra.new}
                              </div>
                            ) : (
                              "-"
                            )}
                            </td>*/}
                          </tr>
                        );
                      }
                    },
                  )
                ) : (
                  <tr style={{ background: "none" }}>
                    <td colSpan={100}>
                      <div className={styles.noneFound}>
                        <img src="/none_found.svg" />
                        <h3>
                          We couldn't find anything matching
                          {searchQuery ? <>" {searchQuery}"</> : "."}
                        </h3>
                      </div>
                    </td>
                  </tr>
                )
              ) : (
                Array.from({ length: itemsPerPage }, (v, i) => i).map((i) => (
                  <UserSkeleton key={"log-skeleton-" + i} noOfStandard={6} />
                ))
              )}
            </tbody>
          </table>
        </div>
        <div className={styles.pagination}>
          <TableControl
            itemsPerPage={itemsPerPage}
            setItemsPerPage={setItemsPerPage}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            numberOfPages={totalPages}
          />
        </div>
      </div>
    </div>
  );
};

export default ActivityLogs;
