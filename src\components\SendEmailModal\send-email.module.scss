@use "../../styles/theme.scss" as *;
@import "../../styles/mixins.module.scss";

@include animatedSelection;

.main {
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-right: 15px;
  h3{
    margin-bottom: 51px;
  }
}

// .contentContainer {
//   width: 100%;
//   display: flex;
//   flex-direction: column;
//   align-items: center;
// }

// .plansContainer {
//   display: grid;
//   // width: 100%;
//   grid-template-columns: 1fr 1fr 1fr;
//   grid-column-gap: 12px;
//   grid-row-gap: 12px;
//   align-items: center;
// }
