<svg width="700" height="902" viewBox="0 0 700 902" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_5_140)">
<g clip-path="url(#clip0_5_140)">
<path d="M60 24C60 10.7451 70.7452 0 84 0H700V902H84C70.7452 902 60 891.255 60 878V24Z" fill="#F8FCFF"/>
<g clip-path="url(#clip1_5_140)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M686.562 796.374H395.137V167.81H686.562V796.374Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M651.924 796.374H420.077V195.541H651.924V796.374Z" fill="#CCE3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.728 732.7H504.273V588.848H567.728V732.7Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.728 398.371H504.273V254.519H567.728V398.371Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M567.728 565.535H504.273V421.682H567.728V565.535Z" fill="#DCEEFF"/>
<path d="M630.201 456.239H587.54" stroke="#B6D4FF" stroke-width="14"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M395.137 796.374H385.67V167.81H395.137V796.374Z" fill="#B6D4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M661.392 796.374H651.925V195.541H661.392V796.374Z" fill="#B6D4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M632.438 506.531C637.925 506.531 642.371 502.081 642.371 496.592V451.227C642.371 445.737 637.925 441.287 632.438 441.287C626.952 441.287 622.505 445.737 622.505 451.227V496.592C622.505 502.081 626.952 506.531 632.438 506.531Z" fill="#B6D4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M385.67 146.046C385.67 65.8854 450.606 0.90155 530.71 0.90155C610.814 0.90155 675.75 65.8854 675.75 146.046H385.67Z" fill="#B6D4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M396.482 145.145C396.482 64.9839 461.419 -0.000610352 541.522 -0.000610352C621.626 -0.000610352 686.562 64.9839 686.562 145.145H396.482Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M396.482 146.046C396.482 65.8854 461.419 0.90155 541.522 0.90155C621.626 0.90155 686.562 65.8854 686.562 146.046H396.482Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M434.513 121.71H661.151C649.411 67.1596 603.135 26.4472 547.832 26.4472C492.528 26.4472 446.253 67.1596 434.513 121.71Z" fill="#B6D4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M422.8 121.71H649.438C637.698 67.1596 591.422 26.4472 536.119 26.4472C480.815 26.4472 434.54 67.1596 422.8 121.71Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M85.7642 290.4H13.3506L3.57162 192.642H75.9851L85.7642 290.4Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M85.7642 290.4H13.3506L3.57162 192.642H75.9851L85.7642 290.4Z" stroke="#DCEEFF" stroke-width="5"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M89.6727 290.4H15.912L6.13227 192.642H79.8937L89.6727 290.4Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M89.6727 290.4H15.912L6.13227 192.642H79.8937L89.6727 290.4Z" stroke="#DCEEFF" stroke-width="5"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M75.5452 277.492H27.4569L20.2603 205.55H68.3486L75.5452 277.492Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M195.321 264.132C195.321 269.316 191.121 273.52 185.939 273.52H101.531V254.743H185.939C191.121 254.743 195.321 258.946 195.321 264.132Z" fill="#CCE3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M138.549 264.132C138.549 267.133 136.118 269.566 133.118 269.566H101.758C101.633 269.566 101.531 269.465 101.531 269.341V258.922C101.531 258.797 101.633 258.696 101.758 258.696H133.118C136.118 258.696 138.549 261.13 138.549 264.132Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M207.102 273.52V292.296H114.971C109.79 292.296 105.589 288.093 105.589 282.908C105.589 277.723 109.79 273.52 114.971 273.52H207.102Z" fill="#CCE3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M207.102 277.526V288.291H169.221C166.251 288.291 163.842 285.881 163.842 282.908C163.842 279.935 166.251 277.526 169.221 277.526H207.102Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M237.013 304.77H-80.5826V291.419H237.013V304.77Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M159.606 304.77L134.804 343.708V304.77H159.606Z" fill="#B6D4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M173.331 304.77L148.529 343.708H134.804L159.606 304.77H173.331Z" fill="#CCE3FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M234.793 507.815C234.793 507.815 177.249 634.915 170.378 773.106C170.378 773.106 202.585 633.066 234.793 507.815Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M131.834 605.49C131.834 605.49 173.252 606.568 179.558 744.759C179.558 744.759 161.883 640.461 131.834 605.49Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M174.128 466.396C174.128 466.396 198.306 612.604 174.508 761.379C174.508 761.379 175.922 605.993 174.128 466.396Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M206.743 800.503H146.945C146.496 800.503 146.049 800.495 145.603 800.479C118.337 799.459 103.167 764.945 118.609 740.045L132.308 717.959H221.38L235.078 740.045C250.521 764.945 235.351 799.459 208.085 800.479C207.639 800.495 207.192 800.503 206.743 800.503Z" fill="#ECF6FF"/>
<path d="M112.43 768.757H241.258" stroke="#B6D4FF" stroke-width="9"/>
<path d="M115.42 747.411H238.683" stroke="#B6D4FF" stroke-width="9"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M237.013 95.2141H-80.5818V81.8636H237.013V95.2141Z" fill="#DCEEFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M159.605 95.2139L134.804 134.152V95.2139H159.605Z" fill="#B6D4FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M173.331 95.2139L148.53 134.152H134.804L159.605 95.2139H173.331Z" fill="#CCE3FF"/>
<path d="M337.978 709.349L347.746 799.952" stroke="#19141E" stroke-width="23" stroke-linecap="round"/>
<path d="M414.376 709.349L424.143 799.952" stroke="#19141E" stroke-width="23" stroke-linecap="round"/>
<path d="M351.857 546.927L338.341 697.025" stroke="#19141E" stroke-width="23" stroke-linecap="round"/>
<path d="M421.612 545.79C421.588 541.02 417.73 537.172 412.994 537.196C408.258 537.22 404.439 541.106 404.462 545.876L405.208 695.593C405.232 700.363 409.09 704.211 413.826 704.187C418.561 704.163 422.381 700.277 422.357 695.507L421.612 545.79Z" fill="#19141E"/>
<path d="M382.781 879.178L380.923 861.985L370.007 760.7C369.47 755.721 365.033 752.123 360.083 752.664L321.877 756.847C316.933 757.388 313.362 761.857 313.898 766.836L324.24 862.766C310.864 865.861 300.887 877.922 300.88 892.335V892.35V901.482H385.188H464.112L463.128 892.335L459.847 861.985L448.931 760.7C448.394 755.721 443.958 752.123 439.007 752.664L400.801 756.847C395.857 757.388 392.286 761.857 392.823 766.836L403.165 862.766C394.163 864.848 386.702 870.991 382.781 879.178ZM421.681 559.82C427.475 555.49 431.256 548.561 431.282 540.778C431.326 527.687 420.728 516.94 407.731 516.897L354.306 516.716C341.309 516.672 330.639 527.347 330.596 540.437C330.552 553.528 341.151 564.275 354.147 564.319L404.555 564.489L404.462 545.876C404.439 541.106 408.258 537.22 412.994 537.196C417.73 537.172 421.588 541.02 421.612 545.79L421.681 559.82ZM394.55 701.705C394.55 713.632 404.149 723.3 415.989 723.3C427.829 723.3 437.428 713.632 437.428 701.705C437.428 689.779 427.829 680.111 415.989 680.111C404.149 680.111 394.55 689.779 394.55 701.705ZM315.714 701.705C315.714 713.632 325.314 723.3 337.153 723.3C348.994 723.3 358.593 713.632 358.593 701.705C358.593 689.779 348.994 680.111 337.153 680.111C325.314 680.111 315.714 689.779 315.714 701.705Z" fill="#6CA1FF"/>
<mask id="mask0_5_140" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="300" y="516" width="165" height="386">
<path d="M382.781 879.178L380.923 861.985L370.007 760.7C369.47 755.721 365.033 752.123 360.083 752.664L321.877 756.847C316.933 757.388 313.362 761.857 313.898 766.836L324.24 862.766C310.864 865.861 300.887 877.922 300.88 892.335V892.35V901.482H385.188H464.112L463.128 892.335L459.847 861.985L448.931 760.7C448.394 755.721 443.958 752.123 439.007 752.664L400.801 756.847C395.857 757.388 392.286 761.857 392.823 766.836L403.165 862.766C394.163 864.848 386.702 870.991 382.781 879.178ZM421.681 559.82C427.475 555.49 431.256 548.561 431.282 540.778C431.326 527.687 420.728 516.94 407.731 516.897L354.306 516.716C341.309 516.672 330.639 527.347 330.596 540.437C330.552 553.528 341.151 564.275 354.147 564.319L404.555 564.489L404.462 545.876C404.439 541.106 408.258 537.22 412.994 537.196C417.73 537.172 421.588 541.02 421.612 545.79L421.681 559.82ZM394.55 701.705C394.55 713.632 404.149 723.3 415.989 723.3C427.829 723.3 437.428 713.632 437.428 701.705C437.428 689.779 427.829 680.111 415.989 680.111C404.149 680.111 394.55 689.779 394.55 701.705ZM315.714 701.705C315.714 713.632 325.314 723.3 337.153 723.3C348.994 723.3 358.593 713.632 358.593 701.705C358.593 689.779 348.994 680.111 337.153 680.111C325.314 680.111 315.714 689.779 315.714 701.705Z" fill="#F6B852"/>
</mask>
<g mask="url(#mask0_5_140)">
<path d="M300.503 515.965H464.112V902H300.503V515.965Z" fill="#F47D27"/>
</g>
<path opacity="0.397182" fill-rule="evenodd" clip-rule="evenodd" d="M379.861 892.267C379.864 887.553 380.933 883.09 382.839 879.111L380.98 861.918L370.064 760.633C369.527 755.653 365.091 752.056 360.14 752.597L321.934 756.78C316.99 757.321 313.419 761.789 313.956 766.769L324.298 862.699C310.921 865.793 300.945 877.855 300.937 892.267V901.415H464.112L463.128 892.267H379.861Z" fill="black"/>
<path opacity="0.401204" fill-rule="evenodd" clip-rule="evenodd" d="M315.727 701.662C315.727 713.589 325.326 723.257 337.166 723.257C349.007 723.257 358.606 713.589 358.606 701.662C358.606 689.736 349.007 680.067 337.166 680.067C325.326 680.067 315.727 689.736 315.727 701.662Z" fill="black"/>
<path opacity="0.596656" fill-rule="evenodd" clip-rule="evenodd" d="M430.151 773.051C430.151 776.496 432.924 779.288 436.344 779.288C439.764 779.288 442.537 776.496 442.537 773.051C442.537 769.606 439.764 766.813 436.344 766.813C432.924 766.813 430.151 769.606 430.151 773.051ZM432.34 793.392C432.34 796.837 435.112 799.631 438.533 799.631C441.953 799.631 444.726 796.837 444.726 793.392C444.726 789.947 441.953 787.155 438.533 787.155C435.112 787.155 432.34 789.947 432.34 793.392Z" fill="black"/>
<path opacity="0.396019" fill-rule="evenodd" clip-rule="evenodd" d="M436.633 870.401C436.633 874.933 440.281 878.607 444.78 878.607C449.28 878.607 452.928 874.933 452.928 870.401C452.928 865.869 449.28 862.195 444.78 862.195C440.281 862.195 436.633 865.869 436.633 870.401Z" fill="black"/>
<path d="M330.947 500.664L297.3 573.373" stroke="#19141E" stroke-width="16" stroke-linecap="round"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M396.718 516.716H370.623V495.687H396.718V516.716Z" fill="#19141E"/>
<path d="M457.421 580.9L450.204 501.631" stroke="#19141E" stroke-width="16" stroke-linecap="round"/>
<path d="M290.351 604.437L298.749 611.94C299.133 611.509 299.74 610.783 300.496 609.8C301.732 608.192 302.965 606.412 304.121 604.493C307.243 599.313 309.273 594.07 309.627 588.864C310.104 581.863 307.413 575.639 301.269 571.153C295.121 566.664 288.711 566.277 283.227 569.813C279.316 572.334 276.302 576.515 273.823 581.897C272.571 584.615 271.534 587.485 270.69 590.349C270.395 591.348 270.145 592.28 269.936 593.122C269.807 593.644 269.718 594.033 269.668 594.268L280.681 596.605C280.705 596.49 280.766 596.224 280.864 595.829C281.034 595.143 281.242 594.371 281.488 593.536C282.188 591.159 283.045 588.787 284.046 586.613C285.711 583 287.579 580.407 289.323 579.284C290.773 578.349 292.137 578.431 294.634 580.254C297.554 582.386 298.617 584.846 298.396 588.098C298.183 591.218 296.761 594.893 294.481 598.675C293.559 600.205 292.565 601.641 291.574 602.929C291.231 603.375 290.917 603.767 290.639 604.1C290.484 604.285 290.385 604.399 290.351 604.437ZM429.367 496.615L429.884 497.639C434.5 506.769 445.739 510.461 454.863 505.843L456.4 505.065C465.523 500.446 469.212 489.198 464.597 480.068L438.487 428.412C434.975 421.462 427.623 417.663 420.289 418.282C416.823 416.861 413.035 416.076 409.073 416.076H358.269C351.13 416.076 344.556 418.623 339.411 422.854C332.626 424.359 326.933 429.625 325.268 436.82L312.211 493.215C309.904 503.182 316.165 513.226 326.125 515.536L327.804 515.925C337.291 518.124 346.847 512.545 349.729 503.396C352.437 504.213 355.304 504.652 358.269 504.652H409.073C416.894 504.652 424.037 501.595 429.367 496.615ZM468.89 609.623L480.128 610.287C480.136 610.159 480.145 609.958 480.154 609.689C480.296 605.241 479.895 599.922 478.456 594.669C475.402 583.513 468.354 576.291 456.802 576.746C450.297 577.003 445.442 579.719 442.846 584.551C440.824 588.315 440.416 592.835 441.127 597.953C441.571 601.141 442.441 604.453 443.61 607.797C444.203 609.491 444.836 611.088 445.472 612.548C445.861 613.441 446.172 614.106 446.367 614.5L456.45 609.49C456.43 609.449 456.376 609.336 456.292 609.158C456.144 608.84 455.975 608.468 455.792 608.048C455.262 606.831 454.731 605.49 454.235 604.075C453.294 601.383 452.606 598.765 452.277 596.4C451.435 590.342 452.593 588.187 457.246 588.003C462.646 587.79 465.78 591.001 467.599 597.646C468.212 599.885 468.597 602.334 468.786 604.842C468.917 606.585 468.94 608.128 468.902 609.329C468.897 609.494 468.892 609.594 468.89 609.623Z" fill="#6CA1FF"/>
<mask id="mask1_5_140" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="269" y="416" width="212" height="199">
<path d="M290.351 604.437L298.749 611.94C299.133 611.509 299.74 610.783 300.496 609.8C301.732 608.192 302.965 606.412 304.121 604.493C307.243 599.313 309.273 594.07 309.627 588.864C310.104 581.863 307.413 575.639 301.269 571.153C295.121 566.664 288.711 566.277 283.227 569.813C279.316 572.334 276.302 576.515 273.823 581.897C272.571 584.615 271.534 587.485 270.69 590.349C270.395 591.348 270.145 592.28 269.936 593.122C269.807 593.644 269.718 594.033 269.668 594.268L280.681 596.605C280.705 596.49 280.766 596.224 280.864 595.829C281.034 595.143 281.242 594.371 281.488 593.536C282.188 591.159 283.045 588.787 284.046 586.613C285.711 583 287.579 580.407 289.323 579.284C290.773 578.349 292.137 578.431 294.634 580.254C297.554 582.386 298.617 584.846 298.396 588.098C298.183 591.218 296.761 594.893 294.481 598.675C293.559 600.205 292.565 601.641 291.574 602.929C291.231 603.375 290.917 603.767 290.639 604.1C290.484 604.285 290.385 604.399 290.351 604.437ZM429.367 496.615L429.884 497.639C434.5 506.769 445.739 510.461 454.863 505.843L456.4 505.065C465.523 500.446 469.212 489.198 464.597 480.068L438.487 428.412C434.975 421.462 427.623 417.663 420.289 418.282C416.823 416.861 413.035 416.076 409.073 416.076H358.269C351.13 416.076 344.556 418.623 339.411 422.854C332.626 424.359 326.933 429.625 325.268 436.82L312.211 493.215C309.904 503.182 316.165 513.226 326.125 515.536L327.804 515.925C337.291 518.124 346.847 512.545 349.729 503.396C352.437 504.213 355.304 504.652 358.269 504.652H409.073C416.894 504.652 424.037 501.595 429.367 496.615ZM468.89 609.623L480.128 610.287C480.136 610.159 480.145 609.958 480.154 609.689C480.296 605.241 479.895 599.922 478.456 594.669C475.402 583.513 468.354 576.291 456.802 576.746C450.297 577.003 445.442 579.719 442.846 584.551C440.824 588.315 440.416 592.835 441.127 597.953C441.571 601.141 442.441 604.453 443.61 607.797C444.203 609.491 444.836 611.088 445.472 612.548C445.861 613.441 446.172 614.106 446.367 614.5L456.45 609.49C456.43 609.449 456.376 609.336 456.292 609.158C456.144 608.84 455.975 608.468 455.792 608.048C455.262 606.831 454.731 605.49 454.235 604.075C453.294 601.383 452.606 598.765 452.277 596.4C451.435 590.342 452.593 588.187 457.246 588.003C462.646 587.79 465.78 591.001 467.599 597.646C468.212 599.885 468.597 602.334 468.786 604.842C468.917 606.585 468.94 608.128 468.902 609.329C468.897 609.494 468.892 609.594 468.89 609.623Z" fill="#F6B852"/>
</mask>
<g mask="url(#mask1_5_140)">
<path d="M270.035 414.574H481.676V624.115H270.035V414.574Z" fill="#F47D27"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M383.093 475.621H358.154C353.2 475.621 349.148 471.565 349.148 466.608V451.973C349.148 447.016 353.2 442.96 358.154 442.96H383.093C388.047 442.96 392.099 447.016 392.099 451.973V466.608C392.099 471.565 388.047 475.621 383.093 475.621Z" fill="#19141E"/>
<g opacity="0.399182">
<path d="M290.482 604.034L298.88 611.537C299.264 611.106 299.87 610.38 300.626 609.397C301.863 607.789 303.095 606.009 304.251 604.09C307.373 598.91 309.403 593.667 309.758 588.461C310.234 581.46 307.543 575.236 301.4 570.751C295.251 566.261 288.842 565.874 283.357 569.41C279.446 571.931 276.433 576.112 273.953 581.494C272.701 584.212 271.665 587.082 270.82 589.946C270.526 590.945 270.275 591.877 270.067 592.719C269.937 593.241 269.848 593.63 269.799 593.865L280.811 596.202C280.836 596.087 280.896 595.821 280.994 595.426C281.164 594.74 281.372 593.968 281.618 593.133C282.319 590.756 283.175 588.384 284.177 586.21C285.841 582.597 287.71 580.004 289.453 578.881C290.903 577.946 292.268 578.028 294.764 579.851C297.684 581.983 298.748 584.443 298.526 587.695C298.314 590.815 296.891 594.49 294.611 598.272C293.689 599.802 292.695 601.238 291.704 602.526C291.362 602.972 291.047 603.364 290.769 603.697C290.615 603.882 290.516 603.996 290.482 604.034ZM337.951 496.706C338.72 499.751 337.472 503.069 334.655 504.797C331.186 506.925 326.65 505.835 324.524 502.363C322.398 498.891 323.487 494.352 326.956 492.224C329.037 490.948 331.501 490.83 333.599 491.688C330.284 486.884 328.338 481.07 328.338 474.824V445.869C328.338 436.617 332.607 428.315 339.27 422.836C332.485 424.341 326.792 429.606 325.127 436.801L312.07 493.196C309.763 503.163 316.024 513.208 325.984 515.517L327.663 515.906C337.149 518.106 346.706 512.526 349.588 503.378C345.204 502.056 341.237 499.744 337.951 496.706ZM438.566 428.525C433.952 419.394 422.711 415.703 413.588 420.321L412.051 421.099C402.928 425.717 399.238 436.966 403.853 446.096L429.963 497.752C434.578 506.882 445.818 510.574 454.941 505.956L456.479 505.178C465.602 500.559 469.291 489.311 464.676 480.181L438.566 428.525ZM439.462 489.508C438.286 485.61 440.489 481.495 444.384 480.318C448.279 479.14 452.391 481.344 453.568 485.243C454.745 489.141 452.541 493.255 448.646 494.433C444.751 495.61 440.639 493.406 439.462 489.508ZM414.961 441.034C413.784 437.136 415.988 433.022 419.883 431.844C423.778 430.666 427.89 432.871 429.067 436.769C430.243 440.667 428.04 444.781 424.145 445.96C420.25 447.137 416.138 444.932 414.961 441.034ZM468.885 609.696L480.123 610.361C480.13 610.233 480.139 610.031 480.148 609.762C480.29 605.315 479.889 599.996 478.451 594.742C475.396 583.586 468.348 576.364 456.797 576.82C450.291 577.076 445.436 579.793 442.84 584.625C440.819 588.388 440.41 592.909 441.122 598.026C441.565 601.215 442.435 604.527 443.604 607.87C444.197 609.564 444.83 611.162 445.466 612.622C445.855 613.515 446.166 614.18 446.362 614.573L456.445 609.563C456.424 609.523 456.37 609.41 456.287 609.232C456.138 608.914 455.97 608.542 455.786 608.121C455.257 606.905 454.725 605.564 454.23 604.148C453.288 601.456 452.601 598.838 452.272 596.474C451.429 590.415 452.587 588.26 457.24 588.077C462.641 587.864 465.774 591.074 467.593 597.719C468.206 599.958 468.591 602.408 468.78 604.915C468.912 606.658 468.935 608.201 468.896 609.403C468.891 609.568 468.886 609.668 468.885 609.696Z" fill="black"/>
</g>
<g opacity="0.601772">
<path fill-rule="evenodd" clip-rule="evenodd" d="M439.485 489.492C440.662 493.39 444.774 495.594 448.669 494.417C452.564 493.239 454.767 489.124 453.59 485.227C452.414 481.328 448.302 479.124 444.407 480.301C440.511 481.479 438.309 485.594 439.485 489.492ZM414.984 441.018C416.16 444.916 420.272 447.121 424.167 445.943C428.063 444.765 430.266 440.651 429.089 436.753C427.912 432.855 423.8 430.65 419.905 431.827C416.01 433.006 413.807 437.12 414.984 441.018ZM324.53 502.407C326.656 505.879 331.192 506.969 334.662 504.841C338.13 502.714 339.219 498.174 337.093 494.702C334.968 491.231 330.432 490.14 326.962 492.268C323.493 494.396 322.404 498.935 324.53 502.407Z" fill="black"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M345.19 374.25H335.171C331.186 374.25 327.926 370.987 327.926 366.999C327.926 363.012 331.186 359.749 335.171 359.749H345.19C349.176 359.749 352.436 363.012 352.436 366.999C352.436 370.987 349.176 374.25 345.19 374.25Z" fill="#19141E"/>
<path d="M384.368 387.953C382.856 383.856 377.118 381.437 371.553 382.551C365.988 383.664 362.703 387.889 364.215 391.986L371.845 412.656C373.358 416.753 373.866 417.928 380.733 417.928C383.641 417.928 388.074 417.928 390.581 417.928C394.168 417.928 395.27 415.608 391.998 408.624L384.368 387.953Z" fill="#19141E"/>
<path d="M375.785 407.469C392.962 407.469 407.017 393.404 407.017 376.214V359.344C407.017 342.154 392.962 328.09 375.785 328.09H372.783C355.605 328.09 341.55 342.154 341.55 359.344V376.214C341.55 393.404 355.605 407.469 372.783 407.469H375.785ZM356.098 310.469C356.098 306.162 352.609 302.67 348.304 302.67C344 302.67 340.511 306.162 340.511 310.469C340.511 314.776 344 318.268 348.304 318.268C352.609 318.268 356.098 314.776 356.098 310.469Z" fill="#6CA1FF"/>
<mask id="mask2_5_140" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="340" y="302" width="68" height="106">
<path d="M375.785 407.469C392.962 407.469 407.017 393.404 407.017 376.214V359.344C407.017 342.154 392.962 328.09 375.785 328.09H372.783C355.605 328.09 341.55 342.154 341.55 359.344V376.214C341.55 393.404 355.605 407.469 372.783 407.469H375.785ZM356.098 310.469C356.098 306.162 352.609 302.67 348.304 302.67C344 302.67 340.511 306.162 340.511 310.469C340.511 314.776 344 318.268 348.304 318.268C352.609 318.268 356.098 314.776 356.098 310.469Z" fill="#F6B852"/>
</mask>
<g mask="url(#mask2_5_140)">
<path d="M340.582 300.416H407.376V408.566H340.582V300.416Z" fill="#F47D27"/>
</g>
<path d="M351.436 317.613C356.546 322.066 359.455 326.99 360.332 333.697C360.493 334.931 361.624 335.8 362.857 335.638C364.09 335.477 364.958 334.346 364.797 333.112C363.786 325.388 360.475 319.666 354.886 314.649C354.06 315.949 352.859 316.988 351.436 317.613Z" fill="#19141E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M364.059 361.536C363.665 363.764 365.151 365.888 367.376 366.281C369.602 366.675 371.725 365.189 372.118 362.961C372.511 360.734 371.026 358.61 368.8 358.216C366.575 357.822 364.451 359.309 364.059 361.536Z" fill="#19141E"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M342.812 361.624C342.419 363.851 343.905 365.975 346.13 366.369C348.356 366.762 350.478 365.276 350.871 363.048C351.265 360.822 349.779 358.697 347.553 358.303C345.328 357.91 343.205 359.397 342.812 361.624Z" fill="#19141E"/>
<g opacity="0.403971">
<path fill-rule="evenodd" clip-rule="evenodd" d="M359.254 357.114L348.927 373.512L360.043 373.781L359.254 357.114Z" fill="black"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M411.388 372.698H401.667V358.197H411.388C415.374 358.197 418.634 361.46 418.634 365.448C418.634 369.436 415.374 372.698 411.388 372.698Z" fill="#19141E"/>
<path d="M361.354 381.432C366.272 382.265 370.869 379.455 371.368 376.509" stroke="#19141E" stroke-width="4"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_5_140" x="0" y="-40" width="720" height="982" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-20"/>
<feGaussianBlur stdDeviation="20"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.372059 0 0 0 0 0.825 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5_140"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5_140" result="shape"/>
</filter>
<clipPath id="clip0_5_140">
<path d="M60 24C60 10.7451 70.7452 0 84 0H700V902H84C70.7452 902 60 891.255 60 878V24Z" fill="white"/>
</clipPath>
<clipPath id="clip1_5_140">
<rect width="1125" height="902" fill="white" transform="translate(-157)"/>
</clipPath>
</defs>
</svg>
