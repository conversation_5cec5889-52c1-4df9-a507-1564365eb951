import { motion } from "framer-motion";
import styles from "./toggle-control.module.scss";
import { useMemo } from "react";

type Option = {
  label: string;
  value: string;
};

interface ToggleControlProps {
  options: Option[];
  selectedOption: string;
  onChange: (value: string) => void;
  variant?: "primary" | "secondary";
}

const ToggleControl = ({
  options,
  selectedOption,
  onChange,
  variant = "primary",
}: ToggleControlProps) => {
  // ensure unique layoutId per instance
  const layoutId = useMemo(() => `selectedIndicator-${Math.random()}`, []);

  return (
    <div className={`${styles.toggleContainer} ${styles[variant]}`}>
      {options.map((option, index) => (
        <button
          key={index}
          className={`${styles.toggleOption} ${selectedOption === option.value ? styles.selected : ""}`}
          onClick={() => onChange(option.value)}
          type="button"
        >
          <span className={styles.label}>{option.label}</span>
          {selectedOption === option.value && (
            <motion.div
              className={styles.selectedIndicator}
              layoutId={layoutId}
            />
          )}
        </button>
      ))}
    </div>
  );
};

export default ToggleControl;
