import { useCallback, useEffect, useState } from "react";
import { ApiGet } from "../api/api";
import { useDispatch } from "react-redux";
import styles from "../../styles/promotion-detail.module.scss";
import UserSkeleton from "../../components/UserSkeleton";
import Pagination from "../../components/Pagination";
import { formatDateWithTime } from "../../components/utils/formatDate";
import { Link, useParams } from "react-router-dom";

type UsageHistoryTabProps = {
  promoId: string;
};

const UsageHistoryTab = (props: UsageHistoryTabProps) => {
  const { mvnoId } = useParams();
  const { promoId } = props;
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);

  const [currentPageItems, setCurrentPageItems] = useState<
    Array<any> | undefined
  >();
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  const fetchUsageHistory = useCallback(
    (pageNo: number) => {
      setLoading(true);
      ApiGet(`/promotions/${promoId}/history?page=${pageNo - 1}`)
        .then((response) => {
          setCurrentPageItems(response.data.content);
          setTotalPages(response.data.totalPages);
        })
        .catch(() => {
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: "Error fetching promotions",
            },
          });
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [promoId],
  );

  useEffect(() => {
    fetchUsageHistory(1);
  }, []);

  return (
    <div className={`${styles.tableContainer} table-scroll`}>
      <table>
        <thead>
          <tr>
            <th>Name</th>
            <th>Email</th>
            <th>Timestamp</th>
            <th>Order Number</th>
          </tr>
        </thead>
        <tbody>
          {!loading ? (
            currentPageItems?.length !== 0 ? (
              currentPageItems?.map((item) => (
                <tr key={item.id}>
                  <td>{item.name}</td>
                  <td>{item.email}</td>
                  <td>{formatDateWithTime(new Date(item.timeStamp))}</td>
                  <td>
                    <Link to={`/${mvnoId}/orders/${item.orderNumber}`}>
                      {item.orderNumber}
                    </Link>{" "}
                  </td>
                </tr>
              ))
            ) : (
              <tr style={{ background: "none" }}>
                <td colSpan={100}>
                  <div className={styles.noneFound}>
                    <img src="/none_found.svg" />
                    <h3>No usage history</h3>
                  </div>
                </td>
              </tr>
            )
          ) : (
            Array.from({ length: 8 }, (v, i) => i).map((i) => (
              <UserSkeleton key={"user-skeleton-" + i} noOfStandard={8} />
            ))
          )}
        </tbody>
      </table>

      <div className={styles.pagination}>
        <Pagination
          currentPage={page}
          setCurrentPage={(currentPage: number) => {
            setPage(currentPage);
            fetchUsageHistory(currentPage);
          }}
          numberOfPages={totalPages}
        />
      </div>
    </div>
  );
};

export default UsageHistoryTab;
