@use "../../styles/theme.scss" as *;
@import "../../styles/mixins.module.scss";

@include animatedSelection;

.main {
  margin: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-right: 15px;
  h3 {
    margin-bottom: 51px;
  }
}

.modalContent {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 450px;
  justify-content: center;
  background-color: #f7f6f6;
  border-radius: 10px;
  padding: 24px;
  margin-bottom: 12px;

  p {
    margin: 0 0 12px 0;
  }
}

.details {
  color: var(--primary-palette-black, #000014);
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.subdetails {
  color: #4d4d4d;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.attachmentsContainer {
  padding: 0 24px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
}
.attachment {
  border-radius: 16px;
  background: #f7f6f6;
  display: flex;
  align-items: start;
  padding: 8px;
  gap: 16px;
  flex-direction: column;
}

.imageContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 116px;
  border-radius: 16px;
  border: 1px solid #b5b5b5;
  svg {
    vertical-align: middle;
  }
}

.filename {
  font-size: 14px;
  font-weight: 600;
  color: $black;
  margin-bottom: 6px;
}

.date,
.size {
  color: #4d4d4d;
  font-size: 12px;
  font-weight: 400;
}

.date {
  margin-bottom: 2px;
}
