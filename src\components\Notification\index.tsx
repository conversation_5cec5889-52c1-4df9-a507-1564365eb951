import styles from "./notification.module.scss";
import { Close } from "../svgs";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { motion } from "framer-motion";

const Notification = ({ id, error, message }: any) => {
  const dispatch = useDispatch();

  let timeout: any;

  const onClose = () => {
    if (timeout) clearTimeout(timeout);
    dispatch({
      type: "closeNotification",
      payload: id,
    });
  };

  useEffect(() => {
    timeout = setTimeout(() => {
      onClose();
    }, 5000);
  }, []);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: -150 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -150 }}
      transition={{
        type: "tween",
        ease: "easeInOut",
        duration: 0.3,
      }}
      key={id}
    >
      <div className={styles.main}>
        <div className={styles.imgContainer}>
          <img
            src={error ? "/error_robot.svg" : "/success_robot.svg"}
            className={styles.image}
          />
        </div>
        <p className={styles.message}>
          {message
            ? message
            : error
            ? "An unexpected error occurred"
            : "Success!"}
        </p>
        <button className={styles.close} onClick={onClose}>
          <Close />
        </button>
      </div>
    </motion.div>
  );
};

export default Notification;
