import Shimmer from "../Shimmer";
import styles from "./features-loading.module.scss";

const FeaturesLoading = () => {
  return (
    <>
      {Array.from({ length: 2 }).map(() => (
        <div className={styles.container}>
          <div className={styles.name}>
            <Shimmer />
          </div>
          <div className={styles.flex}>
            <div className={styles.data} style={{ width: 180 }}>
              <Shimmer />
            </div>
            <div className={styles.data} style={{ width: 100 }}>
              <Shimmer />
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default FeaturesLoading;
