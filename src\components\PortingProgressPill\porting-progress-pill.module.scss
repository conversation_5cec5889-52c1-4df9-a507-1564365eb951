@use "../../styles/theme.scss" as *;

.main {
  padding: 4px 12px;
  font-size: 14px;
  line-height: 20px;
  border-radius: 6px;
  width: auto;
  font-weight: 500 !important;
  align-items: center;
  display: inline-flex;
  svg {
    width: 20px;
    height: 20px;
    margin-left: 4px;
    vertical-align: middle;
  }
  &.status-0 {
    background-color: $inprogress;
    color: #000;
  }
  &.status-1 {
    background-color: $completed;
    color: #fdfcfb;
  }
  &.status-2 {
    background-color: $cancelled;
    color: #fdfcfb;
  }
  &.status-3 {
    background-color: #6361dc;
    color: #fdfcfb;
  }
  &.status-4 {
    background-color: $cancelled;
    color: #fdfcfb;
  }
  &.status-5 {
    background-color: $tbs;
    color: #000;
  }
  &.status-none {
    border: 1px solid black;
  }
}
