import { useEffect, useState } from "react";
import Modal from "../Modal";
import styles from "./add-features-modal.module.scss";
import Bolton from "../Bolton";

const AddFeaturesModal = ({
  show,
  setShow,
  addFeatures,
  base,
  adding,
  removing,
  throttle,
}: any) => {
  const [activeFeature, setActiveFeature] = useState([] as any);
  useEffect(() => {
    if (show) {
      setActiveFeature([] as any);
    }
  }, [show]);
  return (
    <Modal
      scroll
      image="/search_graphic.svg"
      saveButton="Add features to account"
      cancelButton="Cancel"
      show={show}
      proceed={() => {
        addFeatures(activeFeature);
      }}
      close={() => {
        setShow(false);
      }}
      fullSize
      title={<div style={{ textAlign: "start" }}>Throttles</div>}
      clearContainer
    >
      <div className={styles.plansMain}>
        <div className={styles.features}>
          {throttle
            ?.filter(
              (throttle: any) =>
                !adding.some((item: any) => item.code === throttle.code) &&
                (!base.some((item: any) => item.code === throttle.code) ||
                  removing.some((item: any) => item.code === throttle.code))
            )
            .map((feature: any) => (
              <Bolton
                feature={feature}
                activeFeature={activeFeature}
                setActiveFeature={setActiveFeature}
              />
            ))}
        </div>
      </div>
    </Modal>
  );
};

export default AddFeaturesModal;
