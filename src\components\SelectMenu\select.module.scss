@use "../../styles/theme.scss" as *;

.menuButton {
  height: 32px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &:hover {
    color: $light-primary;
  }
}

.box {
  height: 100%;
  display: flex;
  align-items: center;
}

.menuItem {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: $black;
  padding: 0;
  font-size: 14px;
  line-height: 24px;
  padding: 6px 12px;
  font-weight: 500;
  margin-bottom: 10px;
  &:last-of-type {
    margin-bottom: 0px;
  }
  svg {
    margin-right: 12px;
    vertical-align: middle;
  }
  &:hover {
    color: $orange;
    background: none;
  }
}
