import { useState } from "react";
import Button from "../Button";
import { Delete, Mountains } from "../svgs";
import { formatDate } from "../utils/dateHelpers";
import { getFileSize } from "../utils/getFileSize";
import styles from "./attachment.module.scss";
import DeleteFileModal from "../DeleteFileModal";

const Attachment = ({ data, mode }: any) => {
  const [state, setState] = useState(false);
  return (
    <>
      <DeleteFileModal show={state} setShow={setState} item={data} />
      <div className={styles.main}>
        <div style={{ display: "flex", gap: "8px" }}>
          <div className={styles.imageContainer}>
            <Mountains />
          </div>
          <div>
            <div className={styles.filename}>{data.name}</div>
            <div className={styles.date}>
              Upload date: {formatDate(data.date)}
            </div>
            <div className={styles.size}>Size: {getFileSize(data.size)}</div>
          </div>
        </div>
        {mode ? null : (
          <div className={styles.button} onClick={() => setState(true)}>
            <Delete />
          </div>
        )}
      </div>
    </>
  );
};

export default Attachment;
