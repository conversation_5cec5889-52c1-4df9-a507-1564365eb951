import Button from "../Button";
import { Delete } from "../svgs";
import { getFileSize } from "../utils/getFileSize";
import styles from "./file-preview.module.scss";

const FilePreview = ({ file, setFileStore }: any) => {
  const removeFile = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    setFileStore((prev: any) =>
      prev.filter((item: any) => item.id !== file.id)
    );
  };

  return (
    <div className={styles.main}>
      <div className={styles.fileName} title={file.name}>
        {file.name}
      </div>
      <div className={styles.fileSize}>{getFileSize(file.body.size)}</div>
      <button onClick={removeFile} className={styles.deleteButton}>
        <Delete />
      </button>
    </div>
  );
};

export default FilePreview;
