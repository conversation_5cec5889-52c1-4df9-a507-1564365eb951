@use "./theme.scss" as *;
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap");

* {
  box-sizing: border-box;
  font-family:
    $font,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    "Open Sans",
    "Helvetica Neue",
    sans-serif;
  margin: 0;
  padding: 0;
}

body,
html {
  padding: 0;
  margin: 0;
  min-width: 1050px;
  max-width: 100vw;
  overflow-x: hidden;
}

.chartjs-tooltip {
  background-color: red !important;
}

.tooltip-highlight {
  .highlight-search-result {
    color: $placeholder;
    font-weight: 700;
  }
}

.video-guide {
  .highlight-search-result {
    color: #fff;
  }
}

.highlight-search-result {
  color: $orange;
  font-weight: 600;
}

.slide-top-enter {
  opacity: 0;
  transform: translateY(-100px);
}
.slide-top-exit {
  opacity: 1;
  transform: translateY(0px);
}
.slide-top-enter-active {
  opacity: 1;
  transform: translateY(0px);
}
.slide-top-exit-active {
  opacity: 0;
  transform: translateY(-100px);
}
.slide-top-enter-active,
.slide-top-exit-active {
  transition: all 300ms ease;
}

.fade-enter {
  opacity: 0;
}
.fade-exit {
  opacity: 1;
}
.fade-enter-active {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
}
.fade-enter-active,
.fade-exit-active {
  transition: opacity 100ms;
}

.edit-menu .szh-menu-container,
.edit-menu .szh-menu {
  z-index: 9999;
}

/*.menu .szh-menu-container {
  position: relative !important;
}*/

.menu .szh-menu {
  box-shadow: none !important;
  border-radius: 0 !important;
  background: #fff !important;
  //left: -32px !important;
  //right: -40px !important;
  //left: initial !important;
  padding: 10px !important;
  display: flex;
  flex-direction: column;
  min-width: 202px !important;
}

.filter-cell .date-select .szh-menu {
  font-weight: initial !important;
  padding: 0px !important;
  box-shadow: 0px 6px 25px rgba(0, 0, 0, 0.15) !important;
  border-radius: 12px !important;
}

.single-date-select.important-date-style .szh-menu {
  padding: 0 !important;
  box-shadow: 0px 6px 25px rgba(0, 0, 0, 0.15) !important;
  border-radius: 12px !important;
  background: #fff !important;
}

.select .szh-menu {
  box-shadow: 0px 6px 25px rgba(0, 0, 0, 0.15) !important;
  border-radius: 12px !important;
  background: #fff !important;
  padding: 24px !important;
  display: flex;
  flex-direction: column;
  min-width: initial !important;
}
.select.coloured-select .szh-menu {
  //top: 16px !important;
  padding: 16px !important;
}

.select.coloured-select .szh-menu,
.select.coloured-select .szh-menu-container {
  width: 100% !important;
}

.multi-select .szh-menu {
  padding: 24px !important;
  //left: -30px !important;
  //top: 10px !important;
}

.normal-select-input .szh-menu-container {
  width: 350px !important;
  z-index: 10000 !important;
}

.normal-select-input .szh-menu {
  box-shadow: 0px 10px 20px rgba(46, 112, 229, 0.1) !important;
  border-radius: 0 0 8px 8px !important;
  background: #fff !important;
  padding: 22px 16px !important;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.normal-select-input.select-upwards .szh-menu {
  border-radius: 8px 8px 0 0 !important;
}

.__react_component_tooltip {
  border-radius: 4px !important;
  min-width: 146px;
  opacity: 0.3 !important;
  div {
    text-align: center;
  }
}

.notification-wrapper {
  position: fixed;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 99999;
  padding: 63px 25px;
  pointer-events: none;
  &.login-notification-wrapper {
    padding-left: 262px;
  }
}

.pagination {
  .Mui-disabled.MuiPaginationItem-previousNext {
    opacity: 0 !important;
  }
  .MuiPaginationItem-root {
    font-family: $font !important;
    margin: 0 4px !important;
    @media (max-width: 335px) {
      margin: 0 2px !important;
    }
    &.MuiButtonBase-root:hover {
      background: #bdbdbd;
    }
  }
  .Mui-selected.MuiPaginationItem-root {
    color: #fff !important;
    background: #000 !important;
    .MuiTouchRipple-root {
      display: none;
    }
  }
}

.modal-scroll {
  overflow-y: auto;
}

.modal-scroll::-webkit-scrollbar {
  width: 10px;
}

/* Track */
.modal-scroll::-webkit-scrollbar-track {
  background: none;
  border-radius: 9999px;
  background: $light-orange;
}

/* Handle */
.modal-scroll::-webkit-scrollbar-thumb {
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: $orange;
  transition: background-color 0.2s ease;
}

/* Handle on hover */
.table-scroll::-webkit-scrollbar-thumb:hover {
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: $dark-orange;
}

.table-scroll::-webkit-scrollbar {
  height: 12px;
}

/* Track */
.table-scroll::-webkit-scrollbar-track {
  background: $light-orange;
  border-radius: 9999px;
}

/* Handle */
.table-scroll::-webkit-scrollbar-thumb {
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: $orange;
  transition: background-color 0.2s ease;
}

/* Handle on hover */
.table-scroll::-webkit-scrollbar-thumb:hover {
  background-clip: padding-box;
  border-radius: 9999px;
  background-color: $dark-orange;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px #ffffff inset !important;
  &.input-disabled {
    -webkit-background-clip: text;
    -webkit-text-fill-color: $disabled-text;
  }
}

//material UI accordion
.css-1elwnq4-MuiPaper-root-MuiAccordion-root {
  position: initial !important;
  box-shadow: none !important;
}
