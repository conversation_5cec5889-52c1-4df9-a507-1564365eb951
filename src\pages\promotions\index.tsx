import styles from "../../styles/promotions.module.scss";
import SearchSection from "../../components/SearchSection";
import Button from "../../components/Button";
import { Pencil, Plus } from "../../components/svgs";
import { useCallback, useEffect, useState } from "react";
import UserSkeleton from "../../components/UserSkeleton";
import RadioSelect from "../../components/RadioSelect";
import TrueFalseStatus from "../../components/TrueFalseStatus";
import Tooltip from "../../components/Tooltip";
import AddPromotionModal from "../../components/AddPromotionModal";
import { useNavigate, useParams } from "react-router-dom";
import { ApiGet, ApiPut } from "../api/api";
import { useDispatch } from "react-redux";
import formatDate, {
  formatDateWithTime,
} from "../../components/utils/formatDate";
import EditPromotionModal from "../../components/EditPromotionModal";
import {
  formatPromoAmount,
  formatType,
} from "../../components/utils/promoUtils";
import Pagination from "../../components/Pagination";

const Promotions = () => {
  const { mvnoId } = useParams();
  const dispatch = useDispatch();

  const navigate = useNavigate();

  const [loadingPromotions, setLoadingPromotions] = useState(true);

  const [displayedPromotions, setDisplayedPromotions] = useState<
    Array<any> | undefined
  >();

  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  const fetchPromotions = useCallback(
    (pageNo: number) => {
      setLoadingPromotions(true);
      ApiGet(`/promotions/mvno/${mvnoId}?page=${pageNo - 1}`)
        .then((response) => {
          setDisplayedPromotions(response.data.content);
          setTotalPages(response.data.totalPages);
        })
        .catch(() => {
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: "Error fetching promotions",
            },
          });
        })
        .finally(() => {
          setLoadingPromotions(false);
        });
    },
    [mvnoId],
  );

  useEffect(() => {
    fetchPromotions(1);
  }, []);

  const [showAddPromotionModal, setShowAddPromotionModal] = useState(false);
  const [showEditPromotionModal, setShowEditPromotionModal] = useState(false);
  const [activelyEditedPromotion, setActivelyEditedPromotion] =
    useState<any>(undefined);

  const handleSwitchPromotionStatus = (id: string, newStatus: boolean) => {
    ApiPut(`/promotions/${id}`, {
      status: newStatus,
    })
      .then(() => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: "Success",
            message: "Status updated successfully",
          },
        });
        fetchPromotions(page);
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: "Error",
            message: error?.response?.data?.message,
          },
        });
      });
  };

  return (
    <>
      <AddPromotionModal
        show={showAddPromotionModal}
        setShow={setShowAddPromotionModal}
        mvnoId={mvnoId}
        refresh={() => fetchPromotions(page)}
      />
      <EditPromotionModal
        show={showEditPromotionModal}
        setShow={setShowEditPromotionModal}
        promoDetails={activelyEditedPromotion}
        refresh={() => fetchPromotions(page)}
      />

      <div className={styles.main}>
        <SearchSection
          // data={products}
          // filters={filters}
          setFilteredData={() => {}}
          setQueryDisplay={() => {}}
          placeholder="Search"
          id="promotions-search"
          setCurrentPage={() => {}}
        />

        <div className={styles.titleBar}>
          <h3>Promotions</h3>

          <Button
            onClick={() => {
              setShowAddPromotionModal(true);
            }}
          >
            <Plus />
            Create New
          </Button>
        </div>

        <div className={`${styles.tableContainer} table-scroll`}>
          <table>
            <thead>
              <tr>
                <th>Type</th>
                <th>Name</th>
                <th>Amount</th>
                <th>Times Used</th>
                <th>Promo Code</th>
                <th>Start Date</th>
                <th>Expiry Date</th>
                <th colSpan={3}>Status</th>
              </tr>
            </thead>
            <tbody>
              {!loadingPromotions ? (
                displayedPromotions?.length !== 0 ? (
                  displayedPromotions?.map((promo) => (
                    <tr
                      key={promo.id}
                      style={{ cursor: "pointer" }}
                      onClick={() => navigate("./" + promo.id)}
                    >
                      <td>{formatType(promo.type)}</td>
                      <td>{promo.name}</td>
                      <td>{formatPromoAmount(promo)}</td>
                      <td>{promo.timesUsed}</td>
                      <td>{promo.promoCode}</td>
                      <td>{formatDate(new Date(promo.startDate))}</td>
                      <td>{formatDateWithTime(new Date(promo.expiryDate))}</td>
                      <td>
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "flex-start",
                          }}
                        >
                          <RadioSelect
                            label={<TrueFalseStatus status={promo.status} />}
                            options={[
                              {
                                label: <TrueFalseStatus status />,
                                key: true,
                              },
                              {
                                label: <TrueFalseStatus />,
                                key: false,
                              },
                            ]}
                            selected={promo.status}
                            onChange={(e: any) =>
                              handleSwitchPromotionStatus(promo.id, e)
                            }
                          />
                        </div>
                      </td>
                      <td>
                        <div className={styles.actionPanel}>
                          <Tooltip show text="Edit" style={{ marginRight: 16 }}>
                            <button
                              type="button"
                              className={styles.editBtn}
                              title="Edit"
                              onClick={(e) => {
                                e.stopPropagation();
                                setActivelyEditedPromotion(promo);
                                setShowEditPromotionModal(true);
                              }}
                            >
                              <Pencil />
                            </button>
                          </Tooltip>
                          <Tooltip show text="Show details">
                            <button
                              className={styles.viewBtn}
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                navigate("./" + promo.id);
                              }}
                            >
                              View
                            </button>
                          </Tooltip>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr style={{ background: "none" }}>
                    <td colSpan={100}>
                      <div className={styles.noneFound}>
                        <img src="/none_found.svg" />
                        <h3>No promotions</h3>
                      </div>
                    </td>
                  </tr>
                )
              ) : (
                Array.from({ length: 8 }, (v, i) => i).map((i) => (
                  <UserSkeleton key={"user-skeleton-" + i} noOfStandard={8} />
                ))
              )}
            </tbody>
          </table>

          <div className={styles.pagination}>
            <Pagination
              currentPage={page}
              setCurrentPage={(currentPage: number) => {
                setPage(currentPage);
                fetchPromotions(currentPage);
              }}
              numberOfPages={totalPages}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default Promotions;
