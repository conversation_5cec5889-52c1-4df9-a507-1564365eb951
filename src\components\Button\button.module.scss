@use "../../styles/theme.scss" as *;

.button {
  height: 50px;
  min-width: 160px;
  padding: 0 28px;
  font-size: 16px;
  border: none;
  border-radius: 57px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: grid;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  &:disabled {
    pointer-events: none;
    opacity: 0.5;
  }
  &.primary {
    background: $orange;
    color: #fff;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
    &:hover {
      background: $dark-orange;
    }
  }
  &.secondary {
    background: transparent;
    color: $black;
    border: 1px solid $black;
    &:hover {
      background: $orange;
      border-color: $orange;
      color: #fff;
      box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
    }
  }
  &.white {
    background: #fff;
    color: $black;
    border: 1px solid $black;
    &:hover {
      background: $orange;
      border-color: $orange;
      color: #fff;
      box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
    }
  }
  &.tertiary {
    background: none;
    color: $black;
    min-width: initial;
    &:hover {
      color: $dark-orange;
    }
  }
  &.quaternary {
    background: none;
    color: $orange;
    min-width: initial;
    padding: 0 15px;
    &:hover {
      color: $dark-orange;
    }
  }
  &.search {
    height: 100%;
    background: $orange;
    color: #fff;
    margin-left: auto;
    &:hover {
      background: $dark-orange;
    }
  }
  &.export {
    background: $orange;
    color: #fff;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
    padding: 0 24px;
    height: 50px;
    min-width: 0;
    &:hover {
      background: $dark-orange;
    }
  }
  &.danger {
    background: #fee8e8;
    color: #c60808;

    &:hover {
      background: #fcdede;
    }
  }
  &.grey {
    background: #f7f6f6;
    color: #1a1a1a;
    height: 41px;
    min-width: initial;
    padding: 0px 16px;
    border-radius: 8px;
  }
}

.content {
  grid-area: 1 / 1 / 2 / 2;
  display: flex;
  align-items: center;
  svg {
    margin-right: 8px;
    vertical-align: middle;
  }
}
