import { faker } from "@faker-js/faker";

const createSub = () => {
  return {
    mdn: faker.string.numeric({ length: 10, allowLeadingZeros: false }),
    subscription: "Airespring ABC",
    activationDate: faker.date.recent({ days: 100 }),
    simType: ["sim", "esim"][faker.number.int(1)],
    planSize: "100 GB",
    dataBalance: "5 GB",
    porting: [
      "In Progress",
      "Confirmed",
      "Completed",
      "Cancelled",
      "Failed",
      "",
    ][faker.number.int(5)],
    status: [
      "Missing Details",
      "Cancelled",
      "Active",
      "Pending",
      "BAN Change",
      "Ready To Activate",
      "Rejected",
      "Cancelled",
    ][faker.number.int(7)],
  };
};

export const populateSubs = (number: number) => {
  let subs = [] as any;
  Array.from({ length: number }).forEach((x, i) => {
    subs.push(createSub());
  });
  return subs;
};

const createOrder = () => {
  return {
    transactionId: faker.string.numeric({
      length: 10,
      allowLeadingZeros: false,
    }),
    orderId: faker.string.numeric({ length: 10, allowLeadingZeros: false }),
    date: faker.date.recent({ days: 100 }),
    result: faker.datatype.boolean,
    product: "Airespring ABC",
    amount: faker.number.int(100),
    network: [
      "mastercard",
      "visa",
      "discover",
      "jcb",
      "diners",
      "unionpay",
      "amex",
    ][faker.number.int(6)],
    last4Digits: faker.string.numeric({ length: 4 }),
  };
};

export const populateOrders = (number: number) => {
  let subs = [] as any;
  Array.from({ length: number }).forEach((x, i) => {
    subs.push(createOrder());
  });
  return subs;
};

export const dummyPortins = [
  {
    id: 98,
    subscriberNumber: "**********",
    zipCode: "91304",
    status: "InComplete",
    iccid: "89014104333671615859",
    imei: "***************",
    product: {
      deviceType: "Smartphone",
      offerId: "410",
      product: "Airespring Throttle - Airespring-Custom Usage 1 GB",
      soc: "APX1GBS23",
      productSize: "1 GB",
      serviceType: "Unlimited",
      description: null,
      retailName: null,
      retailPrice: null,
      productFamily: "Airespring Throttle",
    },
    failureReason: "No AT&T failure reason provided",
    attDetails: {
      oldService: {
        billingAccountNumber: "**************",
        billingAccountPassword: null,
        firstName: "ODINAKA TEST",
        lastName: "ODINAKA TEST",
        addressDetails: {
          streetNumber: "9015",
          streetDirection: "N",
          streetName: "ETON AVE STE A",
          city: "Canoga",
          state: "CA",
          zipCode: "91304",
        },
      },
      accountDetails: {
        firstName: "ODINAKA TEST",
        lastName: "ODINAKA TEST",
        streetNumber: "9015",
        streetDirection: "",
        streetName: "ETON",
        city: "CANOGA PARK",
        state: "CA",
        zipCode: "91304",
        email: "<EMAIL>",
        contactNumber: "1223456",
        mvnoId: 1,
        channelId: null,
      },
      msisdn: "**********",
    },
    billingAccountPassword: null,
  },
  {
    id: 96,
    subscriberNumber: "**********",
    zipCode: "91304",
    status: "Cancelled",
    iccid: "89014104333631615420",
    imei: "***************",
    product: {
      deviceType: "Smartphone",
      offerId: "394",
      product: "Airespring Throttle - Airespring-Flat Rate 7 GB",
      soc: "APX1G5S23",
      productSize: "7 GB",
      serviceType: "Unlimited",
      description: null,
      retailName: "Super Hero Plan",
      retailPrice: "69.99",
      productFamily: "Airespring Throttle",
    },
    failureReason: "REMOVE NUMBER LOCK ON VERIZON ACCOUNT",
    attDetails: null,
    billingAccountPassword: null,
  },
  {
    id: 95,
    subscriberNumber: "**********",
    zipCode: "91304",
    status: "Completed",
    iccid: "89014104333671615818",
    imei: "***************",
    product: {
      deviceType: "Smartphone",
      offerId: "410",
      product: "Airespring Throttle - Airespring-Custom Usage 1 GB",
      soc: "APX1GBS23",
      productSize: "1 GB",
      serviceType: "Unlimited",
      description: null,
      retailName: null,
      retailPrice: null,
      productFamily: "Airespring Throttle",
    },
    failureReason: null,
    attDetails: null,
    billingAccountPassword: null,
  },
  {
    id: 92,
    subscriberNumber: "**********",
    zipCode: "91304",
    status: "Cancelled",
    iccid: "89014104333671616071",
    imei: "***************",
    product: {
      deviceType: "Smartphone",
      offerId: "410",
      product: "Airespring Throttle - Airespring-Custom Usage 1 GB",
      soc: "APX1GBS23",
      productSize: "1 GB",
      serviceType: "Unlimited",
      description: null,
      retailName: null,
      retailPrice: null,
      productFamily: "Airespring Throttle",
    },
    failureReason: "MDN NOT ACTIVE",
    attDetails: null,
    billingAccountPassword: null,
  },
  {
    id: 87,
    subscriberNumber: "**********",
    zipCode: "91304",
    status: "Completed",
    iccid: "89014104333671616022",
    imei: "***************",
    product: {
      deviceType: "Smartphone",
      offerId: "410",
      product: "Airespring Throttle - Airespring-Custom Usage 1 GB",
      soc: "APX1GBS23",
      productSize: "1 GB",
      serviceType: "Unlimited",
      description: null,
      retailName: null,
      retailPrice: null,
      productFamily: "Airespring Throttle",
    },
    failureReason: null,
    attDetails: null,
    billingAccountPassword: null,
  },
];
