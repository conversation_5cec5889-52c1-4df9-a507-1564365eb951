import Modal from "../Modal";
import styles from "./add-subscriber-error-modal.module.scss";

const AddSubscriberErrorModal = ({
  show,
  setShow,
  handleFinish,
  error,
}: any) => {
  return (
    <Modal
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={() => {
        setShow(false);
      }}
      image="/robot_error.svg"
      saveButton="Try Again"
      clearContainer
    >
      <div className={styles.main}>
        <h3>Something Went Wrong!</h3>
        <div className={styles.message} style={{ marginBottom: 16 }}>
          {error}
        </div>
        <div className={styles.message}>
          Your request has not been sent. Please try again
        </div>
      </div>
    </Modal>
  );
};

export default AddSubscriberErrorModal;
