import { useDispatch, useSelector } from "react-redux";
import Menu from "../Menu";
import NotificationBell from "../NotificationBell";
import { LogOut, User } from "../svgs";
import styles from "./user-menu.module.scss";
import { useNavigate, useParams } from "react-router-dom";
import { logOut } from "../utils/logOut";

const UserMenu = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { mvnoId } = useParams();

  const { userInfo } = useSelector((state: any) => state);

  return (
    <div className={styles.user}>
      <NotificationBell />
      <Menu
        data={{
          label: userInfo ? userInfo.firstName : "",
          items: [
            {
              label: "Profile",
              icon: <User />,
              link: `/${mvnoId}/user-profile`,
            },
            {
              label: "Logout",
              icon: <LogOut />,
              onClick: () => {
                logOut(dispatch, navigate);
              },
            },
          ],
        }}
      />
    </div>
  );
};

export default UserMenu;
