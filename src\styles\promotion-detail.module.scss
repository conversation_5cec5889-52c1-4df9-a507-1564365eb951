@use "./theme.scss" as *;

.main {
  width: 100%;
  background: #f1f1f1;
  padding: 50px 40px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;

  .breadcrumbs {
    a {
      color: $primary;
      text-decoration: none;
      &:hover {
        color: $orange;
      }
    }

    span {
      font-weight: 700;
    }
  }

  .actionButtons {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

.container {
  border-radius: 24px;
  background: #fff;
  margin-top: 16px;
  padding: 24px;

  .detailTop {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .detailDates {
    display: flex;
    gap: 24px;
    font-size: 14px;

    p {
      background-color: #f6f6f6;
      border-radius: 8px;
      padding: 8px;

      &.expiry {
        background-color: #fef2e3;
        display: flex;
        align-items: center;
        gap: 2.5px;
      }
    }
  }
  .detailDates.plain {
    margin-top: 6px;
    p {
      background-color: transparent;
      padding: 0;
      color: #6b6b6b;
    }
  }

  h1,
  h2 {
    font-size: 18px;
    font-weight: 700px;
    margin-top: 16px;
  }
}

.detailItemCardsContainer {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

.detailItemCard {
  background: #f6f6f6;
  width: 200px;
  padding: 16px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;

  span {
    font-size: 12px;
  }

  h3 {
    font-size: 18px;
    font-weight: 600;
  }
}

.tabsContainer {
  margin-top: 24px;

  .tabs {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  .tab {
    height: 47px;
    border-radius: 1000px;
    color: $black;
    font-size: 14px;
    font-weight: 600;
    padding: 0 24px;
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      color: $dark-orange;
    }

    span {
      position: relative;
      z-index: 6;
    }
  }

  .activeTab {
    cursor: auto;

    &:hover {
      color: $black;
    }
  }

  .tabBackground {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 1000px;
    background-color: $mid-orange;
    z-index: 5;
    left: 0;
  }
}

.tableContainer {
  overflow-x: auto;
  padding-bottom: 5px;
  border-radius: 24px;
  background: #fff;
  margin-top: 16px;
  padding: 24px 0 12px 0;

  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 6px;
    white-space: nowrap;

    tbody {
      tr {
        height: 40px;

        &:nth-child(2n + 1) {
          background: #f7f6f6;
        }

        td {
          font-size: 14px;
          font-weight: 400;
          line-height: 21px;
          padding: 0 16px;

          svg {
            vertical-align: middle;
          }

          a {
            color: $primary;

            &:hover {
              color: $orange;
            }
          }
        }

        td:first-child {
          border-radius: 12px 0 0 12px;
          padding-left: 24px;
        }

        td:last-child {
          border-radius: 0 12px 12px 0;
          padding-right: 24px;
        }
      }

      &:before {
        content: "@";
        display: block;
        line-height: 0px;
        text-indent: -99999px;
      }
    }

    thead {
      tr {
        border-bottom: 1px solid #0000141f;

        th {
          border-bottom: 1px solid #0000141f;
        }

        th:first-child {
          padding-left: 24px;
        }
      }
    }

    th {
      font-size: 14px;
      font-weight: 500;
      line-height: 21px;
      text-align: start;
      border-bottom: 1px solid $disabled;
      padding: 0 16px;
      padding-bottom: 19px;
    }
  }

  .viewBtn {
    border: none;
    background: none;
    font-weight: 600;
    color: #b85e1d;
    cursor: pointer;
  }

  .editBtn {
    border: none;
    background: none;
    cursor: pointer;
  }
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 55px;

  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }

  h3 {
    width: 100%;
    text-align: center;
  }
}

.pagination {
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: right;
}

.changesCell {
  display: flex;
  flex-direction: column;
  gap: 8px;

  &Item {
    display: flex;
    flex-direction: column;
    gap: 2px;

    &Field {
      font-size: 12px;
    }

    &Values {
      display: flex;
      align-items: center;
      gap: 16px;
      font-size: 14px;
    }
  }
}

.activityLogRow {
  vertical-align: top;

  td {
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
}
