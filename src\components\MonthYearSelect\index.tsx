import styles from "./month-year-select.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { useRef } from "react";
import { months } from "../utils/formatDate";
import { ChevronDown } from "../svgs";

const MonthYearSelect = ({ month, year, disabled, setMonth, setYear }: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  return (
    <div className={`${styles.box} month-year`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        } ${disabled && styles.disabled}`}
        onClick={(e: any) => {
          e.stopPropagation();
          toggleMenu(true);
        }}
      >
        {months[month - 1]} {year}
        <ChevronDown />
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        align="start"
        viewScroll="initial"
        position="initial"
        onItemClick={(e: any) => (e.stopPropagation = true)}
      >
        <div className={styles.container}>
          <div className={`${styles.column} modal-scroll`}>
            {months.map((item: any, index: number) => (
              <MenuItem className={styles.menuItem} key={item}>
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    setMonth(index + 1);
                  }}
                >
                  {item}
                </div>
              </MenuItem>
            ))}
          </div>
          <div
            className={`${styles.column} modal-scroll`}
            style={{ paddingLeft: 15 }}
          >
            {["2023", "2022"].map((item: any) => (
              <MenuItem className={styles.menuItem} key={item}>
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    setYear(parseInt(item));
                  }}
                >
                  {item}
                </div>
              </MenuItem>
            ))}
          </div>
        </div>
      </ControlledMenu>
    </div>
  );
};

export default MonthYearSelect;
