import styles from "./ticket-skeleton.module.scss";
import Checkbox from "../Checkbox";
import { ArrowSquareIn, ChevronDownLg, PencilSimple } from "../svgs";
import Shimmer from "../Shimmer";

const TicketSkeleton = () => {
  return (
    <div className={styles.main}>
      <div className={styles.summary}>
        {/*<Checkbox disabled />*/}
        <div className={styles.overview}>
          <div className={styles.top}>
            <div className={styles.id}>
              <Shimmer />
            </div>
            <div className={styles.date}>
              <Shimmer />
            </div>
          </div>
          <div className={styles.bottom}>
            <div className={styles.subject}>
              <Shimmer />
            </div>
            <div className={styles.email}>
              <Shimmer />
            </div>
          </div>
        </div>
        <div className={styles.dataBar}>
          <div className={`${styles.box1} ${styles.box}`}>
            <Shimmer />
          </div>
          {/*<div className={`${styles.box2} ${styles.box}`}>
            <Shimmer />
  </div>*/}
          <div className={`${styles.box3} ${styles.box}`}>
            <Shimmer />
          </div>
          <div className={`${styles.box4} ${styles.box}`}>
            <Shimmer />
          </div>
        </div>
        <div
          className={styles.expand}
          style={{ marginRight: 12, marginLeft: 24 }}
        >
          <ArrowSquareIn />
        </div>
        <div style={{ marginRight: 20 }} className={styles.expand}>
          <PencilSimple />
        </div>
        <div className={styles.expand}>
          <ChevronDownLg />
        </div>
      </div>
    </div>
  );
};

export default TicketSkeleton;
