import { useRef, useState } from "react";
import styles from "./dual-mode-input.module.scss";
import { Input } from "../Input";
import SegmentedControl from "../SegmentedControl";

type Mode = {
  label: string;
  value: string;
  transformer?: (value: number) => number;
};

type ValueWithMode<T extends Mode> = {
  value: number;
  mode: T;
};

type DualModeInputProps<T extends Mode> = {
  label: string;
  value: number;
  activeModeValue: T["value"];
  modes: T[];
  onChange: (value: ValueWithMode<T>) => void;
  onModeChange: (mode: string) => void;
  error?: string;
};

const DualModeInput = <T extends Mode>(
  props: DualModeInputProps<T>,
): JSX.Element => {
  const {
    onChange,
    onModeChange,
    modes,
    value,
    activeModeValue,
    error,
    label,
  } = props;
  const activeMode =
    modes.find((mode) => mode.value === activeModeValue) || modes[0];

  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <div className={styles.container}>
      <Input
        label={label}
        value={value}
        leftComponent={<span>{activeMode.label}</span>}
        onChange={(e: any) => {
          onChange({
            value: activeMode.transformer
              ? activeMode.transformer(e.target.value)
              : e.target.value,
            mode: activeMode,
          });
        }}
        number
        error={error}
        ref={inputRef}
      />
      <div className={styles.segmentedControl}>
        <SegmentedControl
          options={modes.map((o) => ({
            label: o.label,
            value: o.value,
          }))}
          selectedValue={activeMode.value}
          onChange={(value) => {
            inputRef.current?.focus();

            // clear input on mode change
            onChange({
              // @ts-expect-error Set as empty string which isn't a valid number
              value: "",
              mode: modes.find((m) => m.value === value) || modes[0],
            });

            onModeChange(value);
          }}
        />
      </div>
    </div>
  );
};

export default DualModeInput;
