import styles from "../../styles/user-management.module.scss";
import { Export, Pencil, Plus } from "../../components/svgs";
import { useEffect, useState } from "react";
import Button from "../../components/Button";
import Pagination from "../../components/Pagination";
import { filterList } from "../../components/utils/searchAndFilter";
import MultiSelect from "../../components/MultiSelect";
import UserSkeleton from "../../components/UserSkeleton";
import { useDispatch, useSelector } from "react-redux";
import SearchSection from "../../components/SearchSection";
import { ApiGet, ApiGetNoAuth, ApiPatchWithId, ApiPut } from "../api/api";
import { padArrayToLength } from "../../components/utils/padArray";
import RemoveFiltersBar from "../../components/RemoveFiltersBar";
import {
  exportCsv,
  exportCsvPlainFields,
} from "../../components/utils/exportCsv";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import Toggle from "../../components/Toggle";
import formatDate, {
  formatDateWithTime,
} from "../../components/utils/formatDate";
import EditProductModal from "../../components/EditProductModal";
import { motion } from "framer-motion";
import AddFeeModal from "../../components/AddFeeModal";
import EditFeeModal from "../../components/EditFeeModal";
import Tooltip from "../../components/Tooltip";
import TrueFalseStatus from "../../components/TrueFalseStatus";
import RadioSelect from "../../components/RadioSelect";

const ProductCatalogue = () => {
  const tabs = [
    {
      label: "Products",
      key: "products",
    },
    {
      label: "Fees",
      key: "fees",
    },
  ] as const;
  const [searchParams, setSearchParams] = useSearchParams();
  const activeTabKey = searchParams.get("tab") === "fees" ? "fees" : "products";

  const setActiveTab = (key: string) => {
    if (key === "fees") {
      setSearchParams({ tab: "fees" });
    } else {
      setSearchParams({});
    }
  };

  const { userInfo } = useSelector((state: any) => state);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [initialLoading, setInitialLoading] = useState(true);

  const { mvnoId } = useParams();

  useEffect(() => {
    if (userInfo.roleName === "Agent") {
      navigate(`/${mvnoId}/subscriber-management`);
    }
  }, [userInfo]);

  const [currentPage, setCurrentPage] = useState(1);
  const productsPerPage = 8;

  const [queryDisplay, setQueryDisplay] = useState("");

  const [products, setProducts] = useState([] as any);

  const [filterdProducts, setFilteredProducts] = useState([] as any);

  const [filterOptions, setFilterOptions] = useState({
    approaches: [],
    billings: [],
    productFamily: [],
    talkAndText: [],
  });

  const load = (next: any = null) => {
    ApiGetNoAuth(`/products/catalogue/${mvnoId}`)
      .then((response) => {
        console.log(response);
        let prodList = response.data;
        let appStore = [] as any,
          billStore = [] as any,
          famStore = [] as any,
          tatStore = [] as any;
        prodList.forEach((prod: any) => {
          if (!appStore.includes(prod.ProductApproach)) {
            appStore.push(prod.ProductApproach);
          }
          if (!billStore.includes(prod.Billing)) {
            billStore.push(prod.Billing);
          }
          if (!famStore.includes(prod.ProductFamily)) {
            famStore.push(prod.ProductFamily);
          }
          if (!tatStore.includes(prod.TalkAndText)) {
            tatStore.push(prod.TalkAndText);
          }
        });
        setFilterOptions({
          approaches: appStore,
          billings: billStore,
          productFamily: famStore,
          talkAndText: tatStore,
        });
        setProducts(response.data);
        setInitialLoading(false);

        if (next) next();
      })
      .catch((error) => {
        setInitialLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message || "Something went wrong",
          },
        });
      });
  };

  useEffect(load, []);

  const filterData = [
    {
      label: "Family",
      key: "ProductFamily",
      options: filterOptions.productFamily,
    },
    {
      label: "Talk & Text",
      key: "TalkAndText",
      options: filterOptions.talkAndText,
    },
    {
      label: "Approach",
      key: "ProductApproach",
      options: filterOptions.approaches,
    },
    {
      label: "Billing",
      key: "Billing",
      options: filterOptions.billings,
    },
  ];

  const emptyFilters = {
    ProductFamily: [],
    TalkAndText: [],
    ProductApproach: [],
    Billing: [],
  } as any;

  // Store for table column filters
  const [filters, setFilters] = useState(emptyFilters);

  const resetFilters = () => {
    setFilters(emptyFilters);
  };

  // Trigger filters when changed
  useEffect(() => {
    setFilteredProducts(filterList(products, filters));
  }, [products, filters]);

  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  const [editingProduct, setEditingProduct] = useState(false);
  const [activeProduct, setActiveProduct] = useState(null as any);

  const ShowToggle = ({ product }: any) => {
    const handleToggleChange = (product: any) => {
      setLoading(true);
      setShowStatus((prev: boolean) => !prev);
      ApiPatchWithId("/products/showstatus", mvnoId, {
        offerId: parseInt(product.OfferId),
        showStatus: !product.ShowOnRetail,
      })
        .then((response) => {
          load(() => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
          });
        })
        .catch((error) => {
          setShowStatus((prev: boolean) => !prev);
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: error.response.data.message,
            },
          });
        });
    };

    const [showStatus, setShowStatus] = useState(false);

    const [loading, setLoading] = useState(false);

    useEffect(() => {
      setShowStatus(product.ShowOnRetail);
    }, [product]);

    return (
      <td>
        <Toggle
          disabled={loading}
          blue
          on={showStatus}
          onChange={() => {
            handleToggleChange(product);
          }}
        />
      </td>
    );
  };

  const [showAddFeeModal, setShowAddFeeModal] = useState(false);

  const [showEditFeeModal, setShowEditFeeModal] = useState(false);
  const [activelyEditedFee, setActivelyEditedFee] = useState(null as any);

  const [feesLoading, setFeesLoading] = useState(true);
  const [fetchedFees, setFetchedFees] = useState(null as any);

  const hasReachedFeeLimit = fetchedFees?.length >= 3;
  const canCreateNewFee = !feesLoading && !hasReachedFeeLimit;

  function fetchFees() {
    setFeesLoading(true);
    ApiGet(`/fees/mvno/${mvnoId}`)
      .then((response) => {
        console.log(response);
        const feesData = response.data;
        setFetchedFees(feesData);
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "Could not fetch fees",
            message: error.response?.data?.message || "Something went wrong",
          },
        });
      })
      .finally(() => {
        setFeesLoading(false);
      });
  }

  useEffect(() => {
    if (activeTabKey === "fees") {
      fetchFees();
    }
  }, [activeTabKey]);

  const handleChangeFeeStatus = (fee: any, newStatus: any) => {
    setFeesLoading(true);
    ApiPut(`/fees/${fee.id}`, {
      name: fee.name,
      amount: fee.amount,
      type: fee.type,
      status: newStatus,
    })
      .then((response) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        fetchFees();
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      })
      .finally(() => {
        setFeesLoading(false);
      });
  };

  return (
    <div className={styles.main}>
      <EditProductModal
        show={editingProduct}
        setShow={setEditingProduct}
        product={activeProduct}
        repopulate={load}
      />
      <SearchSection
        data={products}
        filters={filters}
        setFilteredData={setFilteredProducts}
        setQueryDisplay={setQueryDisplay}
        placeholder="Search a product name"
        id="products-search"
        setCurrentPage={setCurrentPage}
      />
      <div className={styles.titleBar}>
        <h3>Product Catalogue</h3>
      </div>
      <div className={styles.selectionWrapper}>
        {tabs.map((tab) => (
          <div
            className={`${styles.selection} ${
              activeTabKey === tab.key && styles.activeSelection
            }`}
            onClick={() => {
              setActiveTab(tab.key);
            }}
          >
            <span>{tab.label}</span>
            {activeTabKey === tab.key && (
              <motion.div className={styles.background} layoutId="underline" />
            )}
          </div>
        ))}
        {activeTabKey === "products" && (
          <Button
            onClick={() => {
              exportCsvPlainFields(
                Object.keys(filterdProducts[0]),
                filterdProducts,
                dispatch,
                "product_catalogue",
              );
            }}
            style={{ marginLeft: "auto" }}
            disabled={initialLoading}
          >
            <Export />
            Export to CSV
          </Button>
        )}

        {activeTabKey === "fees" && (
          <div className={styles.createFeesAction}>
            {hasReachedFeeLimit && (
              <p>
                You've reached the limit of 3 fees.
                <br />
                No additional fees can be added.
              </p>
            )}
            <Button
              disabled={!canCreateNewFee}
              onClick={() => canCreateNewFee && setShowAddFeeModal(true)}
            >
              <Plus />
              Create New
            </Button>
          </div>
        )}
      </div>

      <div className="">
        {activeTabKey === "products" && (
          <div className={`${styles.usersPanel} ${styles.products}`}>
            <div className={`${styles.filters} menu-select select`}>
              <div className={styles.label}>Filters</div>
              {filterData.map((filterType) => (
                <MultiSelect
                  key={`filter-${filterType.key}`}
                  label={filterType.label}
                  options={filterType.options.map((option: any) => ({
                    key: option,
                    label: option,
                  }))}
                  selected={filters[filterType.key]}
                  setSelected={(state: any) => {
                    setFilters({
                      ...filters,
                      [filterType.key]: state,
                    });
                  }}
                />
              ))}
            </div>
            <RemoveFiltersBar
              filters={filters}
              setFilters={setFilters}
              resetFilters={resetFilters}
              grey
            />
            <div className={`${styles.tableContainer} table-scroll`}>
              <table>
                <thead>
                  <tr>
                    <th>Family</th>
                    <th>Name</th>
                    <th>
                      Commercial
                      <br />
                      Name
                    </th>
                    <th>
                      Commercial
                      <br />
                      Description
                    </th>
                    <th>Size</th>
                    {userInfo?.roleName === "mvne" && (
                      <th>
                        Wholesale
                        <br />
                        Price
                      </th>
                    )}
                    <th>
                      Retail
                      <br />
                      Price
                    </th>
                    <th>
                      Creation
                      <br />
                      Date
                    </th>
                    <th>Talk & Text</th>
                    <th>Approach</th>
                    <th>Billing</th>
                    <th>
                      Show on
                      <br />
                      Retail
                    </th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {!initialLoading ? (
                    filterdProducts.length !== 0 ? (
                      padArrayToLength(
                        filterdProducts.slice(
                          (currentPage - 1) * productsPerPage,
                          currentPage * productsPerPage,
                        ),
                        productsPerPage,
                        null,
                      ).map((singleProduct: any, index: any) =>
                        singleProduct === null ? (
                          <tr
                            key={`filler-${index}`}
                            style={{
                              background: "none",
                              pointerEvents: "none",
                            }}
                          ></tr>
                        ) : (
                          <tr key={"prod-row-" + singleProduct.OfferId}>
                            <td>{singleProduct.ProductFamily}</td>
                            <td>{singleProduct.OfferName}</td>
                            <td
                              dangerouslySetInnerHTML={{
                                __html: singleProduct?.RetailName || "-",
                              }}
                            />
                            <td
                              style={
                                singleProduct?.RetailDescritpion?.includes("\n")
                                  ? {
                                      whiteSpace: "pre",
                                      padding: "16px",
                                      lineHeight: "24px",
                                    }
                                  : {}
                              }
                              dangerouslySetInnerHTML={{
                                __html: singleProduct?.RetailDescritpion || "-",
                              }}
                            />
                            <td>{singleProduct.Size}</td>
                            {userInfo?.roleName === "mvne" && (
                              <td>{singleProduct.Price.toFixed(2)}</td>
                            )}
                            <td>
                              {singleProduct.RetailPrice
                                ? parseFloat(singleProduct.RetailPrice).toFixed(
                                    2,
                                  )
                                : "-"}
                            </td>
                            <td>{formatDate(new Date())}</td>
                            <td>{singleProduct.TalkAndText}</td>
                            <td>{singleProduct.ProductApproach}</td>
                            <td>{singleProduct.Billing}</td>
                            <ShowToggle product={singleProduct} />
                            <td>
                              <div
                                style={{ cursor: "pointer" }}
                                onClick={() => {
                                  setEditingProduct(true);
                                  setActiveProduct(singleProduct);
                                }}
                              >
                                <Pencil />
                              </div>
                            </td>
                          </tr>
                        ),
                      )
                    ) : (
                      <tr style={{ background: "none" }}>
                        <td colSpan={100}>
                          <div className={styles.noneFound}>
                            <img src="/none_found.svg" />
                            <h3>
                              We couldn't find anything matching
                              {queryDisplay ? <>"{queryDisplay}"</> : "."}
                            </h3>
                          </div>
                        </td>
                      </tr>
                    )
                  ) : (
                    Array.from({ length: productsPerPage }, (v, i) => i).map(
                      (i) => (
                        <UserSkeleton
                          key={"user-skeleton-" + i}
                          noOfStandard={12}
                        />
                      ),
                    )
                  )}
                </tbody>
              </table>
            </div>
            <div className={styles.pagination}>
              <Pagination
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                numberOfPages={Math.ceil(
                  filterdProducts.length / productsPerPage,
                )}
              />
            </div>
          </div>
        )}

        {activeTabKey == "fees" && (
          <>
            <AddFeeModal
              show={showAddFeeModal}
              setShow={setShowAddFeeModal}
              mvnoId={mvnoId}
              refreshFees={fetchFees}
            />
            <EditFeeModal
              show={showEditFeeModal}
              setShow={setShowEditFeeModal}
              fee={activelyEditedFee}
              refreshData={fetchFees}
            />
            <div className={`${styles.feesTableContainer} table-scroll`}>
              <h1 className={`${styles.feesTableHeader}`}>Fees</h1>
              <table>
                <thead>
                  <tr>
                    <th>Type</th>
                    <th>Name</th>
                    <th>Amount</th>
                    <th>Date Created</th>
                    <th colSpan={3}>Status</th>
                  </tr>
                </thead>
                <tbody>
                  {!feesLoading ? (
                    fetchedFees?.length ? (
                      fetchedFees?.map((fee: any) => (
                        <tr
                          style={{ cursor: "pointer" }}
                          onClick={() => navigate("./fee/" + fee.id)}
                        >
                          <td>{fee.type}</td>
                          <td>{fee.name}</td>
                          <td>${fee.amount}</td>
                          <td>{formatDateWithTime(fee.dateCreated)}</td>
                          <td>
                            <div
                              style={{
                                display: "flex",
                                justifyContent: "flex-start",
                              }}
                            >
                              <RadioSelect
                                label={<TrueFalseStatus status={fee.status} />}
                                options={[
                                  {
                                    label: <TrueFalseStatus status />,
                                    key: true,
                                  },
                                  {
                                    label: <TrueFalseStatus />,
                                    key: false,
                                  },
                                ]}
                                selected={fee.status}
                                onChange={(e: any) => {
                                  handleChangeFeeStatus(fee, e);
                                }}
                              />
                            </div>
                          </td>
                          <td>
                            <div className={styles.actionPanel}>
                              <Tooltip
                                show
                                text="Edit fee"
                                style={{ marginRight: 16 }}
                              >
                                <button
                                  type="button"
                                  className={styles.editFeeBtn}
                                  title="Edit fee"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setActivelyEditedFee(fee);
                                    setShowEditFeeModal(true);
                                  }}
                                >
                                  <Pencil />
                                </button>
                              </Tooltip>
                              <Tooltip show text="View fee details">
                                <button
                                  className={styles.viewBtn}
                                  type="button"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    navigate("./fee/" + fee.id);
                                  }}
                                >
                                  View
                                </button>
                              </Tooltip>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr style={{ background: "none" }}>
                        <td colSpan={100}>
                          <div className={styles.noneFound}>
                            <img src="/none_found.svg" />
                            <h3>
                              No fees created yet
                              {queryDisplay ? <>"{queryDisplay}"</> : "."}
                            </h3>
                          </div>
                        </td>
                      </tr>
                    )
                  ) : (
                    Array.from({ length: 3 }, (v, i) => i).map((i) => (
                      <UserSkeleton
                        key={"user-skeleton-" + i}
                        noOfStandard={12}
                      />
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ProductCatalogue;
