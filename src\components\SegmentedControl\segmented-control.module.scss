.segmentedControl {
  display: flex;
  border: 1px solid #666;
  border-radius: 50px;
  overflow: hidden;
  background-color: #fff;
  padding: 4px;
}

.option {
  flex: 1;
  padding: 4px 13px;
  font-size: 14px;
  color: #1b2023;
  background-color: transparent;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  transition:
    background-color 0.3s,
    color 0.3s;
}

.selected {
  background-color: #ff6700; /* Highlight color */
  color: #fff;
}
