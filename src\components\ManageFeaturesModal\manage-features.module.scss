@use "../../styles/theme.scss" as *;

.main {
  padding-right: 16px;
  &.loading {
    opacity: 0.5;
    pointer-events: none;
  }
}

.featureType {
  width: 100%;
  border: 1px solid;
  border-radius: 24px;
  overflow: hidden;
  margin-bottom: 24px;
  .featureName {
    padding: 0 24px;
    height: 53px;
    font-size: 14px;
    line-height: 21px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .featureItems {
    padding: 24px 14px 14px 24px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    .item {
      display: flex;
      align-items: center;
      padding: 4px 12px;
      border-radius: 6px;
      font-size: 14px;
      line-height: 21px;
      margin: 0px 10px 10px 0px;
      svg {
        height: 20px;
        width: 20px;
        margin-left: 2px;
        vertical-align: middle;
      }
    }
  }
}

.throttle {
  border-color: #6361dc;
  .featureName,
  .item {
    background-color: #6361dc;
    color: #fff;
  }
}

.boltons {
  border-color: #f67e93;
  .featureName,
  .item {
    background-color: #f67e93;
  }
}

.features {
  border-color: #cb91f8;
  .featureItems {
    flex-direction: column;
    flex-wrap: nowrap;
    .featureToggleItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    .toggleContainer {
      display: flex;
      align-items: center;
    }
  }
  .featureName,
  .item {
    background-color: #cb91f8;
    margin: 0 !important;
  }
}

.confirmRemove {
  margin: 0 auto;
  height: 100%;
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 24px;
  line-height: 36px;
  text-align: center;
}

.removeButton {
  cursor: pointer;
}

.noneAddedText {
  width: 100%;
  text-align: center;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 21px;
}

.addButton {
  background: none;
  border: none;
  color: inherit;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  width: 44px;
  border-radius: 1000px;
  &:hover {
    background: rgba(0, 0, 0, 0.1);
  }
  svg {
    vertical-align: middle;
  }
}
