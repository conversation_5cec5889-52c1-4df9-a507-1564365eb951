@use "../../styles/theme.scss" as *;

.menuButton {
  height: 32px;
  background: #f7f6f6;
  padding: 5.5px 6px 5.5px 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  display: flex;
  align-items: center;
  margin-right: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  svg {
    margin-left: 4px;
  }
  &:hover {
    background: $light-orange;
  }
  svg {
    transition: all 0.2s ease;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }
  &.darkerBg {
    background: #e3e3e3;
  }
}

.box {
  height: 100%;
}

.menuItem {
  display: flex;
  align-items: center;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: $black;
  padding: 0;
  line-height: 21px;
  font-size: 14px;
  padding: 0;
  gap: 11px;
  cursor: pointer;
  br {
    display: none;
  }
  &:hover {
    background: none;
  }
}

.container {
  display: grid;
  grid-template-columns: 1fr;
  grid-row-gap: 18px;
  max-height: 318px;
  overflow: auto;
  grid-column-gap: 32px;
  padding-right: 12px;
  &.grid {
    grid-template-columns: repeat(3, auto);
  }
  &.twoColumnGrid {
    grid-template-columns: repeat(2, auto);
  }
}

.buttons {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  margin-top: 32px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  // height: 318px;
}