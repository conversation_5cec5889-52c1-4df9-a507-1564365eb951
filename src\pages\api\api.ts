import axios, { CancelTokenSource } from "axios";
import { isNumeric } from "../../components/utils/CardDetailsCheckers";
import moment from "moment";

const queryCors = import.meta.env.VITE_APP_HOST_NAME;

const headers = {
  Accept: "application/json",
  apikey: import.meta.env.VITE_APP_API_KEY,
  companycode: import.meta.env.VITE_APP_COM_CODE,
};

const api = axios.create({
  baseURL: queryCors,
  headers,
});

const refreshToken = async () => {
  try {
    const refreshToken = localStorage.getItem("refreshToken");
    const lastActive = localStorage.getItem("lastActive");
    const lastActiveDate = moment(lastActive);
    const now = moment();
    const dif = moment.duration(now.diff(lastActiveDate)).asMinutes();

    if (dif > 35) throw Error;

    const resp = await ApiPost("/users/refreshtoken", {
      refreshToken: refreshToken,
    });
    return resp.data;
  } catch (e) {
    console.log(e);
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("token");
    localStorage.removeItem("crmUserInfo");
    window.location.replace("/login");
  }
};

api.interceptors.response.use(
  async (response: any) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    console.log(originalRequest);
    if (error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const resp = await refreshToken();
      const accessToken = resp.accessToken;

      localStorage.setItem("token", accessToken);
      originalRequest.headers.Authorization = `Bearer ${accessToken}`;
      return api(originalRequest);
    }
    return Promise.reject(error);
  },
);

api.interceptors.request.use(
  function (config) {
    let newConfig = { ...config };
    const mvnoId = window.location.pathname.split("/")[1];
    if (isNumeric(mvnoId)) {
      newConfig.headers.mvnoId = mvnoId;
    }
    return newConfig;
  },
  function (error) {
    return Promise.reject(error);
  },
);

export const ApiPostAuth = (
  url: string,
  payload?: { [propName: string]: any },
  cancelToken?: CancelTokenSource,
) =>
  api.post(queryCors + url, payload, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiPost = (
  url: string,
  parameters: { [propName: string]: any },
  cancelToken?: CancelTokenSource,
) =>
  api.post(queryCors + url, parameters, {
    headers: headers,
    cancelToken: cancelToken?.token,
  });

export const ApiPatch = (
  url: string,
  parameters?: { [propName: string]: any },
  cancelToken?: CancelTokenSource,
) =>
  api.patch(queryCors + url, parameters, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiPatchWithId = (
  url: string,
  id: any,
  parameters?: { [propName: string]: any },
  cancelToken?: CancelTokenSource,
) =>
  api.patch(queryCors + url, parameters, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      mvnoId: id,
      ...headers,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiPut = (
  url: string,
  parameters?: { [propName: string]: any },
  cancelToken?: CancelTokenSource,
) =>
  api.put(queryCors + url, parameters, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiPutWithId = (
  url: string,
  id: any,
  parameters?: { [propName: string]: any },
  type = "",
  cancelToken?: CancelTokenSource,
) =>
  api.put(queryCors + url, parameters, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      mvnoId: id,
      type: type,
      ...headers,
    },
    cancelToken: cancelToken?.token,
  });

export const ApiGet = (url: string, signal?: any) =>
  api.get(queryCors + url, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    signal: signal,
  });

export const ApiGetSubscriber = (url: string, type: string) =>
  api.get(queryCors + url, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      type: type,
      ...headers,
    },
  });

export const ApiGetWithId = (url: string, id: any) =>
  api.get(queryCors + url, {
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      mvnoId: id,
      ...headers,
    },
  });

export const ApiGetNoAuth = (url: string, cancelToken?: CancelTokenSource) =>
  api.get(queryCors + url, {
    headers: headers,
    cancelToken: cancelToken?.token,
  });

export const ApiDelete = (
  url: string,
  parameters: { [propName: string]: any },
) =>
  api.request({
    url: queryCors + url,
    method: "delete",
    headers: {
      Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
      ...headers,
    },
    data: parameters,
  });
