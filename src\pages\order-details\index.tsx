import React, { useState, useEffect } from "react";
import styles from "../../styles/order-details.module.scss";
import { useDispatch, useSelector } from "react-redux";
import { Link, useParams } from "react-router-dom";
import Button from "../../components/Button";
import { ArrowBack } from "../../components/svgs";
import UserMenu from "../../components/UserMenu";
import moment from "moment";
import OrderStatusBadge from "../../components/OrderStatusBadge";
import { Divider, CircularProgress } from "@mui/material";
import SimTypeBadge from "../../components/SimTypeBadge";
import AddOrderIccidModal from "../../components/AddOrderIccidModal";
import { ApiGetSubscriber, ApiGetWithId } from "../api/api";
import { formatDateWithTime } from "../../components/utils/formatDate";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import AddMissingPortinDetails from "../../components/AddMissingPortinDetails";

const index = () => {
  const dispatch = useDispatch();
  const { mvnoId, id } = useParams();
  const { sidebarOpen } = useSelector((state: any) => state);
  const [showAddIccid, setShowAddIccid] = useState(false);
  const [showAddPortin, setShowAddPortin] = useState(false);
  const [activeOrder, setActiveOrder] = useState(null as any);

  const [portDetails, setPortDetails] = useState(null as any);

  const [portinLoading, setPortinLoading] = useState(false);

  //const formattedDate = moment(order.time).format("DD MMM YYYY, HH:mm:ss");
  const formattedDate = "";

  const [order, setOrder] = useState(null as any);
  const [orderNotFound, setOrderNotFound] = useState(false);

  const loadOrder = () => {
    setOrder(null as any);
    ApiGetWithId(`/app/orders?search=${id}`, mvnoId)
      .then((response: any) => {
        if (response.data.content.length > 0) {
          setOrder(response.data.content[0]);
        } else {
          setOrderNotFound(true);
        }
      })
      .catch((error: any) => {
        console.log(error);
      });
  };

  useEffect(loadOrder, []);

  const handleGetPort = () => {
    setPortinLoading(true);
    ApiGetSubscriber(
      `/accounts/by-account/${order.mid}/portin/${order.tempPortInId}`,
      "temp",
    )
      .then((response) => {
        setPortinLoading(false);
        setPortDetails(response.data);
        setShowAddPortin(true);
      })
      .catch((error: any) => {
        setPortinLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <div
      className={`${styles.main} ${sidebarOpen ? styles.open : styles.closed}`}
    >
      <AddOrderIccidModal
        show={showAddIccid}
        setShow={setShowAddIccid}
        order={activeOrder}
        repopulate={loadOrder}
      />
      <AddMissingPortinDetails
        show={showAddPortin}
        setShow={setShowAddPortin}
        plan={portDetails}
        repopulate={loadOrder}
      />
      <div className={styles.topBar}>
        <div className={styles.backLink}>
          <Link style={{ textDecoration: "none" }} to={`/${mvnoId}/orders`}>
            <Button color="tertiary" style={{ padding: 0, height: 26 }}>
              <ArrowBack /> Back to Orders
            </Button>
          </Link>
        </div>
        <UserMenu />
      </div>
      <SwitchTransition>
        <CSSTransition
          key={order ? "ready" : "loading"}
          addEndListener={(node, done) =>
            node.addEventListener("transitionend", done, false)
          }
          classNames="fade"
        >
          {order ? (
            <div className={`${styles.mainTile}`}>
              <div className={styles.topBar}>
                <div className={styles.orders}>
                  <h3>Order #{order?.orderNumber}</h3>
                  <p>{formatDateWithTime(order?.dateAndTime)}</p>
                </div>
                <div style={{ marginBottom: "auto" }}>
                  <OrderStatusBadge status={order?.status} flat />
                </div>
                <div className={styles.btn}>
                  {order?.status.includes("PORTIN") && (
                    <Button
                      color="primary"
                      onClick={handleGetPort}
                      style={{ minWidth: "initial" }}
                      loading={portinLoading}
                    >
                      Add Port In Details
                    </Button>
                  )}
                  {order?.status.includes("ICCID") && (
                    <Button
                      color="primary"
                      onClick={() => {
                        setActiveOrder(order);
                        setShowAddIccid(true);
                      }}
                      style={{ minWidth: "initial" }}
                    >
                      Add ICCID
                    </Button>
                  )}
                </div>
              </div>
              <Divider style={{ width: "100%", height: "0.5px" }} />
              <div className={styles.orderPanel}>
                <div className={styles.orderTile}>
                  <div className={styles.heading}>
                    <h4>Subscriber Details</h4>
                    <Link
                      style={{
                        textDecoration: "none",
                      }}
                      to={`/${mvnoId}/subscriber/${order?.mid}`}
                    >
                      <Button
                        style={{
                          height: "auto",
                          padding: 0,
                        }}
                        color="quaternary"
                      >
                        View Subscriber Profile
                      </Button>
                    </Link>
                  </div>
                  <div className={styles.innerTile}>
                    <div className={styles.in}>
                      <table>
                        <tr style={{ paddingBottom: "16px" }}>
                          <td>
                            <b>Subscriber</b>
                          </td>
                          <td>{order?.subscriberName}</td>
                        </tr>
                        {order?.deliveryAddress && (
                          <tr>
                            <td style={{ whiteSpace: "nowrap" }}>
                              <b>Delivery Address</b>
                            </td>
                            <td>
                              {order?.deliveryAddress?.streetNumber}{" "}
                              {order?.deliveryAddress?.streetDirection}
                              <br />
                              {order?.deliveryAddress?.streetName}
                              <br />
                              {order?.deliveryAddress?.city}
                              <br />
                              {order?.deliveryAddress?.state}
                              <br />
                              {order?.deliveryAddress?.zipCode}
                            </td>
                          </tr>
                        )}
                      </table>
                    </div>
                  </div>
                </div>
                <div className={styles.orderTile}>
                  <div className={styles.heading}>
                    <h4>Order Details</h4>
                  </div>
                  <div className={styles.innerTile}>
                    <div className={styles.in}>
                      <table>
                        <tr style={{ paddingBottom: "24px" }}>
                          <td>
                            <b>SIM Type</b>
                          </td>
                          <td>
                            <SimTypeBadge status={order?.simType} />
                          </td>
                        </tr>
                        <tr style={{ paddingBottom: "24px" }}>
                          <td>
                            <b>Product</b>
                          </td>
                          <td>{order?.product.offerName}</td>
                        </tr>
                        <tr style={{ paddingBottom: "24px" }}>
                          <td>
                            <b>IMEI</b>
                          </td>
                          <td>{order?.imei}</td>
                        </tr>
                        <tr>
                          <td>
                            <b>ICCID</b>
                          </td>
                          <td>{order?.iccid || "-"}</td>
                        </tr>
                      </table>
                    </div>
                  </div>
                </div>
                <div className={styles.orderTile}>
                  <div className={styles.heading}>
                    <h4>Payment Details</h4>
                  </div>
                  <div className={styles.innerTile}>
                    <div className={styles.in}>
                      <table>
                        <tr style={{ paddingBottom: "16px" }}>
                          <td>
                            <b>Order Total</b>
                          </td>
                          <td>
                            {fallback(
                              withUnit(order?.paymentDetails?.orderTotal),
                              "N/A",
                            )}
                          </td>
                        </tr>
                        <tr style={{ paddingBottom: "16px" }}>
                          <td>
                            <b>Tax amount</b>
                          </td>
                          <td>
                            {fallback(
                              withUnit(order?.paymentDetails?.taxAmount),
                              "N/A",
                            )}
                          </td>
                        </tr>
                        <tr style={{ paddingBottom: "16px" }}>
                          <td>
                            <b>Regulatory Fee</b>
                          </td>
                          <td>
                            {fallback(
                              withUnit(order?.paymentDetails?.regulatoryFee),
                              "N/A",
                            )}
                          </td>
                        </tr>
                        <tr style={{ paddingBottom: "16px" }}>
                          <td>
                            <b>Activation Fee</b>
                          </td>
                          <td>
                            {fallback(
                              withUnit(order?.paymentDetails?.activationFee),
                              "N/A",
                            )}
                          </td>
                        </tr>
                        <tr style={{ paddingBottom: "16px" }}>
                          <td>
                            <b>Discount</b>
                          </td>
                          <td>
                            {fallback(
                              withUnit(order?.paymentDetails?.discountAmount),
                              "N/A",
                            )}
                          </td>
                        </tr>
                        <tr style={{ paddingBottom: "16px" }}>
                          <td>
                            <b>Payment Method</b>
                          </td>
                          <td>
                            <div
                              style={{
                                display: "flex",
                                alignItems: "center",
                                gap: 10,
                              }}
                            >
                              {fallback(
                                paymentMethodDisplay(
                                  order?.paymentDetails?.paymentMethod,
                                ),
                                "N/A",
                              )}
                              <div
                                style={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 4,
                                }}
                              >
                                <Dots />
                                <span>
                                  {fallback(
                                    order?.paymentDetails?.last4DigitCard,
                                    "N/A",
                                  )}
                                </span>
                              </div>
                            </div>
                          </td>
                        </tr>
                        <tr style={{ paddingBottom: "16px" }}>
                          <td>
                            <b>Final Checkout Amount</b>
                          </td>
                          <td>
                            {fallback(
                              withUnit(
                                order?.paymentDetails?.finalCheckoutAmount,
                              ),
                              "N/A",
                            )}
                          </td>
                        </tr>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : orderNotFound ? (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "18px",
              }}
            >
              Order not found
            </div>
          ) : (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                padding: "200px 0",
              }}
            >
              <CircularProgress
                style={{
                  width: 50,
                  height: 50,
                  color: "#f47d27",
                }}
              />
            </div>
          )}
        </CSSTransition>
      </SwitchTransition>
    </div>
  );
};

export default index;

function paymentMethodDisplay(method: string) {
  switch (method) {
    case "mastercard": {
      return (
        <div>
          <img src="/mastercard_card_logo.svg" width={26} />
        </div>
      );
    }

    case "visa": {
      return (
        <div>
          <img src="/visa_card_logo.svg" width={32} />
        </div>
      );
    }

    case "discover": {
      return (
        <div>
          <img src="/discover_card_logo.svg" width={52} />
        </div>
      );
    }

    case "american-express": {
      return (
        <div>
          <img src="/american_express_card_logo.svg" width={36} />
        </div>
      );
    }

    default:
      return method;
  }
}

function fallback(value: any, fallback: any) {
  return value || fallback;
}

function withUnit(value: any, unit: string = "$") {
  return value ? `${unit}${value}` : "";
}

const Dots = () => {
  return (
    <div
      style={{
        display: "flex",
        gap: 2,
      }}
    >
      {Array(4)
        .fill(0)
        .map((i) => (
          <span
            style={{
              width: 4,
              height: 4,
              backgroundColor: "black",
              borderRadius: 40,
            }}
          ></span>
        ))}
    </div>
  );
};
