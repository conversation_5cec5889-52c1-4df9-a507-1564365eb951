import { useState } from "react";
import Button from "../Button";
import { Plus } from "../svgs";
import { formatDate } from "../utils/dateHelpers";
import { formatDateWithTime } from "../utils/formatDate";
import styles from "./notes-tile.module.scss";
import AddNotesModal from "../AddNotesModal";
import ViewNoteModal from "../ViewNoteModal";
import ViewAllNotesModal from "../ViewAllNotesModal";
import NoteSingle from "../NoteSingle";

const NotesTile = ({ sidebar, list, repopulate, ticketId }: any) => {
  const [show, setShow] = useState(false);
  const [viewNote, setViewNote] = useState(false);
  const [Note, setNote] = useState(null as any);
  const [viewAll, setViewAll] = useState(false);

  return (
    <>
      <AddNotesModal
        show={show}
        setShow={setShow}
        repopulate={repopulate}
        ticketId={ticketId}
      />
      <ViewNoteModal show={viewNote} setShow={setViewNote} item={Note} />
      <ViewAllNotesModal show={viewAll} setShow={setViewAll} data={list} />
      <div className={styles.notes}>
        <div className={styles.title}>
          <div className={styles.leftContainer}>
            <h4>Notes</h4>
            <p>
              {list.length} note{list.length !== 1 ? "s" : ""}
            </p>
            {sidebar && !!list.length && (
              <Button
                color="quaternary"
                onClick={() => {
                  setViewAll(true);
                }}
                style={{
                  padding: 0,
                  height: 24,
                  fontSize: 14,
                  marginLeft: 16,
                }}
              >
                See all
              </Button>
            )}
          </div>
          <Button
            style={{
              padding: 0,
              height: 24,
              fontSize: 14,
            }}
            color="tertiary"
            onClick={() => {
              setShow(true);
            }}
          >
            <Plus />
            Add Note
          </Button>
        </div>
        <div className={styles.notesContainer}>
          {list.length > 0 ? (
            sidebar ? (
              list
                .slice(0, 4)
                .map((item: any) => (
                  <NoteSingle
                    cutoff
                    setViewNote={setViewNote}
                    setNote={setNote}
                    item={item}
                  />
                ))
            ) : (
              list.map((item: any) => (
                <NoteSingle
                  setViewNote={setViewNote}
                  setNote={setNote}
                  item={item}
                />
              ))
            )
          ) : (
            <div className={styles.noneFound}>
              <img src="/none_found.svg" />
              <h3>No notes</h3>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default NotesTile;
