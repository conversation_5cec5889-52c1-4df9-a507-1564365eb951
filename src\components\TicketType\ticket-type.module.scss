@use "../../styles/theme.scss" as *;

.main {
  padding: 2px 10px;
  font-size: 12px;
  line-height: 18px;
  border-radius: 4px;
  width: auto;
  font-weight: 600 !important;
  background: $open;
  &.large {
    padding: 6px 16px;
  }
  &.hover:hover {
    box-shadow: 0 0 0 6px #fff;
  }
  /*&.status-0 {
    background-color: $invoice;
    color: $invoice-text;
  }
  &.status-1 {
    background-color: $payment;
    color: $payment-text;
  }
  &.status-2 {
    background-color: $billing;
    color: $billing-text;
  }
  &.status-3 {
    background-color: $maintenance;
    color: $maintenance-text;
  }
  &.status-4 {
    background-color: $complaints;
    color: $complaints-text;
  }*/
}
