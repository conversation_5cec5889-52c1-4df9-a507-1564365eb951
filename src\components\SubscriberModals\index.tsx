import { useState } from "react";
import Button from "../Button";
import ConfirmationModal from "../ConfirmationModal";
import Modal from "../Modal";
import SingleDatePicker from "../SingleDatePicker";
import styles from "./subscriber-modals.module.scss";
import { faker } from "@faker-js/faker";
import { Info } from "../svgs";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { removeHtml } from "../utils/removeHtml";

export const ConfirmResumeOrRestore = ({
  show,
  close,
  number,
  type,
  repopulate,
}: any) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const resumeSubscriber = () => {
    const endpointByType = {
      restore: "/accounts/activate",
      resume: "/accounts/resume",
    } as any;

    setLoading(true);
    ApiPostAuth(endpointByType[type], {
      subscriber: number,
    })
      .then((response) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulate();
        close();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Modal
      show={show}
      close={close}
      saveButton={`Yes, ${type} subscriber`}
      cancelButton="Cancel"
      proceed={resumeSubscriber}
      loading={loading}
      image="/bulk_edit_confirm_graphic.svg"
    >
      <h3 className={styles.text}>
        Are you sure you want to {type} subscription for number {number}?
      </h3>
    </Modal>
  );
};

export const ConfirmCancel = ({ show, close, number, repopulate }: any) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);

  const cancel = () => {
    setLoading(true);
    ApiPostAuth("/accounts/cancel", {
      subscriber: number,
    })
      .then((response) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        close();
        repopulate();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };
  return (
    <Modal
      show={show}
      close={close}
      saveButton="Yes, cancel subscriber"
      cancelButton="Cancel"
      proceed={cancel}
      loading={loading}
      image="/bulk_edit_confirm_graphic.svg"
    >
      <div className={styles.cancel}>
        <h3 className={styles.text}>
          Are you sure you want to cancel subscription for number {number}?
        </h3>
        <div className={styles.cancelTag}>
          <Info />
          This subscription will still be available under the same terms for 60
          days in case you need to "Re-Activate”
        </div>
      </div>
    </Modal>
  );
};

const UpdateSuspension = ({ show, close, number, loading }: any) => {
  const [date, setDate] = useState(faker.date.future(0.5));

  return (
    <Modal
      show={show}
      close={close}
      saveButton="Save Changes"
      cancelButton="Close Dialogue"
      proceed={() => {}}
      loading={loading}
      image="/bulk_edit_confirm_graphic.svg"
    >
      <div className={styles.tbs}>
        <h3>To be suspended</h3>
        <p>Number {number} is set to be suspended on </p>
        <SingleDatePicker date={date} setDate={setDate} />
        <div className={styles.buttons}>
          <Button
            style={{ marginRight: 12, maxWidth: "initial" }}
            color="secondary"
          >
            Suspend now
          </Button>
          <Button style={{ maxWidth: "initial" }} color="secondary">
            Cancel suspension
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export const ConfirmSuspend = ({ show, close, number, repopulate }: any) => {
  const dispatch = useDispatch();
  const [date, setDate] = useState(null as any);
  const [loading, setLoading] = useState(false);

  const format = (date: any) => {
    const year = date.getFullYear().toString();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const suspend = () => {
    setLoading(true);
    /*if (date !== null) {
      ApiPostAuth("/accounts/suspendonfuturedate", {
        subscriber: number,
        date: format(date),
      })
        .then((response) => {
          repopulate(() => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
          });
          close();
        })
        .catch((error) => {
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: error.response.data.message,
            },
          });
        });
    } else {*/
    ApiPostAuth("/accounts/suspend", {
      subscriber: number,
    })
      .then((response) => {
        setLoading(false);
        close();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulate();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
    /*}*/
  };

  return (
    <Modal
      show={show}
      close={close}
      saveButton="Suspend"
      cancelButton="Cancel"
      proceed={suspend}
      loading={loading}
      image="/bulk_edit_confirm_graphic.svg"
    >
      <div className={styles.suspend}>
        <h3>
          Suspend subscription for number
          <br />
          {number}?
        </h3>
        {/*<div className={styles.info}>
          <Info />
          To suspend immediately, leave Effective date field blank
        </div>
  <SingleDatePicker future date={date} setDate={setDate} />*/}
      </div>
    </Modal>
  );
};

const ModalGroup = ({
  state,
  dispatch,
  activeSub,
  loading,
  repopulate,
}: any) => {
  return (
    <>
      <ConfirmResumeOrRestore
        show={state.confirmResume}
        close={() => {
          dispatch({ type: "CLOSE_MODAL", modal: "confirmResume" });
        }}
        number={removeHtml(activeSub.subscriberNumber)}
        resumeSubscriber={() => {
          dispatch({ type: "RESUME_SUBSCRIBER", sub: activeSub });
        }}
        loading={loading}
        type="resume"
        repopulate={repopulate}
      />
      <ConfirmResumeOrRestore
        show={state.confirmRestore}
        close={() => {
          dispatch({ type: "CLOSE_MODAL", modal: "confirmRestore" });
        }}
        number={removeHtml(activeSub.subscriberNumber)}
        resumeSubscriber={() => {
          dispatch({ type: "RESUME_SUBSCRIBER", sub: activeSub });
        }}
        loading={loading}
        type="restore"
        repopulate={repopulate}
      />
      <ConfirmCancel
        show={state.confirmCancel}
        close={() => {
          dispatch({ type: "CLOSE_MODAL", modal: "confirmCancel" });
        }}
        number={removeHtml(activeSub.subscriberNumber)}
        cancelSubscriber={() => {
          dispatch({ type: "CANCEL_SUBSCRIBER", sub: activeSub });
        }}
        loading={loading}
        repopulate={repopulate}
      />
      <ConfirmSuspend
        show={state.confirmSuspend}
        close={() => {
          dispatch({ type: "CLOSE_MODAL", modal: "confirmSuspend" });
        }}
        number={removeHtml(activeSub.subscriberNumber)}
        repopulate={repopulate}
      />
      <UpdateSuspension
        show={state.tbs}
        close={() => {
          dispatch({ type: "CLOSE_MODAL", modal: "tbs" });
        }}
        number={removeHtml(activeSub.subscriberNumber)}
        loading={loading}
      />
    </>
  );
};

export default ModalGroup;
