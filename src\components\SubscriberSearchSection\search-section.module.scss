.main {
  background: #fff;
  width: 100%;
  border-radius: 24px;
  padding: 24px 32px;
  margin-bottom: 16px;
  .heading {
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 16px;
  }
  .inputs {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
    grid-column-gap: 15px;
  }
}

.controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  margin-bottom: 24px;
  .resultsCount {
    margin-right: 16px;
    color: #666666;
    font-size: 16px;
    line-height: 24px;
  }
}
