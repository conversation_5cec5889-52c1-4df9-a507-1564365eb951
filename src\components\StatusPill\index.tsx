import styles from "./status-pill.module.scss";

export const statuses = [
  "Inactive",
  "Active",
  "Suspended",
  "TBS",
  "Cancelled",
  "No Subscription",
  "Rejected",
  "Pending",
  "Ready",
  "ICCID Required",
  "BAN Change",
  "Swapping MDN",
];

const StatusPill = ({ status, hover }: any) => {
  return (
    <div
      className={`${styles.main} ${hover && styles.hover} ${
        styles[`status-${statuses.indexOf(status)}`]
      }`}
    >
      {status !== "" ? status : "No Status"}
    </div>
  );
};

export default StatusPill;
