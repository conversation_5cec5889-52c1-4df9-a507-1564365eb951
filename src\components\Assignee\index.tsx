import styles from "./assignee.module.scss";
import Initials from "../Initials";

const Assignee = ({ name, small }: any) => {
  const getInitials = (name: string) => {
    let names = name.split(" ");
    return names[0][0] + names[names.length - 1][0];
  };

  return (
    <div className={`${styles.main} ${small && styles.small}`}>
      <Initials small={small}>{getInitials(name)}</Initials>
      {name}
    </div>
  );
};

export default Assignee;
