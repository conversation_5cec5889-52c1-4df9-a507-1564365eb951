import styles from "./change-password-modal.module.scss";
import Modal from "../Modal";
import { FloppyDisk, SendPassword } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { Input } from "../Input";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import { validateAll } from "indicative/validator";
import { ApiPatch, ApiPostAuth } from "../../pages/api/api";

const fields = ["newPassword"];
const rules = getRules(fields);
const messages = getMessages(fields);

const ChangePasswordModal = ({ show, setShow, user, id }: any) => {
  console.log(user);
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(createStateObject(fields));

  const sendPasswordReset = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPatch("/users/edit", {
          userId: id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          enable2fa: user.is2faEnabled,
          newPassword: data.newPassword,
          roleId: user.roleName === "Agent" ? 1 : user.roleName === 'mvne' ? 3 : 2,
        })
          .then((response) => {
            setLoading(false);
            setShow(false);
            setData(createStateObject(fields));
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message:
                  error.response.data.message ||
                  "Something went wrong, please try again",
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      image="/edit_user_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={sendPasswordReset}
      loading={loading}
    >
      <div className={styles.main}>
        <h3>Change Password</h3>
        <Input
          label={labels.newPassword}
          placeholder={placeholders.newPassword}
          value={data.newPassword}
          onChange={(e: any) => {
            handleInputChange("newPassword", e, data, setData);
          }}
          error={data.errors.newPassword}
          onKeyDown={sendPasswordReset}
          password
          disabled={loading}
          clear={() => {
            clearInput("newPassword", setData);
          }}
          white
        />
      </div>
    </Modal>
  );
};

export default ChangePasswordModal;
