import styles from "./add-user.module.scss";
import Modal from "../Modal";
import { AddUser } from "../svgs";
import { Input } from "../Input";
import SelectInput from "../SelectInput";
import { useEffect, useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch } from "react-redux";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import { ApiGet, ApiPostAuth } from "../../pages/api/api";
import { useParams } from "react-router-dom";
import MultiSelectInput from "../MultiSelectInput";

const fields = ["firstName", "lastName", "email", "role"];
const rules = getRules(fields);
const messages = getMessages(fields);

const AddUserModal = ({ show, setShow, repopulateUsers }: any) => {
  const dispatch = useDispatch();

  const [data, setData] = useState(createStateObject(fields));

  const { mvnoId }: any = useParams();

  // Reset modal data when closed
  const reset = () => {
    setData(createStateObject(fields));
    setLoading(false);
  };

  // Convert role string to number
  const convertRole = (role: any) => {
    let roleId;
    if (role === "Admin") {
      roleId = 2;
    } else if (role === "Agent") {
      roleId = 1;
    }
    return roleId;
  };

  // Handles creation of new user
  const createUser = () => {
    const testData = {
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      email: data.email.trim(),
      role: data.role.trim(),
    };

    validateAll(testData, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPostAuth("/users/create", {
          firstName: testData.firstName,
          lastName: testData.lastName,
          email: testData.email,
          userRole: convertRole(testData.role),
          mvnoId: mvnoId,
          statusId: 1,
          channelIds: selectedChannels.map((channel: any) => channel.key),
        })
          .then((response) => {
            reset();
            repopulateUsers();
            dispatch({
              type: "notify",
              payload: {
                error: false,
                heading: "Success",
                message: response.data.message,
              },
            });
            setShow(false);
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                heading: "Something went wrong",
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const [loading, setLoading] = useState(false);

  const [channelOptions, setChannelOptions] = useState([] as any);
  const [selectedChannels, setSelectedChannels] = useState([] as any);

  useEffect(() => {
    ApiGet(`/channels/mvno/${mvnoId}`)
      .then((response) => {
        setChannelOptions(
          response.data.map((channel: any) => ({
            key: channel.id,
            label: channel.name,
          })),
        );
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  return (
    <Modal
      saveButton={
        <>
          <AddUser />
          Create User
        </>
      }
      image="/add_user_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={createUser}
      close={() => {
        setShow(false);
        setTimeout(() => {
          reset();
        }, 300);
      }}
      loading={loading}
      fullSize
      title="Add User"
    >
      <div className={`${styles.main} normal-select-input`}>
        {fields
          .filter((field) => field !== "role")
          .map((field) => (
            <Input
              key={`${field}-input`}
              label={labels[field]}
              placeholder={placeholders[field]}
              value={data[field]}
              onChange={(e: any) => {
                handleInputChange(field, e, data, setData);
              }}
              error={data.errors[field]}
              onKeyDown={createUser}
              clear={() => {
                clearInput(field, setData);
              }}
              disabled={loading}
              white
            />
          ))}
        <SelectInput
          placeholder="Role"
          options={["Agent", "Admin"]}
          selected={data.role}
          onChange={(value: any) => {
            setData({
              ...data,
              role: value,
              errors: {
                ...data.errors,
                role: "",
              },
            });
          }}
          disabled={loading}
          error={data.errors.role}
        />
        <div style={{ height: 12 }} />
        <MultiSelectInput
          key="channel-select"
          placeholder="Channels"
          options={channelOptions}
          selected={selectedChannels}
          onAdd={(value: any) => {
            setSelectedChannels((prev: any) => [...prev, value]);
          }}
          onRemove={(value: any) => {
            setSelectedChannels((prev: any) =>
              prev.filter((item: any) => item.key !== value.key),
            );
          }}
          disabled={loading}
          error={data.errors.role}
        />
      </div>
    </Modal>
  );
};

export default AddUserModal;
