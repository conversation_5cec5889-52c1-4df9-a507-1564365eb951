import styles from "./new-plans-tab.module.scss";
import { useState } from "react";
import PlanTile from "../PlanTile";
import PlanManageProvider from "../PlanManageContext";

type NewPlansTabProps = {
  subscriberAndPlansData: any;
  fetchSubscriberAndPlansData: () => void;
};

const NewPlansTab = ({
  subscriberAndPlansData,
  fetchSubscriberAndPlansData,
}: NewPlansTabProps) => {
  const [currentPlan, setCurrentPlan] = useState(null as any);

  const plans = subscriberAndPlansData.subscriptions;

  return (
    <>
      {/* Search, Filters, Sorts */}
      {/* <div className={styles.topContainer}>
        <div className={styles.search}>
          <SearchBar
            placeholder="Search by MDN, IMEI or ICCID"
            onSubmit={() => {}}
            query={""}
            setQuery={() => {}}
            loading={false}
          />
        </div>

        <div className={styles.filtersAndSorts}>
          <div className={styles.item}>
            <div className={styles.label}>Sort by</div>
            <MultiSelect
              label="Activation Date (Newest)"
              options={[]}
              selected={[]}
              setSelected={() => { }}
              darkerBg
            />
          </div>

          <div className={styles.item}>
            <div className={styles.label}>Filters</div>
            <MultiSelect
              label="Status (All)"
              options={[
                {
                  label: "Active",
                  key: "active",
                },
                {
                  label: "Inactive",
                  key: "inactive",
                },
              ]}
              selected={[]}
              setSelected={() => {}}
              darkerBg
            />
          </div>
        </div>
      </div> */}

      {/* Plans Cards Grid */}
      <div className={styles.plansGrid}>
        <PlanManageProvider
          plan={currentPlan}
          repopulate={fetchSubscriberAndPlansData}
          subscriberMid={subscriberAndPlansData?.mid}
        >
          {plans.map((plan: any) => (
            <PlanTile
              key={plan.id}
              plan={plan}
              subscriberMid={subscriberAndPlansData?.mid}
              setCurrentPlan={setCurrentPlan}
            />
          ))}
        </PlanManageProvider>
      </div>
    </>
  );
};

export default NewPlansTab;
