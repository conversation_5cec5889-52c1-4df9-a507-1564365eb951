import styles from "../../styles/user-management.module.scss";
import { Export } from "../../components/svgs";
import { useEffect, useState } from "react";
import Button from "../../components/Button";
import Pagination from "../../components/Pagination";
import {
  filterList,
  highlightSearch,
} from "../../components/utils/searchAndFilter";
import MultiSelect from "../../components/MultiSelect";
import UserSkeleton from "../../components/UserSkeleton";
import { useDispatch } from "react-redux";
import SearchSection from "../../components/SearchSection";
import { ApiGetNoAuth, ApiGetWithId } from "../api/api";
import { padArrayToLength } from "../../components/utils/padArray";
import RemoveFiltersBar from "../../components/RemoveFiltersBar";
import { useNavigate, useParams } from "react-router-dom";
import { getOrders, ordersFields } from "../../components/utils/ordersFields";
import { formatDateWithTime } from "../../components/utils/formatDate";
import SimTypeBadge from "../../components/SimTypeBadge";
import OrderStatusBadge from "../../components/OrderStatusBadge";
import AddOrderIccidModal from "../../components/AddOrderIccidModal";
import qs from "qs";
import SearchBar from "../../components/SearchBar";

const Orders = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [initialLoading, setInitialLoading] = useState(true);

  const { mvnoId } = useParams();

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  const productsPerPage = 8;

  const [queryDisplay, setQueryDisplay] = useState("");

  const [orders, setOrders] = useState([] as any);

  const repopulate = () => {
    setInitialLoading(true);
    setTimeout(() => {
      setInitialLoading(false);
    }, 1000);
  };

  const filterData = [
    {
      label: "Product type",
      key: "simType",
      // todo: use BE values
      options: ["SIM"],
    },
    {
      label: "Status",
      key: "status",
      // todo: use BE values
      options: ["ICCIDREQUIRED", "READY", "BANCHANGE"],
    },
  ];

  const emptyFilters = {
    simType: [],
    status: [],
  } as any;

  // Store for table column filters
  const [filters, setFilters] = useState(emptyFilters);

  const resetFilters = () => {
    setFilters(emptyFilters);
  };

  const [searchInputValue, setSearchInputValue] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = () => {
    setSearchQuery(searchInputValue);
    setCurrentPage(1);
  };

  useEffect(() => {
    const params = {
      currentPage: currentPage - 1,
      pageSize: productsPerPage,
      simType: filters.simType,
      status: filters.status,
      search: searchQuery,
    };
    const stringifiedQueryParams = qs.stringify(params, { indices: false });

    setInitialLoading(true);
    ApiGetWithId(
      `/app/orders${stringifiedQueryParams ? `?${stringifiedQueryParams}` : ""}`,
      mvnoId,
    )
      .then((response: any) => {
        setOrders(response.data.content);
        setTotalPages(response.data.totalPages);
        setInitialLoading(false);
      })
      .catch((error) => {
        setInitialLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message || "Something went wrong",
          },
        });
      });
  }, [currentPage, mvnoId, filters, searchQuery]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  const [showAddIccid, setShowAddIccid] = useState(false);
  const [activeOrder, setActiveOrder] = useState(null as any);

  return (
    <div className={styles.main}>
      <AddOrderIccidModal
        show={showAddIccid}
        setShow={setShowAddIccid}
        order={activeOrder}
        repopulate={repopulate}
      />
      <SearchBar
        placeholder="Search by Order No., Subscriber Name or IMEI"
        id="orders-search"
        onSubmit={handleSearch}
        query={searchInputValue}
        setQuery={setSearchInputValue}
      />
      <div className={styles.titleBar} style={{ margin: "25px 0 -8px 0" }}>
        <h3>Orders</h3>
      </div>
      <div className={`${styles.usersPanel} ${styles.products}`}>
        <div className={`${styles.filters} menu-select select`}>
          <div className={styles.label}>Filters</div>
          {filterData.map((filterType) => (
            <MultiSelect
              key={`filter-${filterType.key}`}
              label={filterType.label}
              options={filterType.options.map((option: any) => ({
                key: option,
                label:
                  filterType.key === "status" ? (
                    <OrderStatusBadge status={option} flat />
                  ) : filterType.key === "simType" ? (
                    <SimTypeBadge status={option} />
                  ) : (
                    option
                  ),
              }))}
              selected={filters[filterType.key]}
              setSelected={(state: any) => {
                setFilters({
                  ...filters,
                  [filterType.key]: state,
                });
              }}
            />
          ))}
        </div>
        <RemoveFiltersBar
          filters={filters}
          setFilters={setFilters}
          resetFilters={resetFilters}
          grey
          orders
        />
        <div className={`${styles.tableContainer} table-scroll`}>
          <table>
            <thead>
              <tr>
                {ordersFields.map((field: any) => (
                  <th>{field.labelStr}</th>
                ))}
                <th></th>
              </tr>
            </thead>
            <tbody>
              {!initialLoading ? (
                orders.length !== 0 ? (
                  padArrayToLength(orders, productsPerPage, null).map(
                    (singleProduct: any, index: any) =>
                      singleProduct === null ? (
                        <tr
                          key={`filler-${index}`}
                          style={{ background: "none", pointerEvents: "none" }}
                        ></tr>
                      ) : (
                        <tr
                          key={"prod-row-" + singleProduct.ProductName + index}
                        >
                          {ordersFields.map((field: any) => {
                            if (field.key === "dateAndTime") {
                              return (
                                <td>
                                  {formatDateWithTime(singleProduct[field.key])}
                                </td>
                              );
                            } else if (field.key === "simType") {
                              return (
                                <td>
                                  <SimTypeBadge
                                    status={singleProduct[field.key]}
                                  />
                                </td>
                              );
                            } else if (field.key === "status") {
                              return (
                                <td>
                                  <OrderStatusBadge
                                    status={singleProduct[field.key]}
                                  />
                                </td>
                              );
                            } else if (field.key === "offerName") {
                              return (
                                <td>
                                  {singleProduct.product[field.key] || "-"}
                                </td>
                              );
                            } else if (
                              field.key === "orderNumber" ||
                              field.key === "subscriberName" ||
                              field.key === "imei"
                            ) {
                              return (
                                <td>
                                  {highlightSearch(
                                    singleProduct[field.key],
                                    searchQuery,
                                  )}
                                </td>
                              );
                            } else {
                              return <td>{singleProduct[field.key] || "-"}</td>;
                            }
                          })}
                          <td>
                            <Button
                              color="quaternary"
                              style={{ padding: 0, maxWidth: "initial" }}
                              onClick={() => {
                                navigate(
                                  `/${mvnoId}/orders/${singleProduct.orderNumber}`,
                                );
                                dispatch({
                                  type: "set",
                                  order: singleProduct,
                                });
                              }}
                            >
                              Manage
                            </Button>
                          </td>
                        </tr>
                      ),
                  )
                ) : (
                  <tr style={{ background: "none" }}>
                    <td colSpan={100}>
                      <div className={styles.noneFound}>
                        <img src="/none_found.svg" />
                        <h3>
                          We couldn't find anything matching
                          {searchQuery ? <>" {searchQuery}"</> : "."}
                        </h3>
                      </div>
                    </td>
                  </tr>
                )
              ) : (
                Array.from({ length: productsPerPage }, (v, i) => i).map(
                  (i) => (
                    <UserSkeleton key={"user-skeleton-" + i} noOfStandard={9} />
                  ),
                )
              )}
            </tbody>
          </table>
        </div>
        <div className={styles.pagination}>
          <Pagination
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            numberOfPages={totalPages}
          />
        </div>
      </div>
    </div>
  );
};

export default Orders;
