@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
}

.titleBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  h3 {
    margin: 0;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
  }
}

.infoButton {
  background: none;
  border: none;
  padding: 0px;
  margin-left: 12px;
  cursor: pointer;
  svg {
    width: 22px;
    height: 22px;
    vertical-align: middle;
  }
}

.logs {
  overflow-x: auto;
  td,
  th {
    white-space: nowrap;
  }
}

.tableContainer {
  overflow-x: auto;
  padding-bottom: 5px;
}

.usersPanel {
  background: #fff;
  width: 100%;
  border-radius: 24px;
  margin-top: 32px;
  padding: 24px 40px;
  min-height: 746px;
  display: flex;
  flex-direction: column;
  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0px 0px;
    white-space: nowrap;
    tbody {
      tr {
        height: 45px;
        background: #f2f2f2;
        &:nth-child(odd) {
          background: #fff;
        }
        td {
          font-size: 14px;
          font-weight: 400;
          line-height: 21px;
          padding: 0 16px;
          svg {
            vertical-align: middle;
          }
        }
        td:first-child {
          border-radius: 16px 0 0 16px;
          padding-left: 24px;
        }
        td:last-child {
          border-radius: 0 16px 16px 0;
          padding-right: 24px;
        }
      }
      &:before {
        content: "@";
        display: block;
        line-height: 0px;
        text-indent: -99999px;
      }
    }
    thead {
      tr {
        th:first-child {
          padding-left: 24px;
        }
      }
    }
    th {
      font-size: 14px;
      font-weight: 500;
      line-height: 21px;
      text-align: start;
      border-bottom: 1px solid $disabled;
      padding: 0 16px;
      padding-bottom: 19px;
    }
  }
}

.pagination {
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: auto;
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 55px;
  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }
  h3 {
    width: 100%;
    text-align: center;
  }
}
