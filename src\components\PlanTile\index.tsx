import { useMemo } from "react";
import styles from "./plan-tile.module.scss";
import { Link, useParams } from "react-router-dom";
import PlanStatus from "../PlanStatus";
import { getStatusesForPlan } from "../utils/planUtils";
import PlanManageMenu from "../PlanManageMenu";
import formatDate from "../utils/formatDate";
import { UsersThree } from "../svgs";

type PlanCardProps = {
  plan: any;
  subscriberMid: string;
  setCurrentPlan: (plan: any) => void;
};

const PlanTile = ({ plan, subscriberMid, setCurrentPlan }: PlanCardProps) => {
  const subscriptionProperties = useMemo(() => getPlanProperties(plan), [plan]);
  const mvnoId = useParams().mvnoId;
  const subscriptionTypeURLSearchParam =
    getSubscriptionTypeURLSearchParam(plan);
  const isPorting =
    plan.type === "tempPortingProcess" || plan.type === "activePortingProcess";

  const planDetailsPath = `/${mvnoId}/subscription-details/${subscriberMid}/${plan.id}?type=${subscriptionTypeURLSearchParam}${isPorting ? "&porting=true" : ""}`;

  return (
    <div className={styles.container}>
      <div className={styles.top}>
        {/* Labels */}
        <PlanStatus plan={plan} />

        {/* Manage Button and Dropdown */}
        <PlanManageMenu plan={plan} setCurrentPlan={setCurrentPlan} />
      </div>

      {/* Family plan badge */}
      {plan.familyPlanId && (
        <Link
          className={styles.familyPlanBadge}
          to={`${planDetailsPath}&tab=manage-family`}
        >
          <UsersThree />
          <span>{`Member of ${plan.familyPlanAdmin}'s Family`}</span>
        </Link>
      )}

      {/* Subscription Details */}
      <div className={styles.details}>
        <h3>{plan.retailName}</h3>
        <div className={styles.detailsRow}>
          <div>
            MDN:{" "}
            {plan.subscriberNumber || plan.cancelledMdn || plan.mdn || "N/A"}
          </div>
          <div>Nickname: {plan.mdnNickname || "N/A"}</div>
        </div>

        <div className={styles.description}>{plan.product?.product}</div>

        <div className={styles.properties}>
          {subscriptionProperties.map((prop) => (
            <div key={prop.title} className={styles.property}>
              <div className={styles.title}>{prop.title}</div>
              <div className={styles.value}>{prop.value}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Use Link */}
      <Link to={planDetailsPath}>
        <button className={styles.seeDetailsBtn}>See Details</button>
      </Link>
    </div>
  );
};

export default PlanTile;

function getPlanProperties(plan: any) {
  const statuses = getStatusesForPlan(plan);

  const showCreationDate = plan.type === "activeSubscription";
  const showActivationDate = plan.type === "activeSubscription";
  const showBillCycle = plan.type === "activeSubscription";
  const showPlanSize = plan.type === "activeSubscription";
  const showDataBalance = plan.type === "activeSubscription";
  const showServiceType = plan.type === "activeSubscription";
  const showBAN = plan.type === "activeSubscription";
  const showOldCarrierDetails =
    statuses.includes("ready-to-activate-temp-portin") ||
    (plan.type === "tempPortingProcess" &&
      plan.billingAccountNumber &&
      plan.billingAccountPassword);

  return [
    ...((showCreationDate && [
      {
        title: "Creation Date",
        value: formatDate(plan.creationDate) || "-",
      },
    ]) ||
      []),
    ...((showActivationDate && [
      {
        title: "Activation Date",
        value: formatDate(plan.activationDate) || "-",
      },
    ]) ||
      []),
    ...((showBillCycle && [
      {
        title: "Bill Cycle",
        value: formatDate(plan.nextBillCycleDate) || "-",
      },
    ]) ||
      []),
    ...((showPlanSize && [
      {
        title: "Plan Size",
        value: `${plan.planSizeGB} GB` || "-",
      },
    ]) ||
      []),
    ...((showDataBalance && [
      {
        title: "Data Balance",
        value: `${plan.dataBalanceGB} GB` || "-",
      },
    ]) ||
      []),
    ...((showServiceType && [
      {
        title: "Service Type",
        value: plan.serviceType || "-",
      },
    ]) ||
      []),
    {
      title: "ICCID",
      value: plan.iccid || "-",
    },
    {
      title: "IMEI",
      value: plan.imei || "-",
    },
    ...((showBAN && [
      {
        title: "BAN",
        value: plan.billingAccountNumber || "-",
      },
    ]) ||
      []),
    ...((showOldCarrierDetails && [
      {
        title: "Old carrier details",
        value: (
          <div>
            <div>
              <div>PIN: {plan.billingAccountPassword || "-"}</div>
              <div>Acct. No.: {plan.billingAccountNumber || "-"}</div>
            </div>
          </div>
        ),
      },
    ]) ||
      []),
  ];
}

function getSubscriptionTypeURLSearchParam(plan: any) {
  if (
    plan.type === "activeSubscription" ||
    plan.type === "activePortingProcess"
  ) {
    return "active";
  }
  if (plan.type === "tempSubscription" || plan.type === "tempPortingProcess") {
    return "temp";
  }
  return "uknown";
}
