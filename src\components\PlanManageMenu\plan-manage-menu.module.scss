.manageBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f1f1f1;
  border-radius: 16px;
  padding: 10px 16px;
  border: none;
  cursor: pointer;
  font-weight: 600;
  color: #000;

  &:hover {
    background-color: #f5f5f5;
  }
}

.container {
  position: relative;
}

:global {
  .subscription-manage-menu .szh-menu {
    border-radius: 24px;
    padding: 16px;
  }
}

.menuItem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: #000;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    background: #fff5eb;
  }

  svg {
    width: 20px;
    height: 20px;
  }
}
