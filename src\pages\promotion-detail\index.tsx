import { useCallback, useEffect, useRef, useState } from "react";
import SearchSection from "../../components/SearchSection";
import styles from "../../styles/promotion-detail.module.scss";
import { Link, useParams } from "react-router-dom";
import { useDispatch } from "react-redux";
import EditPromotionModal from "../../components/EditPromotionModal";
import { ApiGet } from "../api/api";
import Spinner from "../../components/Spinner";
import Button from "../../components/Button";
import HourGlassIcon, { Delete, Export, Pencil } from "../../components/svgs";
import TrueFalseStatus from "../../components/TrueFalseStatus";
import { formatDateWithTime } from "../../components/utils/formatDate";
import DeletePromotionModal from "../../components/DeletePromotionModal";
import {
  formatPromoAmount,
  formatType,
} from "../../components/utils/promoUtils";
import { motion } from "framer-motion";
import UsageHistoryTab from "./usage-history-tab";
import ActivityLogTab from "./activity-log-tab";

const PromotionDetailPage = () => {
  const { mvnoId, id } = useParams();
  const [detailsLoading, setDetailsLoading] = useState(true);

  const [fetchError, setFetchError] = useState(false);

  const [promoDetails, setPromoDetails] = useState(null as any);

  const dispatch = useDispatch();

  const [showEditModal, setShowEditModal] = useState(false);

  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const fetchPromotionDetails = useCallback(() => {
    setDetailsLoading(true);
    setFetchError(false);
    ApiGet(`/promotions/${id}`)
      .then((response) => {
        setPromoDetails(response.data);
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "Something went wrong",
            message: error.response?.data?.message || "Unknown error.",
          },
        });
        setFetchError(true);
      })
      .finally(() => {
        setDetailsLoading(false);
      });
  }, [id]);

  useEffect(() => {
    fetchPromotionDetails();
  }, []);

  const tabs = [
    {
      label: "Usage History",
      key: "usage-history",
    },
    {
      label: "Activity Log",
      key: "activity-log",
    },
  ] as const;
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const activeTab = tabs[activeTabIndex];

  const activityLogRef = useRef<{ refresh: () => void }>(null);
  const refreshActivityLog = () => {
    activityLogRef.current?.refresh();
  };

  return (
    <>
      <div className={styles.main}>
        <SearchSection
          data={[]}
          setFilteredData={() => {}}
          setQueryDisplay={() => {}}
          placeholder="Search"
          id="fee-activity-log-search"
          setCurrentPage={() => {}}
        />
        <EditPromotionModal
          show={showEditModal}
          setShow={setShowEditModal}
          promoDetails={promoDetails}
          refresh={() => {
            fetchPromotionDetails();
            refreshActivityLog();
          }}
        />
        <DeletePromotionModal
          show={showDeleteModal}
          setShow={setShowDeleteModal}
          promoDetails={promoDetails}
        />

        {detailsLoading ? (
          <div
            style={{
              display: "flex",
              height: "50vh",
              alignItems: "center",
              justifyContent: "center",
            }}
            className={`${styles.container} ${styles.loadingView}`}
          >
            <Spinner />
          </div>
        ) : fetchError ? (
          <div className={styles.loadingError}>
            <img src="/error_robot.svg" />
            <div>
              <h3>An unexpected error occurred</h3>
              <p>Please try again later</p>
            </div>
          </div>
        ) : (
          <>
            <div className={styles.header}>
              <div className={styles.breadcrumbs}>
                <Link to={`/${mvnoId}/promotions`}>Promotions </Link>
                <span>/ {promoDetails?.name}</span>
              </div>

              <div className={styles.actionButtons}>
                <Button
                  color="danger"
                  onClick={() => {
                    setShowDeleteModal(true);
                  }}
                >
                  <Delete />
                  Delete
                </Button>
                <Button
                  onClick={() => {
                    setShowEditModal(true);
                  }}
                >
                  <Pencil />
                  Edit Details
                </Button>
              </div>
            </div>
            <div className={styles.container}>
              <div className={styles.detailTop}>
                <TrueFalseStatus status={promoDetails?.status as any} />
                <div className={styles.detailDates}>
                  <p>
                    Start Date:{" "}
                    {formatDateWithTime(new Date(promoDetails?.startDate))}
                  </p>
                  <p className={styles.expiry}>
                    <HourGlassIcon />
                    <span>
                      Expiry:{" "}
                      {formatDateWithTime(new Date(promoDetails?.expiryDate))}
                    </span>
                  </p>
                </div>
              </div>

              <h1>{promoDetails?.name}</h1>
              <div className={`${styles.detailDates} ${styles.plain}`}>
                <p>
                  Created On:{" "}
                  {formatDateWithTime(new Date(promoDetails?.dateCreated))}
                </p>
                <p>
                  Last Modified:{" "}
                  {formatDateWithTime(new Date(promoDetails?.lastModified))}
                </p>
              </div>
              <div className={styles.detailItemCardsContainer}>
                <div className={styles.detailItemCard}>
                  <span>Promotion Type</span>
                  <h3>{formatType(promoDetails?.type)}</h3>
                </div>
                <div className={styles.detailItemCard}>
                  <span>Fee to Discount</span>
                  <h3 style={{ textTransform: "capitalize" }}>
                    {promoDetails?.feeToDiscount}
                  </h3>
                </div>
                <div className={styles.detailItemCard}>
                  <span>Promo Code</span>
                  <h3>{promoDetails?.promoCode}</h3>
                </div>
                <div className={styles.detailItemCard}>
                  <span>Amount</span>
                  <h3>{formatPromoAmount(promoDetails)}</h3>
                </div>
                <div className={styles.detailItemCard}>
                  <span>Times Used</span>
                  <h3> {promoDetails?.timesUsed}</h3>
                </div>
              </div>
            </div>
          </>
        )}

        <div className={`${styles.container} ${styles.tabsContainer}`}>
          <div className={styles.tabs}>
            {tabs.map((tab, i) => (
              <div
                className={`${styles.tab} ${
                  activeTabIndex === i && styles.activeTab
                }`}
                onClick={() => {
                  setActiveTabIndex(i);
                }}
              >
                <span>{tab.label}</span>
                {activeTabIndex === i && (
                  <motion.div
                    className={styles.tabBackground}
                    layoutId="underline"
                  />
                )}
              </div>
            ))}
            {activeTab.key === "usage-history" && (
              <Button
                onClick={() => {
                  // exportCsvPlainFields(
                  //   Object.keys(filterdProducts[0]),
                  //   filterdProducts,
                  //   dispatch,
                  //   "product_catalogue",
                  // );
                }}
                style={{ marginLeft: "auto" }}
                // disabled={initialLoading}
              >
                <Export />
                Export to CSV
              </Button>
            )}
          </div>
          <div className={styles.tabContent}>
            {activeTab.key === "usage-history" && (
              <UsageHistoryTab promoId={id!} />
            )}
            {activeTab.key === "activity-log" && (
              <ActivityLogTab
                promoId={id!}
                ref={activityLogRef}
                // promoDetails={promoDetails}
                // setShowEditModal={setShowEditModal}
              />
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default PromotionDetailPage;
