@use "../../styles/theme.scss" as *;

.menuButton {
  border: none;
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  gap: 4px;
  cursor: pointer;
  transition: color 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &:hover {
    color: $orange;
  }
  svg {
    display: inline;
    vertical-align: middle;
    transition: transform 0.2s ease;
    margin-left: 5px;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }
}

.box {
  height: 100%;
}

.switchContainer {
  height: 50px;
  border-radius: 1000px;
  display: grid;
  grid-template-columns: 67px 67px 67px;
  grid-template-rows: 1fr;
  background: #fcc9a5;
  align-items: center;
  justify-items: center;
  position: relative;
  .unit {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1000;
    transition: all 0.3s ease;
    cursor: pointer;
    &.active {
      color: #fff;
      cursor: auto;
    }
  }
  .thumb {
    position: absolute;
    width: 67px;
    height: 100%;
    background: $orange;
    border-radius: 1000px;
    z-index: 800;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
  }
}
