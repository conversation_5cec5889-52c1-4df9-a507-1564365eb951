import { useState } from "react";
import Pagination from "../Pagination";
import styles from "./table-control.module.scss";
import { ClickAwayListener } from "@mui/material";

const TableControl = ({
  itemsPerPage,
  setItemsPerPage,
  currentPage,
  setCurrentPage,
  numberOfPages,
  label,
}: any) => {
  const [showMenu, setShowMenu] = useState(false);

  return (
    <div className={styles.pagination}>
      <div className={styles.ticketsPerPage}>
        <span>Showing</span>
        <ClickAwayListener
          onClickAway={() => {
            setShowMenu(false);
          }}
        >
          <div style={{ position: "relative" }}>
            <div
              className={`${styles.ticketsPerPageButton} ticketsPerPageButton`}
              onClick={() => {
                setShowMenu((prev: boolean) => !prev);
              }}
            >
              {itemsPerPage}
            </div>
            {[5, 15, 45, 75, 100]
              .filter((item) => item !== itemsPerPage)
              .map((num, index) => (
                <div
                  className={`${styles.ticketsShownMenu} ${
                    showMenu && styles[`ticketsShownMenu${index}`]
                  } ticketsPerPageButton`}
                  onClick={() => {
                    setShowMenu(false);
                    setItemsPerPage(num);
                    setCurrentPage(1);
                  }}
                  key={`tickets-per-page-` + num}
                >
                  {num}
                </div>
              ))}
          </div>
        </ClickAwayListener>
        <span>{label} per page</span>
      </div>
      <Pagination
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        numberOfPages={numberOfPages}
      />
    </div>
  );
};

export default TableControl;
