import styles from "./menu.module.scss";
import {
  ControlledMenu,
  MenuItem,
  useClick,
  useMenuState,
} from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown } from "../svgs";
import { useRef } from "react";
import { Link } from "react-router-dom";

const Menu = ({ data }: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });
  const anchorProps = useClick(menuProps.state, toggleMenu);

  return (
    <div className={`${styles.box} menu`}>
      <div
        ref={ref}
        {...anchorProps}
        className={`${styles.menuButton} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        }`}
      >
        {data.label}
        <ChevronDown />
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        align="end"
        position="auto"
        viewScroll="auto"
      >
        {data.items.map((item: any, index: number) => (
          <Link
            to={item.link}
            style={{
              textDecoration: "none",
              marginBottom: index === data.items.length - 1 ? 0 : 16,
            }}
            key={"menu-" + item.label}
          >
            <MenuItem onClick={item.onClick} className={styles.menuItem}>
              {item.icon}
              {item.label}
            </MenuItem>
          </Link>
        ))}
      </ControlledMenu>
    </div>
  );
};

export default Menu;
