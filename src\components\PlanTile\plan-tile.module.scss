.container {
  border-radius: 24px;
  padding: 24px;
  min-width: 0;
  background-color: #fff;
}

.top {
  display: flex;
  justify-content: space-between;
  gap: 24px;
  align-items: flex-start;
}

.familyPlanBadge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  background-color: #f2f2f2;
  font-size: 14px;
  margin-top: 12px;
  width: fit-content;
  cursor: pointer;
  color: #000014;
  text-decoration: none;

  &:hover {
    background-color: #e8e8e8;
  }
}

.details {
  margin-top: 12px;

  h3 {
    margin-bottom: 0;
  }

  .detailsRow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    gap: 24px;
  }

  .description {
    margin-top: 4px;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
  }
}

.properties {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(128px, 1fr));
  column-gap: 24px;
  row-gap: 16px;
  margin-top: 12px;
}

.property {
  .title {
    color: #666;
    font-size: 12px;
  }

  .value {
    color: #000;
    font-size: 14px;
    word-break: break-all;
  }
}

.seeDetailsBtn {
  width: 100%;
  padding: 10px 16px;
  background-color: #f1f1f1;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  font-weight: 600;
  margin-top: 16px;
  font-size: 14px;
  width: fit-content;

  &:hover {
    background-color: #f5f5f5;
  }
}
