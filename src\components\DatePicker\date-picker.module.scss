@use "../../styles/theme.scss" as *;

.main {
  width: 388px;
  background: #fff;
  padding: 24px;
  border-radius: 12px;
}

.menuButton {
  border: none;
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  gap: 4px;
  cursor: pointer;
  transition: color 0.2s ease, background-color 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &.reports {
    &:hover {
      color: $black;
    }
  }
  &:hover {
    color: $orange;
    &.background {
      color: $black;
      background: $light-orange;
    }
  }
  &.background {
    height: 32px;
    background-color: #f7f6f6;
    padding: 5.5px 6px 5.5px 12px;
    border-radius: 6px;
    font-weight: 500;
    margin-right: 12px;
  }
  svg {
    display: inline;
    vertical-align: middle;
    transition: transform 0.2s ease;
    margin-left: 5px;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }
}

.buttons {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px 16px 16px;
  user-select: none;
}

.prevNext {
  display: flex;
  align-items: center;
  svg {
    vertical-align: middle;
  }
  .prev {
    margin-right: 16px;
  }
  .prev,
  .next {
    cursor: pointer;
    transition: color 0.1s ease;
    &:hover {
      color: $orange;
    }
  }
}

.input {
  width: 100%;
  height: 56px;
  border: 1px solid #74767e;
  border-radius: 8px;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: $placeholder;
  svg {
    margin-right: 8px;
  }
}

.mainDatePicker {
  width: 100%;
  border: 1px solid #74767e;
  border-radius: 8px;
  .fromUntil {
    display: grid;
    grid-template-columns: 1fr 1fr;
    .selection {
      width: 100%;
      height: 47px;
      display: flex;
      align-items: center;
      justify-content: center;
      &.active {
        background: $orange;
        color: #fff;
      }
    }
  }
}

.calendar {
  padding: 16px 0;
  user-select: none;
  .days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    width: 100%;
    justify-items: center;
    .letter {
      color: $black;
      font-size: 12px;
      line-height: 24px;
      padding: 9px 16px;
    }
  }
}

.datesGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(6, 42px);
  width: 100%;
  justify-content: stretch;
  align-items: center;
  .cellContainer {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    &:hover {
      .day {
        background-color: $light-orange;
      }
    }
  }
  .gridCell {
    position: absolute;
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    &.highlight {
      background-color: $light-orange;
    }
    &.curveLeft {
      width: 50%;
      right: 0px;
    }
    &.curveRight {
      width: 50%;
      left: 0px;
    }
  }
  .day {
    font-size: 12px;
    line-height: 24px;
    height: 32px;
    width: 32px;
    border-radius: 1000px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    z-index: 20;
    &.now {
      border: 1px solid $orange;
    }
    &.active {
      color: #fff;
      background-color: $orange;
      cursor: auto;
      &:hover {
        background-color: $orange;
      }
    }
    &.pad {
      color: #74767e;
      &.active {
        color: #fff;
      }
    }
  }
}

.disable {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}
