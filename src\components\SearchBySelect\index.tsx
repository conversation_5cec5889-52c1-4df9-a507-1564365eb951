import styles from "./select.module.scss";
import Select from "react-select";

const SearchBySelect = ({
  value,
  onChange,
  options,
  disabled,
  placeholder,
  dropDownMaxHeight,
}: any) => {
  const root = getComputedStyle(document.getElementById("root")!);
  const customStyles = {
    control: (baseStyles: any, state: any) => ({
      ...baseStyles,
      zIndex: 300,
      height: 56,
      marginBottom: 0,
      borderRadius: 1000,
      borderWidth: 1.5,
      borderColor: disabled
        ? "rgba(255, 255, 255, 0.5)"
        : state.isFocused
        ? root.getPropertyValue("--orange")
        : "#fff",
      boxShadow: state.isFocused ? "rgba(0, 0, 0, 0.16) 0px 1px 4px;" : "none",
      color: "#000",
      background: "#fff",
      "&:hover": {
        borderColor: state.isFocused
          ? root.getPropertyValue("--orange")
          : "#fff",
      },
    }),
    valueContainer: (baseStyles: any, state: any) => ({
      ...baseStyles,
      padding: "0px 24px",
      color: disabled ? "rgba(26, 26, 26, 0.38)" : "#000",
      borderRadius: "0px 0px 28px 28px",
    }),
    menu: (baseStyles: any, state: any) => ({
      ...baseStyles,
      border: "none",
      marginTop: 0,
      zIndex: 200,
      borderRadius: "0px 0px 28px 28px",
      top: 28,
      paddingTop: 38,
    }),
    menuList: (baseStyles: any, state: any) => ({
      ...baseStyles,
      padding: 0,
      maxHeight: dropDownMaxHeight,
      borderRadius: "0px 0px 28px 28px",
      "::-webkit-scrollbar": {
        width: "10px",
        height: "0px",
        borderRadius: "9999px",
      },
      "::-webkit-scrollbar-track": {
        background: "#fde5d4",
        borderRadius: "9999px",
      },
      "::-webkit-scrollbar-thumb": {
        background: "#f47d27",
        borderRadius: "9999px",
      },
      "::-webkit-scrollbar-thumb:hover": {
        background: "#f47d27",
      },
    }),
    option: (baseStyles: any, state: any) => ({
      ...baseStyles,
      height: 56,
      padding: "0 16px",
      lineHeight: "56px",
      color: state.isSelected ? "#fff" : "#000",
      background: state.isSelected ? root.getPropertyValue("--orange") : "#fff",
      display: "flex",
      alignItems: "center",
      "&:hover": {
        background: state.isSelected
          ? root.getPropertyValue("--orange")
          : root.getPropertyValue("--light-orange"),
      },
    }),
    indicatorSeparator: (baseStyles: any, state: any) => ({
      ...baseStyles,
      display: "none",
    }),
    indicatorsContainer: (baseStyles: any, state: any) => ({
      ...baseStyles,
      padding: "8px",
    }),
  };

  return (
    <div className={styles.selectContainer}>
      <Select
        styles={customStyles}
        value={value}
        onChange={onChange}
        options={options}
        placeholder={placeholder}
        isDisabled={disabled}
      />
    </div>
  );
};

export default SearchBySelect;
