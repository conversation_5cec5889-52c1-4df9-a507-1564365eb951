@use "../../styles/theme.scss" as *;

.main {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 6px 0 12px;
  margin: 0 12px 12px 0;
  height: 32px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  color: $black;
  background: #fff;
  &.grey {
    background: #f7f6f6;
  }
  &.sim {
    background-color: $sim;
  }
  &.esim {
    background-color: $esim;
  }
  &.ticketCategory {
    background-color: $open;
    color: $black;
  }
  &.cat-invoice {
    background-color: $invoice;
    color: $invoice-text;
  }
  &.cat-payment {
    background-color: $payment;
    color: $payment-text;
  }
  &.cat-billingissues {
    background-color: $billing;
    color: $billing-text;
  }
  &.cat-maintenanceanddowntime {
    background-color: $maintenance;
    color: $maintenance-text;
  }
  &.cat-complaintsandfeedback {
    background-color: $complaints;
    color: $complaints-text;
  }
  &.ICCIDREQUIRED,
  &.ICCIDANDPORTINREQUIRED,
  &.PORTINREQUIRED {
    background-color: $iccid-required;
    color: $black;
  }
  &.READY {
    background-color: $ready;
    color: #fff;
  }
  &.BANCHANGE {
    background-color: $ban-change;
    color: $black;
  }
}

.remove {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.1s ease;
}

.active {
  color: $off-white;
  background-color: $active;
}

.suspended {
  color: $black;
  background-color: $suspended;
}

.tbs {
  color: $black;
  background-color: $tbs;
}

.cancelled {
  color: $off-white;
  background-color: $cancelled;
}

.noSub {
  color: $black;
  background-color: #e0dcdc;
}

.noStatus {
  color: $black;
  background: none;
  border: 1px solid $black;
}

.pending {
  color: $black;
  background-color: #f8ec7e;
}

.rejected {
  color: $black;
  background-color: $rejected;
}
//new statuses
.open {
  color: $black;
  background-color: $open;
}

.st_pending {
  color: $black;
  background-color: $pending;
}

.resolved {
  color: $off-white;
  background-color: $resolved;
}

.closed {
  color: $off-white;
  background-color: $closed;
}

.iccidRequired {
  color: $black !important;
  background-color: $iccid-required !important;
}

.ready {
  color: $off-white !important;
  background-color: $ready !important;
}

.banChange {
  color: $black !important;
  background-color: $ban-change !important;
}

.mdnChange {
  color: $black;
  background-color: #eed922;
}
