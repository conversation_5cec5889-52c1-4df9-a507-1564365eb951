import styles from "./search-bar.module.scss";
import { MagnifyingGlass } from "../svgs";
import Button from "../Button";
import { useEffect, useState } from "react";
import { Fade } from "@mui/material";

const SearchBar = ({
  placeholder,
  onSubmit,
  id,
  loading,
  query,
  setQuery,
  grey,
  small,
}: any) => {
  const [showButton, setShowButton] = useState(false);

  const handleFocus = () => {
    setShowButton(true);
  };

  const handleBlur = () => {
    setShowButton(false);
  };

  useEffect(() => {
    if (query === "") {
      onSubmit();
    }
  }, [query]);

  return (
    <div
      className={`${styles.main} ${small && styles.small} ${
        grey && styles.grey
      }`}
    >
      <MagnifyingGlass
        color={loading ? "rgba(26, 26, 26, 0.38)" : "currentColor"}
      />
      <input
        placeholder={placeholder}
        className={`${styles.input} ${grey && styles.grey}`}
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onKeyDown={(event) => {
          if (event.key === "Enter") {
            event.preventDefault();
            onSubmit();
          }
        }}
        disabled={loading}
        id={id}
        onFocus={handleFocus}
        onBlur={handleBlur}
      />
      <div
        className={`${styles.highlight} ${loading && styles.loadingHighlight}`}
      />
      <Fade in={showButton || loading} unmountOnExit>
        <div style={{ height: "100%" }}>
          <Button
            color="search"
            style={{ minWidth: "initial", maxWidth: 134, width: "100%" }}
            onClick={() => {
              onSubmit();
            }}
            loading={loading}
          >
            Search
          </Button>
        </div>
      </Fade>
    </div>
  );
};

export default SearchBar;
