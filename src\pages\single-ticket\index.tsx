import { Link, useNavigate, useParams } from "react-router-dom";
import styles from "../../styles/tickets.module.scss";
import TicketPageSummary from "../../components/TicketPageSummary";
import CorrespondenceTile from "../../components/CorrespondenceTile";
import NotesTile from "../../components/NotesTile";
import DIDTile from "../../components/DIDTile";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { CaretLeft, CaretRight } from "../../components/svgs";
import { ApiGet, ApiPostAuth } from "../api/api";
import AttachmentsTile from "../../components/AttachmentsTile";
import TicketSidebarTile from "../../components/TicketSidebarTile";
import SearchSection from "../../components/SearchSection";
import { useAgents } from "../../components/utils/getAgents";

const SingleTicket = () => {
  const [ticket, setTicket] = useState(null as any);
  const [tickets, setTickets] = useState([] as any);
  const { mvnoId, ticketId } = useParams();

  useEffect(() => {
    ApiGet(`/tickets/list/${mvnoId}`).then((response: any) => {
      setTickets(response.data);
    });
  }, [ticketId]);

  const dispatch = useDispatch();
  const { GetAgents, setAgents, agents } = useAgents();

  const navigate = useNavigate();

  const load = () => {
    console.log("hello");
    ApiGet(`/tickets/${ticketId}`)
      .then((response: any) => {
        console.log(response.data);
        setTicket(response.data);
        setNotes(response.data.notes ? response.data.notes : []);
        setFilteredNotes(response.data.notes ? response.data.notes : []);
      })
      .catch((error) => {
        navigate(`/${mvnoId}/tickets`);
      });

    GetAgents();
  };

  useEffect(load, []);
  const [notes, setNotes] = useState([] as any);

  const [filteredNotes, setFilteredNotes] = useState([] as any);

  const prevTicket = () => {
    const currentIndex = tickets.findIndex(
      (item: any) => item.id === ticket.id
    );
    let newTicket;
    if (currentIndex === 0) {
      newTicket = tickets[tickets.length - 1];
    } else {
      newTicket = tickets[currentIndex - 1];
    }
    navigate(`/${mvnoId}/tickets/${newTicket.id}`);
  };

  const nextTicket = () => {
    const currentIndex = tickets.findIndex(
      (item: any) => item.id === ticket.id
    );
    let newTicket;
    if (currentIndex === tickets.length - 1) {
      newTicket = tickets[0];
    } else {
      newTicket = tickets[currentIndex + 1];
    }
    navigate(`/${mvnoId}/tickets/${newTicket.id}`);
  };

  const handleTicketUpdate = (type: string, option: string, ticket: any) => {
    if (type === "assignee") {
      type = "agentId";
    }

    const updatedTicket = { ...ticket };

    /*if (type === "status") {
      const optionNumber = parseInt(option, 10);
      if (optionNumber === 3 || optionNumber === 4 || optionNumber === 5) {
        updatedTicket.dueDate = null;
      }
    }*/

    const { id, createdDate, notes, mvnoName, dueDate, ...rest } =
      updatedTicket;

    const result = {
      ...rest,
      dueBy: dueDate,
      mvnoId: mvnoId,
      [type]: option,
    };
    ApiPostAuth(`/tickets/update/${id}`, result)
      .then((res) => {
        load();
        dispatch({
          type: "notify",
          payload: {
            message: "Your changes have been saved.",
            error: false,
          },
        });
      })
      .catch((err) => {
        console.log(err);
        dispatch({
          type: "notify",
          payload: {
            message: "Error saving changes.",
            error: true,
          },
        });
      });
  };

  const [queryDisplay, setQueryDisplay] = useState("");

  return (
    <div className={`${styles.singleMain} select`}>
      {ticket && (
        <>
          <SearchSection
            data={notes}
            setFilteredData={setFilteredNotes}
            setQueryDisplay={setQueryDisplay}
            placeholder="Search by Note Author or Note Content"
            id="notes-search"
            setCurrentPage={() => {}}
          />
          <div className={styles.top}>
            <div className={styles.breadcrumbs}>
              <Link to={`/${mvnoId}/tickets`} className={styles.ticketsLink}>
                Tickets
              </Link>
              <div className={styles.ticketId}>/ Support #{ticket.id}</div>
            </div>
            <div className={styles.prevNext}>
              <div
                onClick={prevTicket}
                style={{ marginRight: 16, cursor: "pointer", display: "flex" }}
              >
                <CaretLeft />
              </div>
              <div
                onClick={nextTicket}
                style={{ cursor: "pointer", display: "flex" }}
              >
                <CaretRight />
              </div>
            </div>
          </div>
          <div className={styles.content}>
            <div className={ticket.type === "Porting" ? styles.fullWidth : ""}>
              <TicketSidebarTile
                GetTicket={load}
                ticket={ticket}
                handleTicketUpdate={handleTicketUpdate}
                assignees={agents}
              />
              {/*<AttachmentsTile />*/}
            </div>
            {/*<CorrespondenceTile />*/}
            <NotesTile
              list={filteredNotes}
              repopulate={load}
              ticketId={ticket?.id}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default SingleTicket;
