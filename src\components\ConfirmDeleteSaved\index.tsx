import { useState } from "react";
import { ApiDelete } from "../../pages/api/api";
import Modal from "../Modal";
import styles from "./confirm-delete-saved.module.scss";
import { useDispatch } from "react-redux";

const ConfirmDeleteSaved = ({ show, setShow, plan, repopulate }: any) => {
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  const removeSavedPlan = () => {
    setLoading(true);
    ApiDelete(
      `/accounts/${"attDetails" in plan ? "tempportin" : "tempsubscription"}/${
        plan.id
      }`,
      {}
    )
      .then((response) => {
        repopulate(() => {
          setLoading(false);
          setShow("");
        });
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Modal
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={removeSavedPlan}
      image="/bulk_edit_confirm_graphic.svg"
      saveButton="Yes, remove saved subscription"
      cancelButton="No"
      loading={loading}
    >
      <div className={styles.main}>
        <div className={styles.text}>
          Are you sure you want to remove the saved {plan?.product?.product}{" "}
          subscription?
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmDeleteSaved;
