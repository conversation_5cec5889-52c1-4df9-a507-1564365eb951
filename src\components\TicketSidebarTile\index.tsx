import styles from "./ticket-sidebar-tile.module.scss";
import {
  Coin,
  Clipboard,
  Home,
  Spanner,
  User,
  Pencil,
  PencilSimple,
} from "../svgs";
import TicketType from "../TicketType";
import TicketSelect from "../TicketSelect";
import StatusBadge from "../StatusBadge";
import Assignee from "../Assignee";
import Initials from "../Initials";
import Category from "../Category";
import Priority from "../Priority";
import formatDate from "../utils/formatDate";
import { useState } from "react";
import Button from "../Button";
import moment from "moment";
import DueDateBadge from "../DueDateBadge";
import CreateTicketModal from "../CreateTicketModal";
import CategorySelect from "../CategorySelect";
import { categories } from "../utils/tickets";

const TicketSidebarTile = ({
  ticket,
  noBackground,
  handleTicketUpdate,
  assignees,
  GetTicket,
}: any) => {
  const [disabled, setDisabled] = useState(false);
  const [edit, setEdit] = useState(false);

  return (
    <>
      <CreateTicketModal
        show={edit}
        setShow={setEdit}
        ticket={ticket}
        mode="edit"
        populate={GetTicket}
      />
      <div
        className={`${styles.ticketSummary} ${
          noBackground && styles.noBackground
        }`}
      >
        <div className={styles.underlineSection}>
          <div className={styles.top}>
            <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
              <CategorySelect
                disabled={disabled}
                label="Category"
                white
                options={categories.map((item: string) => ({
                  key: item,
                  label: item,
                  displayLabel: <TicketType status={item} />,
                }))}
                selected={ticket.category}
                onChange={(option: string) => {
                  handleTicketUpdate("category", option, ticket);
                }}
                small
              />
              <div className={styles.ticketNo}>#{ticket.id}</div>
              <div className={styles.date}>
                {moment(ticket.createdDate).format("DD/MM/YYYY")}
              </div>
            </div>
            <Button
              color="quaternary"
              onClick={(e: any) => {
                e.preventDefault();
                setEdit(true);
              }}
              style={{ height: "initial", fontSize: 14, padding: 0 }}
            >
              <PencilSimple /> Edit Ticket
            </Button>
            {/*ticket.type !== "Support" && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginLeft: "auto",
            }}
          >
            <TicketSelect
              disabled={loading}
              label="Assignee"
              options={[
                {
                  key: "Robin Billington",
                  label: <Assignee name="Robin Billington" />,
                  displayLabel: <Initials>RB</Initials>,
                },
                {
                  key: "Christina Choong",
                  label: <Assignee name="Christina Choong" />,
                  displayLabel: <Initials>CC</Initials>,
                },
                {
                  key: "Ahmed Houssein",
                  label: <Assignee name="Ahmed Houssein" />,
                  displayLabel: <Initials>AH</Initials>,
                },
                {
                  key: "Malwina Roczniak",
                  label: <Assignee name="Malwina Roczniak" />,
                  displayLabel: <Initials>MR</Initials>,
                },
              ]}
              selected={ticket.assignee}
              onChange={(option: string) => {}}
              white
            />
            <TicketSelect
              disabled={loading}
              label="Status"
              options={[
                {
                  key: "Open",
                  label: <StatusBadge status="Open" />,
                  displayLabel: <StatusBadge status="Open" />,
                },
                {
                  key: "Pending",
                  label: <StatusBadge status="Pending" />,
                  displayLabel: <StatusBadge status="Pending" />,
                },
                {
                  key: "Resolved",
                  label: <StatusBadge status="Resolved" />,
                  displayLabel: <StatusBadge status="Resolved" />,
                },
                {
                  key: "Closed",
                  label: <StatusBadge status="Closed" />,
                  displayLabel: <StatusBadge status="Closed" />,
                },
              ]}
              selected={ticket.status}
              onChange={(option: string) => {}}
              white
            />
          </div>
            )*/}
          </div>
          <div className={styles.subject}>{ticket.subject}</div>
        </div>
        <div className={styles.mainSection}>
          <div className={styles.issueLabel}>Issue:</div>
          <div className={styles.issueContent}>{ticket.description}</div>
          <div className={styles.dataBar}>
            <TicketSelect
              disabled={disabled}
              label="Priority"
              selected={ticket.priority}
              onChange={(option: string) => {
                handleTicketUpdate("priority", option, ticket);
              }}
              options={[
                {
                  key: 4,
                  label: <Priority priority="Urgent" />,
                  displayLabel: <Priority priority="Urgent" />,
                },
                {
                  key: 3,
                  label: <Priority priority="High" />,
                  displayLabel: <Priority priority="High" />,
                },
                {
                  key: 2,
                  label: <Priority priority="Medium" />,
                  displayLabel: <Priority priority="Medium" />,
                },
                {
                  key: 1,
                  label: <Priority priority="Low" />,
                  displayLabel: <Priority priority="Low" />,
                },
              ]}
              white
            />
            {/*<TicketSelect
          disabled={disabled}
          label="Category"
          options={[
            {
              key: "Invoice",
              label: <Category category="Invoice" />,
              displayLabel: <Clipboard />,
            },
            {
              key: "Payment",
              label: <Category category="Payment" />,
              displayLabel: <Coin />,
            },
            {
              key: "Billing Issues",
              label: <Category category="Billing Issues" />,
              displayLabel: <Home />,
            },
            {
              key: "Maintenance and Downtime",
              label: <Category category="Maintenance and Downtime" />,
              displayLabel: <Spanner />,
            },
            {
              key: "Complaints and Feedback",
              label: <Category category="Complaints and Feedback" />,
              displayLabel: <User />,
            },
          ]}
          selected={ticket.category}
          onChange={(option: string) => {
            handleTicketUpdate("category", option, ticket);
          }}
          white
        />*/}
            <TicketSelect
              assignees={assignees}
              disabled={disabled}
              label="Assignee"
              options={assignees.map((assignee: any) => {
                const getInitials = (name: string) => {
                  let names = name.split(" ");
                  return names[0][0] + names[names.length - 1][0];
                };
                return {
                  key: assignee.id,
                  label: <Assignee name={assignee.name} />,
                  displayLabel: (
                    <Initials>
                      {getInitials(assignee.name).toUpperCase()}
                    </Initials>
                  ),
                };
              })}
              selected={ticket.agentId}
              onChange={(option: string) => {
                handleTicketUpdate("assignee", option, ticket);
              }}
              white
            />
            <TicketSelect
              disabled={disabled}
              label="Status"
              options={[
                {
                  key: 2,
                  label: <StatusBadge status="Open" />,
                  displayLabel: <StatusBadge status="Open" />,
                },
                {
                  key: 3,
                  label: <StatusBadge status="Pending" />,
                  displayLabel: <StatusBadge status="Pending" />,
                },
                {
                  key: 4,
                  label: <StatusBadge status="Resolved" />,
                  displayLabel: <StatusBadge status="Resolved" />,
                },
                {
                  key: 5,
                  label: <StatusBadge status="Closed" />,
                  displayLabel: <StatusBadge status="Closed" />,
                },
              ]}
              selected={ticket.status}
              onChange={(option: string) => {
                console.log(option, ticket);
                handleTicketUpdate("status", option, ticket);
              }}
              white
            />
            <DueDateBadge date={new Date(ticket.dueDate)} />
          </div>

          {/*ticket.type === "Support" ? (
        <>
          <div className={styles.subject}>{ticket.subject}</div>
          <div className={styles.issueLabel}>Issue:</div>
          <div className={styles.issueContent}>{ticket.body}</div>
          <div className={styles.dataBar}>
            <TicketSelect
              disabled={loading}
              label="Priority"
              selected={ticket.priority}
              onChange={(option: string) => {}}
              options={[
                {
                  key: "Urgent",
                  label: <Priority priority="Urgent" />,
                  displayLabel: <Priority priority="Urgent" />,
                },
                {
                  key: "High",
                  label: <Priority priority="High" />,
                  displayLabel: <Priority priority="High" />,
                },
                {
                  key: "Medium",
                  label: <Priority priority="Medium" />,
                  displayLabel: <Priority priority="Medium" />,
                },
                {
                  key: "Low",
                  label: <Priority priority="Low" />,
                  displayLabel: <Priority priority="Low" />,
                },
              ]}
              white
            />
            <TicketSelect
              disabled={loading}
              label="Category"
              options={[
                {
                  key: "Mobilise",
                  label: <Category category="Mobilise" />,
                  displayLabel: <Home />,
                },
                {
                  key: "Technical Support",
                  label: <Category category="Technical Support" />,
                  displayLabel: <Spanner />,
                },
                {
                  key: "Finance",
                  label: <Category category="Finance" />,
                  displayLabel: <Coin />,
                },
                {
                  key: "Product Management",
                  label: <Category category="Product Management" />,
                  displayLabel: <Cube />,
                },
              ]}
              selected={ticket.category}
              onChange={(option: string) => {}}
              white
            />
            <TicketSelect
              disabled={loading}
              label="Assignee"
              options={[
                {
                  key: "Robin Billington",
                  label: <Assignee name="Robin Billington" />,
                  displayLabel: <Initials>RB</Initials>,
                },
                {
                  key: "Christina Choong",
                  label: <Assignee name="Christina Choong" />,
                  displayLabel: <Initials>CC</Initials>,
                },
                {
                  key: "Ahmed Houssein",
                  label: <Assignee name="Ahmed Houssein" />,
                  displayLabel: <Initials>AH</Initials>,
                },
                {
                  key: "Malwina Roczniak",
                  label: <Assignee name="Malwina Roczniak" />,
                  displayLabel: <Initials>MR</Initials>,
                },
              ]}
              selected={ticket.assignee}
              onChange={(option: string) => {}}
              white
            />
            <TicketSelect
              disabled={loading}
              label="Status"
              options={[
                {
                  key: "Open",
                  label: <StatusBadge status="Open" />,
                  displayLabel: <StatusBadge status="Open" />,
                },
                {
                  key: "Pending",
                  label: <StatusBadge status="Pending" />,
                  displayLabel: <StatusBadge status="Pending" />,
                },
                {
                  key: "Resolved",
                  label: <StatusBadge status="Resolved" />,
                  displayLabel: <StatusBadge status="Resolved" />,
                },
                {
                  key: "Closed",
                  label: <StatusBadge status="Closed" />,
                  displayLabel: <StatusBadge status="Closed" />,
                },
              ]}
              selected={ticket.status}
              onChange={(option: string) => {}}
              white
            />
          </div>
        </>
      ) : ticket.type === "DID Request" ? (
        <div>
          <div className={styles.issueLabel}>Address</div>
          <div
            className={styles.issueContent}
            dangerouslySetInnerHTML={{
              __html: ticket.didRequest.address.replaceAll(",", "<br />"),
            }}
          />
        </div>
      ) : (
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            paddingRight: 12,
            paddingTop: 12,
          }}
        >
          <div>
            <div className={styles.issueLabel}>Phone Number</div>
            <div className={styles.issueContent}>
              {ticket.porting?.phoneNumber}
            </div>
          </div>
          <div>
            <div className={styles.issueLabel}>Country</div>
            <div className={styles.issueContent}>{ticket.porting?.country}</div>
          </div>
          <div>
            <div className={styles.issueLabel}>PAC Code</div>
            <div className={styles.issueContent}>{ticket.porting?.pacCode}</div>
          </div>
          {noBackground && <Button>Confirm & Forward</Button>}
        </div>
        )*/}
        </div>
      </div>
    </>
  );
};

export default TicketSidebarTile;
