import { useState } from "react";
import { ApiPostAuth } from "../../pages/api/api";
import Modal from "../Modal";
import styles from "./confirm-activate-saved.module.scss";
import { useDispatch } from "react-redux";

const ConfirmActivateSaved = ({
  show,
  setShow,
  plan,
  repopulate,
  accountId,
}: any) => {
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  const activateSavedPlan = () => {
    setLoading(true);
    if ("attDetails" in plan) {
      ApiPostAuth("/accounts/portin/make", {
        oldService: {
          ...plan?.attDetails?.oldService,
          firstName: plan?.attDetails?.accountDetails?.firstName,
          lastName: plan?.attDetails?.accountDetails?.lastName,
        },
        msisdn: plan?.attDetails?.msisdn,
        accountId: accountId,
        tempPortInId: plan?.id,
      })
        .then((response) => {
          repopulate(() => {
            setLoading(false);
            setShow("");
          });
        })
        .catch((error) => {
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: error.response.data.message,
            },
          });
        });
    } else {
      ApiPostAuth("/accounts/attactivation", {
        accountId: accountId,
        tempSubscriptionId: plan.id,
      })
        .then((response) => {
          repopulate(() => {
            setLoading(false);
            setShow("");
          });
        })
        .catch((error) => {
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: error.response.data.message,
            },
          });
        });
    }
  };

  return (
    <Modal
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={activateSavedPlan}
      image="/bulk_edit_confirm_graphic.svg"
      saveButton="Yes, complete activation"
      cancelButton="No"
      loading={loading}
    >
      <div className={styles.main}>
        <div className={styles.text}>
          Are you sure you want to activate the saved {plan?.product?.product}{" "}
          subscription?
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmActivateSaved;
