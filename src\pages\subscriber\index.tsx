import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useParams } from "react-router-dom";
import styles from "../../styles/subscriber-profile.module.scss";
import { Plus } from "../../components/svgs";
import Button from "../../components/Button";
import { ApiGet } from "../api/api";
import ActivateSubscriberModal from "../../components/ActivateSubscriberModal";
import PortInForm from "../../components/PortInForm";
import { ClickAwayListener, Collapse } from "@mui/material";
import UserMenu from "../../components/UserMenu";
import { motion } from "framer-motion";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import NewPlansTab from "../../components/NewPlansTab";
import SubscriberAccountTab from "../../components/SubscriberAccountTab";
import SubscriberProfileLoading from "../../components/SubscriberProfileLoading";
import PortInRequestsTab from "../../components/PortInRequestsTab";
import SubscriberAccountDetailsTabLoading from "../../components/SubscriberAccountDetailsTabLoading";
import AddFamilyPlanMembersModal from "../../components/AddFamilyPlanMembersModal/add-family-plan-members-modal";
import qs from "qs";
import TableControl from "../../components/TableControl";

const Subscriber = () => {
  const dispatch = useDispatch();
  const { mvnoId, id } = useParams();
  const sidebarOpen = useSelector((state: any) => state.sidebarOpen);
  const [subscriberAndPlans, setSubscriberAndPlans] = useState(null as any);

  const [initialLoading, setInitialLoading] = useState(true);
  // const initialLoading = true
  const [loadingError, setLoadingError] = useState(false);

  const [activeSection, setActiveSection] = useState("subscriptions");

  const sections = [
    {
      label: "Subscriptions",
      key: "subscriptions",
    },
    {
      label: "Account Details",
      key: "account",
    },
    {
      label: "Port-in Requests",
      key: "port-in-requests",
    }
  ];

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(15);

  const fetchSubscriberAndPlans = async (after = () => { }) => {
    setInitialLoading(true);

    const params = qs.stringify({
      page: currentPage - 1,
      size: itemsPerPage,
    }, { indices: false });

    const requestUrl = "/accounts/by-account/v2/";

    await ApiGet(requestUrl + id + (params ? `?${params}` : ""))
      .then((response) => {
        console.log(response);
        setSubscriberAndPlans(response.data);
        setInitialLoading(false);
        after();
      })
      .catch((error) => {
        setLoadingError(true);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  }

  useEffect(() => {
    // only fetch(and refresh) data if we are on(or switch to) the subscriptions tab as we always start on this tab
    // other tabs data is fetched within their component
    if (
      activeSection === "subscriptions"
    ) {
      fetchSubscriberAndPlans();
      return;
    } else {
      setInitialLoading(false);
    }
  }, [activeSection, currentPage, itemsPerPage]);

  const [currentModal, setCurrentModal] = useState("");
  const [addSubDropdownExpanded, setAddSubDropdownExpanded] = useState(false);

  // scroll to top
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [activeSection, initialLoading]);

  return (
    <>
      {subscriberAndPlans && (
        <PortInForm
          show={currentModal === "new-port"}
          setShow={setCurrentModal}
          data={subscriberAndPlans}
          repopulate={fetchSubscriberAndPlans}
          accountDataProvided
          checkEligibility
        />
      )}
      <ActivateSubscriberModal
        show={currentModal === "add-subscription"}
        setShow={setCurrentModal}
        complete={() => {
          console.log("");
        }}
        addingNew
        mid={subscriberAndPlans ? subscriberAndPlans.mid : 0}
        repopulate={fetchSubscriberAndPlans}
      />
      {currentModal === "new-family" && (
        <AddFamilyPlanMembersModal
          show={currentModal === "new-family"}
          onClose={() => setCurrentModal("")}
          originUserEmail={subscriberAndPlans?.email || undefined}
        />
      )}

      <div
        className={`${styles.main} ${sidebarOpen ? styles.open : styles.closed
          }`}
      >
        <div className={styles.topBar}>
          <div className={styles.breadcrumbs}>
            <Link
              style={{ textDecoration: "none" }}
              to={`/${mvnoId}/subscriber-management`}
            >
              <div className={styles.backLink}>Subscriber Management</div>
            </Link>
            <div className={styles.activeCrumb}>
              &nbsp;&nbsp;/&nbsp;&nbsp;
              {subscriberAndPlans && (
                <>
                  {subscriberAndPlans.subscriberFirstName}{" "}
                  {subscriberAndPlans.subscriberLastName}
                </>
              )}
            </div>
          </div>
          <UserMenu />
        </div>
        {!initialLoading && !loadingError ? (
          <>
            <div className={styles.subscriberName}>
              {subscriberAndPlans?.subscriberFirstName}{" "}
              {subscriberAndPlans?.subscriberLastName}
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "16px",
              }}
            >
              <div className={styles.selectionWrapper}>
                {sections.map((type: any) => (
                  <div
                    className={`${styles.selection} ${activeSection === type.key && styles.activeSelection
                      }`}
                    onClick={() => {
                      setActiveSection(type.key);
                    }}
                  >
                    <span>{type.label}</span>
                    {activeSection === type.key && (
                      <motion.div
                        className={styles.background}
                        layoutId="underline"
                      />
                    )}
                  </div>
                ))}
              </div>

              <ClickAwayListener
                onClickAway={() => setAddSubDropdownExpanded(false)}
              >
                <div className={styles.addSubscription}>
                  <Button
                    onClick={() => {
                      setAddSubDropdownExpanded((prev: any) => !prev);
                    }}
                  >
                    <Plus />
                    Add a Subscription
                  </Button>
                  <div className={styles.addSubSelectContainer}>
                    <Collapse in={addSubDropdownExpanded}>
                      <div className={styles.addSubSelect}>
                        <div
                          className={styles.addButton}
                          onClick={() => {
                            setCurrentModal("add-subscription");
                          }}
                        >
                          New Number
                        </div>
                        <div
                          className={styles.addButton}
                          onClick={() => setCurrentModal("new-port")}
                        >
                          Port-in Number
                        </div>
                        <div
                          className={styles.addButton}
                          onClick={() => setCurrentModal("new-family")}
                        >
                          Family Plan
                        </div>
                        <div
                          className={styles.addButton}
                        // onClick={}
                        >
                          Shared Data Plan
                        </div>
                      </div>
                    </Collapse>
                  </div>
                </div>
              </ClickAwayListener>
            </div>

            <SwitchTransition>
              <CSSTransition
                key={activeSection}
                addEndListener={(node, done) =>
                  node.addEventListener("transitionend", done, false)
                }
                classNames="fade"
              >
                <div>
                  {activeSection === "account" && (
                    <SubscriberAccountTab
                      subscriberInfo={subscriberAndPlans}
                      repopulate={fetchSubscriberAndPlans}
                    />
                  )}
                  {activeSection === "subscriptions" && (
                    <>
                      <NewPlansTab
                        subscriberAndPlansData={subscriberAndPlans}
                        fetchSubscriberAndPlansData={fetchSubscriberAndPlans}
                      />
                      {/* This is implemented here because initialLoading state change unmounts the tab component and causes continous refetching */}
                      <div className={styles.plansPagination}>
                        <TableControl
                          itemsPerPage={itemsPerPage}
                          setItemsPerPage={(itemsPerPage: number) => {
                            setItemsPerPage(itemsPerPage)
                            setCurrentPage(1);
                          }}
                          currentPage={currentPage}
                          setCurrentPage={(currentPage: number) => {
                            setCurrentPage(currentPage);
                          }}
                          numberOfPages={subscriberAndPlans?.totalPages || 1}
                        />
                      </div>
                    </>
                  )}
                  {activeSection === "port-in-requests" && (
                    <PortInRequestsTab
                      subscriberInfo={subscriberAndPlans}
                      fetchSubscriberInfo={fetchSubscriberAndPlans}
                    />
                  )}
                </div>
              </CSSTransition>
            </SwitchTransition>
          </>
        ) : loadingError ? (
          <div className={styles.loadingError}>
            <img src="/error_robot.svg" />
            <div>
              <h3>An unexpected error occurred</h3>
              <p>Please try again later</p>
            </div>
            </div>
        ) : activeSection === "account" ? (
          <SubscriberAccountDetailsTabLoading />
        ) : (
          <SubscriberProfileLoading />
        )}
      </div>
    </>
  );
};

export default Subscriber;
