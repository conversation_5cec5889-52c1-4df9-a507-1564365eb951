@use "../../styles/theme.scss" as *;

.container {
  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 6px;
    white-space: nowrap;

    tbody {
      tr {
        background: #f7f6f6;
        height: 0px;

        td {
          font-size: 14px;
          font-weight: 400;
          line-height: 21px;
          padding: 16px;
          svg {
            vertical-align: middle;
          }
        }
        td:first-child {
          border-radius: 12px 0 0 12px;
          padding-left: 24px;
        }
        td:last-child {
          border-radius: 0 12px 12px 0;
          padding-right: 24px;
        }
      }
      &:before {
        content: "@";
        display: block;
        line-height: 0px;
        text-indent: -99999px;
      }
    }
    thead {
      tr {
        th:first-child {
          padding-left: 24px;
        }
      }
    }
    th {
      font-size: 14px;
      font-weight: 500;
      line-height: 21px;
      text-align: start;
      border-bottom: 1px solid $disabled;
      padding: 0 16px;
      padding-bottom: 19px;
    }
  }
}

.tableContainer {
  overflow-x: auto;
  padding-bottom: 5px;
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 55px;
  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }
  h3 {
    width: 100%;
    text-align: center;
  }
}

.productChange {
  display: flex;
  flex-direction: column;
  align-items: start;

  svg {
    transform: rotate(90deg);
    width: 16px;
  }

  .oldPlan {
    opacity: 0.5;
  }
}

.pagination {
  display: flex;
  justify-content: flex-end;
}
