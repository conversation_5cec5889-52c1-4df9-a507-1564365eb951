import styles from "./category-select.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown } from "../svgs";
import { useRef, useState, useEffect } from "react";
import Radio from "../Radio";
import Tooltip from "../Tooltip";
import SearchBar from "../SearchBar";
import { submitFilterSelectSearch } from "../utils/searchAndFilter";

const CategorySelect = ({
  label,
  options,
  selected,
  onChange,
  disabled,
  white,
  orderby,
  small,
}: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const [query, setQuery] = useState("");

  const [filteredOptions, setFilteredOptions] = useState([] as any);

  useEffect(() => {
    setFilteredOptions(options);
  }, [options]);

  const reset = () => {
    toggleMenu(false);
    setQuery("");
    setFilteredOptions(options);
  };

  return (
    <div className={`${styles.box} ticket`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${white && styles.white} ${
          disabled && styles.disabled
        } ${orderby && styles.orderby} ${small && styles.small}`}
        onClick={(e) => {
          e.stopPropagation();
          toggleMenu(true);
        }}
      >
        {(() => {
          if (selected) {
            return options.filter((item: any) => item.key === selected)[0]
              ?.displayLabel;
          } else {
            return label;
          }
        })()}
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => {
          toggleMenu(false);
          reset();
        }}
        align="start"
      >
        <div style={{ marginBottom: 32 }}>
          <SearchBar
            onSubmit={() => {
              submitFilterSelectSearch(
                options,
                setFilteredOptions,
                query,
                setQuery
              );
            }}
            query={query}
            setQuery={setQuery}
            placeholder="Search categories"
            small
            grey
          />
        </div>
        <div className={`${styles.container} modal-scroll`}>
          {filteredOptions.map((item: any) => (
            <MenuItem
              onClick={() => {
                onChange(item.key);
              }}
              className={styles.menuItem}
              key={item.key}
            >
              <Radio
                checked={selected === item.key}
                onClick={(e: any) => {
                  e.stopPropagation();
                  onChange(item.key);
                  toggleMenu();
                }}
              />
              <div style={{ width: 14 }} />
              {item.label}
            </MenuItem>
          ))}
        </div>
      </ControlledMenu>
    </div>
  );
};

export default CategorySelect;
