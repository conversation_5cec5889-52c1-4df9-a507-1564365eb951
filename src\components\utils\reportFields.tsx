export const subscriptionReportFields = [
  {
    label: "Plan Name",
    labelStr: "Plan Name",
    key: "offerName",
  },
  {
    label: "Plan Size",
    labelStr: "Plan Size",
    key: "offerSize",
  },
  {
    label: "CTN",
    labelStr: "CTN",
    key: "ctn",
  },
  {
    label: "BAN",
    labelStr: "BAN",
    key: "ban",
  },
  {
    label: "CTN Status",
    labelStr: "CTN Status",
    key: "ctnStatus",
  },
  {
    label: "Last Update",
    labelStr: "Last Update",
    key: "updatedAt",
  },
  {
    label: "Activation Date",
    labelStr: "Activation Date",
    key: "activationDate",
  },
  {
    label: "Usage (KB)",
    labelStr: "Usage (KB)",
    key: "usageKb",
  },
  {
    label: "Usage (MB)",
    labelStr: "Usage (MB)",
    key: "usageMb",
  },
  {
    label: "Usage (GB)",
    labelStr: "Usage (GB)",
    key: "usageGb",
  },
  {
    label: "Overage (MB)",
    labelStr: "Overage (MB)",
    key: "overageMb",
  },
  {
    label: "Overage (GB)",
    labelStr: "Overage (GB)",
    key: "overageGb",
  },
  {
    label: (
      <>
        International
        <br />
        Calling
      </>
    ),
    labelStr: "International Calling",
    key: "internationalCallBlock",
  },
  {
    label: (
      <>
        International
        <br />
        Roaming
      </>
    ),
    labelStr: "International Roaming",
    key: "internationalRoamBlock",
  },
  {
    label: (
      <>
        International
        <br />
        Text
      </>
    ),
    labelStr: "International Texting",
    key: "internationalTextBlock",
  },
  {
    label: (
      <>
        Business World
        <br />
        Connect Advantage
      </>
    ),
    labelStr: "Business World Connect Advantage",
    key: "apexWorldConnectAdvantage",
  },
  {
    label: (
      <>
        Day Pass for Resale
        <br />
        including roaming
      </>
    ),
    labelStr: "Day Pass for Resale including roaming",
    key: "attInternationalDayPassForResale",
  },
  {
    label: (
      <>
        PassPort Data/Voice
        <br />
        10GB MRC
      </>
    ),
    labelStr: "PassPort Data/Voice 10GB MRC",
    key: "passportDataVoice10GbMrc",
  },
  {
    label: (
      <>
        PassPort Voice/Data
        <br />
        10GB MRC
      </>
    ),
    labelStr: "PassPort Voice/Data 10GB MRC",
    key: "passportVoiceData10GbMrc",
  },
  {
    label: (
      <>
        PassPort
        <br />
        DataOnly 10GB
      </>
    ),
    labelStr: "PassPort DataOnly 10GB",
    key: "passortDataOnly10Gb",
  },
  {
    label: (
      <>
        Data
        <br />
        Blocking
      </>
    ),
    labelStr: "Data Blocking",
    key: "dataBlocking",
  },
  /*{
    label: "Plan Name",
    labelStr: "Plan Name",
    key: "productName",
  },
  {
    label: "Plan Size",
    labelStr: "Plan Size",
    key: "productSize",
  },*/
  {
    label: (
      <>
        Subscription
        <br />
        Cancellation Date
      </>
    ),
    labelStr: "Subscription Cancellation Date",
    key: "subscriptionCancellationDate",
  },
  {
    label: "Suspension Date",
    labelStr: "Suspension Date",
    key: "suspensionDate",
  },
  {
    label: "Port Out Date",
    labelStr: "Port Out Date",
    key: "portOutDate",
  },
  {
    label: "ICCID",
    labelStr: "ICCID",
    key: "iccid",
  },
  {
    label: "Biller IMEI",
    labelStr: "Biller IMEI",
    key: "billerImei",
  },
  {
    label: "Network IMEI",
    labelStr: "Network IMEI",
    key: "networkImei",
  },
  {
    label: "Subscriber Name",
    labelStr: "Subscriber Name",
    key: "subscriberName",
  },
];

/*export const createReport = () => {
  let reportItem = {} as any;
  reportFields.forEach((field: any) => {
    reportItem[field.key] = "";
  });

  let reportList = [] as any;
  Array.from({ length: 100 }).forEach((x, i) => {
    reportList.push(reportItem);
  });
  return reportList;
};*/

export const churnReportFields = [
  {
    label: "CTN",
    labelStr: "CTN",
    key: "ctn",
  },
  {
    label: "CTN Status",
    labelStr: "CTN Status",
    key: "ctnStatus",
  },
  {
    label: "Activation Date",
    labelStr: "Activation Date",
    key: "activationDate",
  },
  {
    label: (
      <>
        Subscription
        <br />
        Cancellation Date
      </>
    ),
    labelStr: "Subscription Cancellation Date",
    key: "subscriptionCancellationDate",
  },
  {
    label: "Port Out Date",
    labelStr: "Port Out Date",
    key: "portOutDate",
  },
  {
    label: "ICCID",
    labelStr: "ICCID",
    key: "iccid",
  },
  {
    label: "Biller IMEI",
    labelStr: "Biller IMEI",
    key: "billerImei",
  },
  {
    label: "Network IMEI",
    labelStr: "Network IMEI",
    key: "networkImei",
  },
  {
    label: "Subscriber Name",
    labelStr: "Subscriber Name",
    key: "subscriberName",
  },
];

export const throttleReportFields = [
  {
    label: (
      <>
        Subscriber
        <br />
        Number
      </>
    ),
    labelStr: "Subscriber Number",
    key: "mdn",
  },
  {
    label: "Plan Name",
    labelStr: "Plan Name",
    key: "plan",
  },
  {
    label: "Usage (KB)",
    labelStr: "Usage (KB)",
    key: "usageKb",
  },
  {
    label: "Usage (GB)",
    labelStr: "Usage (GB)",
    key: "usageGb",
  },
  {
    label: "Status",
    labelStr: "Status",
    key: "status",
  },
  {
    label: "Throttle Name",
    labelStr: "Throttle Name",
    key: "name",
  },
  {
    label: "Applied Date",
    labelStr: "Applied Date",
    key: "appliedDate",
  },
];

export const paymentsFields = [
  {
    label: "Email",
    labelStr: "Email",
    key: "email",
  },
  {
    label: "MDN",
    labelStr: "MDN",
    key: "mdn",
  },
  {
    label: "Plan",
    labelStr: "Plan",
    key: "planName",
  },
  {
    label: "Total Amount",
    labelStr: "Total Amount",
    key: "amount",
  },
  {
    label: "Regulatory Fee",
    labelStr: "Regulatory Fee",
    key: "regulatoryFee",
  },
  {
    label: "Taxes",
    labelStr: "Taxes",
    key: "taxAmount",
  },
  {
    label: "Status",
    labelStr: "Status",
    key: "status",
  },
  {
    label: "Failure Reason",
    labelStr: "Failure Reason",
    key: "failureReason",
  },
  {
    label: "Date",
    labelStr: "Date",
    key: "createdAt",
  },
];

export const mdnChangeReportFields = [
  {
    label: "MVNO",
    labelStr: "MVNO",
    key: "mvnoName",
  },
  {
    label: "MVNO ID",
    labelStr: "MVNO ID",
    key: "mvnoId",
  },
  {
    label: "Account ID",
    labelStr: "Account ID",
    key: "accountId",
  },
  {
    label: "First Name",
    labelStr: "First Name",
    key: "firstName",
  },
  {
    label: "Last Name",
    labelStr: "Last Name",
    key: "lastName",
  },
  {
    label: "Email",
    labelStr: "Email",
    key: "email",
  },
  {
    label: "ICCID",
    labelStr: "ICCID",
    key: "iccid",
  },
  {
    label: "Pre MDN Change",
    labelStr: "Pre-MDN Change",
    key: "preMdnchange",
  },
  {
    label: "Post MDN Change",
    labelStr: "Post-MDN Change",
    key: "postMdnChange",
  },
  {
    label: "Aradial Message",
    labelStr: "Aradial Message",
    key: "araidialMessage",
  },
  {
    label: "ATT Message",
    labelStr: "ATT Message",
    key: "attMessage",
  },
  {
    label: "Description",
    labelStr: "Description",
    key: "description",
  },
  {
    label: "Sub ID",
    labelStr: "Sub ID",
    key: "subId",
  },
  {
    label: "Status",
    labelStr: "Status",
    key: "status",
  },
];
