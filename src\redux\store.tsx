import { useLocation } from "react-router-dom";
import { createStore } from "redux";
import { v4 as uuidv4 } from "uuid";
import { populateActivity, populateUsers } from "../components/subscribers";

const initialState = {
  isLoggedIn: false,
  userInfo: localStorage.getItem("crmUserInfo")
    ? JSON.parse(localStorage.getItem("crmUserInfo") || "")
    : null,
  notifications: [],
  sidebarOpen: localStorage.getItem("sidebarOpen")
    ? localStorage.getItem("sidebarOpen") === "true"
      ? true
      : false
    : true,
  logoLarge: "",
  logoSmall: "",
  subscribers: [],
  loginMessage: null,
  closeLoginMessage: true,
  resetMessage: null,
  closeResetMessage: true,
  activeMdn: null,
  projects: [
    {
      name: "MVNO 1",
      urlName: "mvno1",
      image: "/placeholder.png",
      logoLarge: "/placeholder.png",
      logoSmall: "/placeholder.png",
    },
    {
      name: "MVNO 2",
      urlName: "mvno2",
      image: "/placeholder.png",
      logoLarge: "/placeholder.png",
      logoSmall: "/placeholder.png",
    },
    {
      name: "MVNO 3",
      urlName: "mvno3",
      image: "/placeholder.png",
      logoLarge: "/placeholder.png",
      logoSmall: "/placeholder.png",
    },
    {
      name: "MVNO 4",
      urlName: "mvno4",
      image: "/placeholder.png",
      logoLarge: "/placeholder.png",
      logoSmall: "/placeholder.png",
    },
  ],
  throttleNotifications: null,
  order: null,
};

const changeState = (state = initialState, { type, payload, ...rest }: any) => {
  switch (type) {
    case "set":
      return { ...state, ...rest };
    case "notify":
      let current = [] as any;
      current.push({ ...payload, id: uuidv4() });
      return { ...state, notifications: current };
    case "closeNotification":
      let currentOpen = [...state.notifications] as any;
      currentOpen = currentOpen.filter((item: any) => item.id !== payload);
      return { ...state, notifications: currentOpen };
    default:
      return state;
  }
};

const store = createStore(changeState);
export default store;
