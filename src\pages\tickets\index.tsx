import { useEffect, useReducer, useState } from "react";
import styles from "../../styles/tickets.module.scss";
import TicketsPanel from "../../components/TicketsPanel";
import { useDispatch, useSelector } from "react-redux";
import CreateTicketModal from "../../components/CreateTicketModal";
import { ApiGet } from "../api/api";
import { useParams } from "react-router-dom";
import { useAgents } from "../../components/utils/getAgents";

const reducer = (state: any, { type = "set", ...rest }: any) => {
  if (type === "set") {
    return {
      ...state,
      ...rest,
    };
  }
};

//////////////////////////////////////////////////////////
/************     Main Component       ******************/

const Tickets = () => {
  const dispatch = useDispatch();
  const { mvnoId } = useParams();

  const { ticketOpen } = useSelector((state: any) => state);

  const initialState = {
    tickets: [],
    ticketsLoading: true,
  };

  const [state, setState] = useReducer(reducer, initialState);
  const { GetAgents, agents, setAgents } = useAgents();

  useEffect(() => {
    GetTicket();
    GetAgents();
  }, []);

  useEffect(() => {
    dispatch({
      type: "set",
      ticketOpen: false,
    });
  }, []);

  const GetTicket = () => {
    //setState({ ticketsLoading: true });
    ApiGet(`/tickets/list/${mvnoId}`).then((res) => {
      setState({
        tickets: res.data.map((item: any) => ({
          ...item,
          id: item.id.toString(),
        })),
      });
      setTimeout(() => {
        setState({ ticketsLoading: false });
      }, 300);
      console.log("Tickets Response", res.data);
    });
  };

  const [showCreate, setShowCreate] = useState(false);

  return (
    <div className={`${styles.main} ${ticketOpen && styles.ticketOpen}`}>
      <CreateTicketModal
        show={showCreate}
        setShow={setShowCreate}
        populate={GetTicket}
      />
      <TicketsPanel
        allTickets={state.tickets}
        showFilters
        ticketsLoading={state.ticketsLoading}
        assignees={agents}
        setState={setState}
        GetAgents={GetAgents}
        GetTicket={GetTicket}
        setShowCreate={setShowCreate}
      />
    </div>
  );
};

export default Tickets;
