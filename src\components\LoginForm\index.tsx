import { Input } from "../Input";
import styles from "./login-form.module.scss";
import { useState } from "react";
import Button from "../Button";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { validateAll } from "indicative/validator";
import $ from "jquery";
import { ApiPost } from "../../pages/api/api";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";

const fields = ["email", "password"];
const rules = getRules(fields);
const messages = getMessages(fields);

const LoginForm = ({ handleOtp }: any) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  /***********  Init State  ***********/

  const [loginInfo, setLoginInfo] = useState(createStateObject(fields));

  const [loading, setLoading] = useState(false);

  const login = () => {
    const data = {
      email: loginInfo.email.trim(),
      password: loginInfo.password.trim(),
    };

    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPost("/users/login", {
          email: data.email,
          password: data.password,
          entered2faCode: "",
        })
          .then((response) => {
            localStorage.setItem("token", response.data.auth);
            localStorage.setItem("refreshToken", response.data.refreshToken);
            const { message, auth, active, ...userInfo } = response.data;
            if (!userInfo.brandLogo) {
              userInfo.brandLogo = "";
            }
            dispatch({
              type: "set",
              isLoggedIn: true,
              userInfo: userInfo,
            });
            localStorage.setItem("crmUserInfo", JSON.stringify(userInfo));
            if (response.data.roleName === "mvne") {
              dispatch({
                type: "set",
                resetMessage: response.data.message,
                closeResetMessage: false,
              });
              navigate("/select-project");
            } else {
              dispatch({
                type: "set",
                loginMessage: response.data.message,
                closeLoginMessage: false,
              });
              navigate(`/${userInfo.mvnoId}/subscriber-management`);
            }
          })
          .catch((error) => {
            if (
              error.response.data.message ==
              "2FA is enabled for this user, please use 2FA code that you got in the email"
            ) {
              handleOtp({
                email: data.email,
                password: data.password,
              });
            } else {
              dispatch({
                type: "notify",
                payload: {
                  error: true,
                  message: error.response.data.message,
                },
              });
              setLoading(false);
            }
          });
      })
      .catch((errors) => {
        displayErrors(errors, setLoginInfo);

        if (!data.email) {
          $("#login-email").trigger("focus");
        } else if (!data.password) {
          $("#login-password").trigger("focus");
        }
      });
  };

  return (
    <div className={styles.form}>
      <h2>Login</h2>
      {fields.map((key: string) => (
        <Input
          label={labels[key]}
          placeholder={placeholders[key]}
          value={loginInfo[key]}
          disabled={loading}
          onChange={(e: any) => {
            handleInputChange(key, e, loginInfo, setLoginInfo);
          }}
          error={loginInfo.errors[key]}
          clear={() => {
            clearInput(key, setLoginInfo);
          }}
          id={"login-" + key}
          onKeyDown={login}
          password={key === "password"}
        />
      ))}
      <Button
        style={{ marginTop: 30 }}
        loading={loading}
        color="primary"
        onClick={login}
      >
        Log in
      </Button>
    </div>
  );
};

export default LoginForm;
