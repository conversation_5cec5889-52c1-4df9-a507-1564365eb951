@use "../../styles/theme.scss" as *;

.container {
  width: 100%;
  padding: 24px;
  background: #fff;
  border-radius: 24px 10px 10px 24px;
  height: calc(100vh - 178px);

  @media (max-width: 1250px) {
    padding: 16px;
    height: calc(100vh - 116px);
  }
}

.title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 24px;
}

.portingGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  width: 100%;
}

.errorContainer {
  background-color: #fff0f0;
  border: 1px solid #ffcaca;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  text-align: center;

  p {
    color: #d32f2f;
    margin-bottom: 12px;
  }
}

.retryButton {
  margin-top: 12px;
}

.noPortins {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;

  img {
    margin-bottom: 16px;
    max-width: 120px;
  }

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #666;
  }
}
