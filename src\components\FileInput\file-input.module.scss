@use "../../styles/theme.scss" as *;

.container {
  //transition: all 0.3s ease;
  border-radius: 8px;
  &.filled {
    padding: 16px;
    border: 1px solid $grey;
  }
}

.inputMain {
  height: 56px;
  padding: 16px 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed $orange;
  border-radius: 8px;
  color: $placeholder;
  cursor: pointer;
  //transition: all 0.3s ease;
  &.dragging {
    border-width: 2px;
    border-color: $dark-orange;
  }
  &:hover {
    border-color: $dark-orange;
  }
  &.shrink {
    height: 40px;
  }
  .bold {
    color: $orange;
    font-weight: 600;
  }
  svg {
    margin-right: 10px;
  }
}

.info {
  display: flex;
  justify-content: space-between;
  margin-top: 2px;
  color: $grey;
  font-size: 12px;
  line-height: 24px;
}
