@use "../../styles/theme.scss" as *;

.main {
  background: $off-white;
  box-shadow: 0px 0px 30px 0px rgba(8, 7, 87, 0.1);
  border-radius: 24px;
  padding: 24px 0;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px 12px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    margin-bottom: 24px;
    .leftContainer {
      display: flex;
      align-items: center;
    }
    h4 {
      font-size: 20px;
      line-height: 30px;
      margin-right: 16px;
    }
  }
  .attachmentsContainer {
    padding: 0 24px;
    display: grid;
    grid-template-columns: 1fr;
    grid-column-gap: 16px;
    grid-row-gap: 16px;
  }
}

.noneFound {
  display: grid;
  grid-column: 1 / 3;
  margin: 0 auto;
  width: 100%;
  max-width: 360px;
  align-items: center;
  justify-content: center;
  padding: 87px 0;
  img,
  h3 {
    width: 100%;
    grid-area: 1 / 1 / 2 / 2;
  }
  h3 {
    text-align: center;
    font-size: 14px;
    font-weight: 600;
  }
}
