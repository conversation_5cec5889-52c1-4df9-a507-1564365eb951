@use "../../styles/theme.scss" as *;

.subscriberTile {
  width: 100%;
  min-height: calc(100vh - 227px);
  background: #fff;
  border-radius: 24px 10px 10px 24px;
  padding: 16px 24px;
  overflow-y: scroll;
  overflow-x: hidden;
  @media (max-width: 1250px) {
    padding: 24px;
    height: calc(100vh - 116px);
  }
}

.accountDetails {
  padding: 23px 26px;
  width: 100%;
  .accountDetailsSection {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .title {
      font-size: 20px;
      font-weight: 600;
      line-height: 30px;
      margin-bottom: 9px;
    }
    .accountDetailsGrid {
      display: grid;
      grid-template-columns: auto auto;
      grid-row-gap: 16px;
      grid-column-gap: 150px;
      .itemLabel {
        font-size: 12px;
        line-height: 18px;
        color: #666666;
      }
      .itemDetail {
        font-size: 14px;
        line-height: 21px;
      }
    }
    .buttons {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }
    .accountButton {
      border: none;
      background-color: #f1f1f1;
      display: flex;
      align-items: center;
      font-size: 16px;
      line-height: 24px;
      font-weight: 600;
      border-radius: 16px;
      padding: 10px 16px;
      cursor: pointer;
      &:hover {
        background-color: $light-orange;
      }
      svg {
        vertical-align: middle;
        margin-right: 8px;
      }
    }
  }
}
