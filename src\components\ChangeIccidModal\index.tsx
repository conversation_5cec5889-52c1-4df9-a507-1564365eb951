import { useState } from "react";
import { Input } from "../Input";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import {
  clearInput,
  createStateObject,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
  displayErrors,
} from "../utils/InputHandlers";
import styles from "./change-iccid-modal.module.scss";
import { useDispatch } from "react-redux";
import { validateAll } from "indicative/validator";
import { ApiPostAuth } from "../../pages/api/api";

const fields = ["iccid"];
const rules = getRules(fields);
const messages = getMessages(fields);

const ChangeIccidModal = ({
  show,
  setShow,
  subInfo,
  plan,
  repopulate,
}: any) => {
  const dispatch = useDispatch();

  const handleChange = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPostAuth("/accounts/update", {
          subscriber: plan.mdn,
          imei: plan.imei,
          iccid: data.iccid,
          mid: subInfo.mid,
          subscriptionId: plan.id,
        })
          .then((response) => {
            setLoading(false);
            setData(createStateObject(fields));
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
            setShow("");
            repopulate();
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message:
                  error.response.data.message ||
                  "Something went wrong, please try again.",
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const [data, setData] = useState(createStateObject(fields));
  const [loading, setLoading] = useState(false);

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      setData={setData}
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      proceed={handleChange}
      setShow={setShow}
      close={() => {
        clearInput("iccid", setData);
        setShow("");
      }}
      loading={loading}
    >
      <div className={styles.main}>
        <h4>Change ICCID</h4>
        <Input
          label={labels.iccid}
          placeholder={placeholders.iccid}
          value={data.iccid}
          onChange={(e: any) => {
            handleInputChange("iccid", e, data, setData);
          }}
          clear={() => {
            clearInput("iccid", setData);
          }}
          onKeyDown={handleChange}
          disabled={loading}
          error={data.errors.iccid}
        />
      </div>
    </Modal>
  );
};

export default ChangeIccidModal;
