@use "../../styles/theme.scss" as *;

.main {
  height: 100vh;
  border-radius: 0px 24px 24px 0px;
  background: #fff;
  box-shadow: 5px 0px 20px rgba(0, 0, 0, 0.1);
  position: fixed;
  left: 0px;
  top: 0px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition:
    width 0.3s ease,
    padding 0.3s ease;
  overflow: visible;
  @media (max-height: 700px) {
    &.open {
      padding-bottom: 12px;
      padding-top: 12px;
    }
    &.closed {
      padding-bottom: 12px;
      padding-top: 12px;
    }
  }
  @media (max-width: 1250px) {
    &.open {
      width: 235px;
      padding-left: 12px;
      padding-right: 12px;
      .page {
        padding: 0 12px;
      }
    }
    &.closed {
      width: 68px;
      padding-left: 12px;
      padding-right: 12px;
      .page {
        padding: 0 10px;
      }
    }
  }
}

.mvnoName {
  text-align: center;
  font-size: 16px;
  line-height: 24px;
  margin-top: 12px;
  white-space: nowrap;
  font-weight: 600;
}

.open {
  width: 320px;
  padding: 35px 32px;
  .page {
    justify-content: flex-start;
    padding: 0 5px 0 24px;
  }
  .projectLogo {
    margin-bottom: 0px;
  }
}

.closed {
  width: 104px;
  padding: 35px 16px;
  .airLogo {
    max-width: 100%;
  }
  .page {
    .title {
      opacity: 0;
    }
    justify-content: flex-start;
  }
  .page:not(.expand) {
    &:hover {
      .title {
        opacity: 1;
      }
      box-shadow: rgba(99, 99, 99, 0.1) 0px 2px 8px 0px;
      width: 270px;
    }
  }
}

.heroLogo {
  width: 33px;
  height: auto;
  margin: 0 auto 0 20px;
}

.airLogo {
  max-width: calc(100% - 52px);
  max-height: 50px;
  margin: 0 auto;
  @media (max-height: 700px) {
    max-height: 40px;
  }
}

.projectLogo {
  transition: all 0.3s ease;
  height: 35px;
  max-width: 100%;
  margin: 0 auto;
}

.logoWrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 35px;
  margin-bottom: 32px;
}

.smallLogoWrapper {
  display: grid;
  justify-items: center;
  width: 100%;
  .projectLogo {
    grid-area: 1 / 1 / 2 / 2;
  }
  .backLink {
    .back {
      margin: 0;
    }
    grid-area: 1 / 1 / 2 / 2;
    opacity: 0;
    pointer-events: none;
  }
}

.smallLogoWrapper.mvne {
  &:hover {
    .projectLogo {
      opacity: 0;
      pointer-events: none;
    }
    .backLink {
      opacity: 1;
      pointer-events: all;
    }
  }
}

.back {
  color: $black;
  transition: all 0.2s ease;
  cursor: pointer;
  height: 100%;
  display: flex;
  align-items: center;
  margin-right: 18px;
  margin-left: 8px;
  &:hover {
    color: $dark-orange;
  }
}

.divider {
  height: 0px;
  border-bottom: 1px solid $disabled;
  width: 100%;
  margin: 12px 0px 20px 0px;
  @media (max-height: 700px) {
    margin: 10px 0;
  }
}

.pages {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: visible;
}

.page {
  width: 100%;
  height: 45px;
  border-radius: 1000px;
  display: grid;
  grid-template-columns: 24px 1fr;
  padding: 0 24px;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  color: $black;
  position: relative;
  .title {
    margin-left: 12px;
    opacity: 1;
    position: relative;
    display: flex;
    align-items: center;
    .newTab {
      position: absolute;
      right: 12px;
    }
  }
  &:hover {
    background-color: #f6f6f6;
  }
  span {
    position: relative;
    z-index: 6;
  }
  svg {
    width: 24px;
    transition: all 0.2s ease;
    margin-left: auto;
    position: relative;
    z-index: 6;
  }
  @media (max-height: 700px) {
    height: 35px;
    svg {
      width: 20px;
    }
  }
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  background-color: $light-orange;
  z-index: 5;
  left: 0;
}

.collapseMenu {
  svg {
    transform: rotate(180deg);
  }
}

.pageActive {
  font-weight: 600;
  &:hover {
    background-color: $light-orange;
  }
}

.section {
  color: $black;
  font-size: 10px;
  line-height: 15px;
  height: 60px;
  padding-left: 29px;
  padding-bottom: 4px;
  display: flex;
  align-items: flex-end;
}
