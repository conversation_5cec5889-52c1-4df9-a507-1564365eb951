@use "./theme.scss" as *;

.main {
  padding: 32px 40px;
  &.open {
    .subscriberTile {
      @media (max-width: 1420px) {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
      }
    }
    .userDetailsSection {
      @media (max-width: 1420px) {
        border-bottom: 1px solid #e0dcdc;
        border-right: none;
        padding-right: 0px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .portingContainer {
          display: flex;
          flex-wrap: wrap;
        }
      }
    }
    .planDetailsSection {
      @media (max-width: 1420px) {
        padding-top: 32px;
        padding-left: 0px;
      }
    }
  }
  &.closed {
    .subscriberTile {
      @media (max-width: 1200px) {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
      }
    }
    .userDetailsSection {
      @media (max-width: 1200px) {
        border-bottom: 1px solid #e0dcdc;
        border-right: none;
        padding-right: 0px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .portingContainer {
          display: flex;
          flex-wrap: wrap;
        }
      }
    }
    .planDetailsSection {
      @media (max-width: 1200px) {
        padding-top: 32px;
        padding-left: 0px;
      }
    }
  }
  @media (max-width: 1250px) {
    padding: 24px;
  }
}

.topBar {
  display: flex;
  margin-bottom: 6px;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  @media (max-width: 1250px) {
    margin-bottom: 12px;
  }
}

.breadcrumbs {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: $black;
  a {
    color: $black;
  }
  .activeCrumb {
    font-weight: 700;
  }
}

.backLink {
  cursor: pointer;
  &:hover {
    color: $orange;
  }
}

.subscriberName {
  font-size: 20px;
  font-weight: 700;
  line-height: 30px;
  margin-bottom: 12px;
}

.selectionWrapper {
  display: flex;
  align-items: center;
}

.selection {
  height: 47px;
  border-radius: 1000px;
  color: $black;
  font-size: 14px;
  font-weight: 600;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    color: $dark-orange;
  }
  span {
    position: relative;
    z-index: 6;
  }
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1000px;
  background-color: #fcc9a5;
  z-index: 5;
  left: 0;
}

.activeSelection {
  cursor: auto;
  &:hover {
    color: $black;
  }
}

.userDetailsSection {
  padding-right: 32px;
  border-right: 1px solid #e0dcdc;
}

.nameContainer {
  display: flex;
  flex-direction: column-reverse;
  justify-content: space-between;
  margin-bottom: 32px;
  .name {
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
  }
  .toolsContainer {
    display: flex;
    margin-left: auto;
  }
  .toolButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 12px;
    cursor: pointer;
    border: none;
    outline: none;
    background-color: #fff;
    &:hover {
      background-color: #f2f2f2;
    }
  }
}

.detailsItem {
  display: grid;
  grid-template-columns: 24px 1fr;
  margin-bottom: 24px;
  grid-column-gap: 12px;
  word-break: break-all;
  svg {
    width: 24px;
    height: 24px;
  }
  &:last-of-type {
    margin-bottom: 32px;
  }
}

.planDetailsSection {
  padding-left: 32px;
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 120px;
  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }
  h3 {
    width: 100%;
    text-align: center;
  }
}

.loadingError {
  background: #fff;
  width: 100%;
  padding: 50px;
  border-radius: 24px;
  display: flex;
  align-items: center;
}

.addSubscription {
  position: relative;
  button {
    position: relative;
    z-index: 10;
  }
}

.addSubSelectContainer {
  position: absolute;
  top: 25px;
  z-index: 1;
  right: 0;
  left: 0;
}

.addSubSelect {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  padding-top: 25px;
  border-radius: 0 0 24px 24px;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.1);

  .addButton {
    height: 50px;
    width: 100%;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: color 0.2s ease;
    color: $black;
    &:hover {
      color: $orange;
    }
  }
}
