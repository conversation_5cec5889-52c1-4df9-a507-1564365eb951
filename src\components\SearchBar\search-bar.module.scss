@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  display: grid;
  grid-template-columns: 24px auto 162px;
  align-items: center;
  background: #fff;
  border-radius: 1000px;
  height: 56px;
  padding: 5px;
  padding-left: 24px;
  position: relative;
  &.grey {
    background: #f7f6f6;
    margin: 0px;
  }
  &.small {
    height: 50px;
    grid-template-columns: 24px 1fr 150px;
  }
}

.highlight {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  transition: all 0.2s ease;
  border-radius: 1000px;
  z-index: 1;
  pointer-events: none;
}

input:focus ~ .highlight {
  box-shadow: 0 0 0 1.5px $orange;
}

.highlight.loadingHighlight {
  box-shadow: 0 0 0 1.5px $orange;
  opacity: 0.5;
}

.input {
  border: none;
  font-size: 16px;
  color: $black;
  width: 100%;
  margin-left: 16px;
  &.grey {
    background: #f7f6f6;
    color: #45474f;
  }
  &:disabled {
    color: $disabled-text;
    background: none;
  }
  &:focus {
    outline: none;
  }
  &::placeholder {
    color: $placeholder;
  }
}
