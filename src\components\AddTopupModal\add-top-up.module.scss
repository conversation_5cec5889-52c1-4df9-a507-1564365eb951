@use "../../styles/theme.scss" as *;

.plansMain {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.plans {
  margin-right: 12px;
}

.searchBar {
  display: flex;
  width: 93%;
  align-items: center;
  justify-content: space-between;
  .title {
    margin-right: 27px;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
  }
}

.info {
  width: 100%;
  padding: 16px 24px;
  border-radius: 16px;
  background: $light-orange;
  margin-bottom: 24px;
  font-weight: 500;
  font-size: 14px;
  line-height: 21px;
  display: flex;
  align-items: flex-start;
  svg {
    //color: $orange;
    vertical-align: middle;
    margin-right: 12px;
  }
}
