import { useState } from "react";
import { Input } from "../Input";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import styles from "./change-imei-modal.module.scss";
import { validateAll } from "indicative/validator";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";

const fields = ["imei"];
const rules = getRules(fields);
const messages = getMessages(fields);

const ChangeImeiModal = ({ show, setShow, plan, subInfo, repopulate }: any) => {
  const dispatch = useDispatch();

  const handleChange = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPostAuth("/accounts/update", {
          subscriber: plan.mdn,
          imei: data.imei,
          iccid: plan.iccid,
          mid: subInfo.mid,
          subscriptionId: plan.id,
        })
          .then((response) => {
            setLoading(false);
            setData(createStateObject(fields));
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
            setShow("");
            repopulate();
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message:
                  error.response.data.message ||
                  "Something went wrong, please try again.",
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const [data, setData] = useState(createStateObject(fields));

  const [loading, setLoading] = useState(false);

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={handleChange}
      close={() => {
        clearInput("imei", setData);
        setShow("");
      }}
      loading={loading}
    >
      <div className={styles.main}>
        <h4>Change IMEI</h4>
        <Input
          label={labels.imei}
          placeholder={placeholders.imei}
          value={data.imei}
          onChange={(e: any) => {
            handleInputChange("imei", e, data, setData);
          }}
          clear={() => {
            clearInput("imei", setData);
          }}
          onKeyDown={handleChange}
          error={data.errors.imei}
          disabled={loading}
        />
      </div>
    </Modal>
  );
};

export default ChangeImeiModal;
