@use "../../styles/theme.scss" as *;

.menuButton {
  height: 32px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &:hover {
    color: $orange;
  }
  svg {
    display: inline;
    vertical-align: middle;
    transition: transform 0.2s ease;
    margin-left: 5px;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }
}

.box {
  height: 100%;
}

.menuItem {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: $black;
  padding: 0;
  font-size: 14px;
  line-height: 24px;
  padding: 6px;
  font-weight: 500;
  svg {
    margin-right: 12px;
    vertical-align: middle;
  }
  &:hover {
    color: $orange;
    background: none;
  }
}

.container {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.column {
  overflow-y: auto;
  height: 200px;
  padding-right: 15px;
}
