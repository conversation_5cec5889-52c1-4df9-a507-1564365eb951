import styles from "./edit-user.module.scss";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import { Input } from "../Input";
import SelectInput from "../SelectInput";
import { useEffect, useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch } from "react-redux";
import { ApiGet, ApiPatch, ApiPostAuth } from "../../pages/api/api";
import Toggle from "../Toggle";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import MultiSelectInput from "../MultiSelectInput";
import { useParams } from "react-router-dom";

const fields = ["firstName", "lastName", "email", "role"];
const rules = getRules(fields);
const messages = getMessages(fields);

const EditUserModal = ({
  show,
  setShow,
  user,
  resetActiveUser,
  repopulateUsers,
  id,
  self,
  clearContainer,
  mvne,
}: any) => {
  const dispatch = useDispatch();

  const { mvnoId } = useParams();

  const [data, setData] = useState(createStateObject(fields, " "));
  const [twoFactor, setTwoFactor] = useState(false);

  // Populate with current user's data
  useEffect(() => {
    if (user && show) {
      setData({
        ...data,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.roleName,
      });
      if (user.channels) {
        setSelectedChannels(
          user.channels.map((channel: any) => ({
            key: channel.id,
            label: channel.name,
          })),
        );
      }
      setTwoFactor(user.is2faEnabled);
    }
  }, [user, show]);

  // Reset modal data when closed
  const reset = () => {
    setTimeout(() => {
      setData(createStateObject(fields, " "));
    }, 300);
    if (resetActiveUser) {
      resetActiveUser();
    }
    setLoading(false);
    setShow(false);
  };

  const [channelOptions, setChannelOptions] = useState([] as any);
  const [selectedChannels, setSelectedChannels] = useState([] as any);

  useEffect(() => {
    ApiGet(`/channels/mvno/${mvnoId}`).then((response) => {
      setChannelOptions(
        response.data.map((channel: any) => ({
          key: channel.id,
          label: channel.name,
        })),
      );
      console.log(response);
    });
  }, []);

  // Handles creation of new user
  const editUser = () => {
    const testData = {
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      email: data.email.trim(),
      role: data.role.trim(),
    };

    validateAll(testData, rules, messages)
      .then((response) => {
        console.log(twoFactor);
        setLoading(true);
        let request = {
          userId: id,
          firstName: testData.firstName,
          lastName: testData.lastName,
          email: testData.email,
          enable2fa: twoFactor,
          roleId:
            testData.role === "mvne" ? 3 : testData.role === "Agent" ? 1 : 2,
        } as any;
        if (!self) {
          request.channelIds = selectedChannels.map(
            (channel: any) => channel.key,
          );
        }
        ApiPatch("/users/edit", request)
          .then((response) => {
            if (mvne) {
              repopulateUsers(request);
            } else {
              repopulateUsers();
            }
            reset();
            console.log(response.data.message);
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const [loading, setLoading] = useState(false);

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      image="/edit_user_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={editUser}
      close={reset}
      loading={loading}
      clearContainer={clearContainer}
      fullSize
      title="Edit User"
    >
      <div className={`${styles.main} normal-select-input`}>
        {fields.map((prop) =>
          prop === "role" ? (
            !self ? (
              <SelectInput
                key="edit-role"
                placeholder="Role"
                options={["Agent", "Admin"]}
                selected={data.role}
                onChange={(value: any) => {
                  setData({
                    ...data,
                    role: value,
                    errors: {
                      ...data.errors,
                      role: "",
                    },
                  });
                }}
                disabled={loading}
                error={data.errors.role}
              />
            ) : (
              ""
            )
          ) : (
            <Input
              key={`edit-user-${prop}`}
              label={labels[prop]}
              placeholder={placeholders[prop]}
              value={data[prop]}
              onChange={(e: any) => {
                handleInputChange(prop, e, data, setData);
              }}
              error={data.errors[prop]}
              onKeyDown={editUser}
              clear={() => {
                clearInput(prop, setData);
              }}
              disabled={loading}
              white
            />
          ),
        )}
        {!self && (
          <>
            <div style={{ height: 12 }} />
            <MultiSelectInput
              key="channel-select"
              placeholder="Channels"
              options={channelOptions}
              selected={selectedChannels}
              onAdd={(value: any) => {
                setSelectedChannels((prev: any) => [...prev, value]);
              }}
              onRemove={(value: any) => {
                setSelectedChannels((prev: any) =>
                  prev.filter((item: any) => item.key !== value.key),
                );
              }}
              disabled={loading}
              error={data.errors.role}
            />
          </>
        )}
        <div
          style={{ marginTop: !self ? 12 : 0, opacity: loading ? 0.3 : 1 }}
          className={styles.twoFactor}
        >
          <div className={styles.twoFaLabel}>Two-Factor Authentication</div>
          <div className={styles.toggleContainer}>
            <div className={styles.onOff}>{twoFactor ? "On" : "Off"}</div>
            <Toggle
              on={twoFactor}
              onChange={() => {
                setTwoFactor((prev: boolean) => !prev);
              }}
              disabled={loading}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default EditUserModal;
