import styles from "./select-input.module.scss";
import {
  ControlledMenu,
  MenuItem,
  useMenuState,
  useClick,
} from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown, X } from "../svgs";
import { useEffect, useRef, useState } from "react";
import { Collapse } from "@mui/material";
import $ from "jquery";
import { createPortal } from "react-dom";

const MultiSelectInput = ({
  options,
  selected,
  placeholder,
  onAdd,
  onRemove,
  readonly,
  disabled,
  id = null,
  error,
}: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });
  const anchorProps = useClick(menuProps.state, toggleMenu);

  const [openUpwards, setOpenUpwards] = useState(false);

  useEffect(() => {
    if ($(".normal-select-input ul.szh-menu")) {
      let spaceBottom =
        $(window).height()! - $(".select-input").offset()!.top - 56;
      let menuHeight = $(".normal-select-input ul.szh-menu").outerHeight()!;
      if (spaceBottom < menuHeight) {
        setOpenUpwards(true);
      } else {
        setOpenUpwards(false);
      }
    }
  }, [menuProps]);

  return (
    <div
      className={`${styles.box} normal-select-input ${
        openUpwards && "select-upwards"
      }`}
    >
      {selected.length > 0 && (
        <div className={`${styles.label} ${disabled && styles.disabled}`}>
          {placeholder}
        </div>
      )}
      <div
        ref={ref}
        {...anchorProps}
        className={`${styles.menuButton} ${selected && styles.selected} ${
          (disabled || readonly) && styles.disabled
        } ${readonly && styles.readonly} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        } ${error && styles.error} select-input`}
        onClick={() => toggleMenu(true)}
      >
        {selected.length ? (
          <div className={styles.selectedContainer}>
            {selected.map((item: any) => (
              <div className={styles.selectedLabel}>
                {item.label}
                <div
                  className={styles.remove}
                  onClick={(e: any) => {
                    e.stopPropagation();
                    onRemove(item);
                  }}
                >
                  <X />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <span style={{ marginRight: 4 }}>{placeholder}</span>
        )}
        {!readonly && <ChevronDown />}
      </div>
      <Collapse in={error ? true : false}>
        <p className={styles.errorText} id={`${id}-error`}>
          {error || <br />}
        </p>
      </Collapse>
      {createPortal(
        <div className="normal-select-input">
          <ControlledMenu
            {...menuProps}
            anchorRef={ref}
            onClose={() => toggleMenu(false)}
            align="start"
            position="auto"
            viewScroll="auto"
            direction={openUpwards ? "top" : "bottom"}
            onItemClick={(e) => (e.keepOpen = true)}
            overflow="auto"
          >
            {options.map((item: any) => (
              <MenuItem
                className={`${styles.menuItem} ${
                  selected.includes(item) && styles.selected
                }`}
                onClick={() => {
                  if (
                    selected.some(
                      (selectedItem: any) => selectedItem.key === item.key,
                    )
                  ) {
                    onRemove(item);
                  } else {
                    onAdd(item);
                  }
                }}
                key={item}
              >
                {item.label}
              </MenuItem>
            ))}
          </ControlledMenu>
        </div>,
        document.getElementById("root")!,
      )}
    </div>
  );
};

export default MultiSelectInput;
