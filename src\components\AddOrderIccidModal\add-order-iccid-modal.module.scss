.main {
  width: 100%;
  max-width: 350px;
  margin: 0 auto;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  h3 {
    margin: 0 0 12px 0;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
  }
  h4 {
    margin: 0 0 40px 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
  }
}

.summaryContainer {
  background: #f7f6f6;
  border-radius: 16px;
  padding: 16px 24px;
  width: 100%;
  margin-bottom: 24px;
  .summarySection {
    display: grid;
    grid-template-columns: 120px 1fr;
    grid-column-gap: 12px;
    justify-items: start;
    text-align: start;
    margin-bottom: 13px;
    font-size: 14px;
    line-height: 21px;
    &:last-child {
      margin-bottom: 0px;
    }
  }
  .editButton {
    cursor: pointer;
  }
}
