import styles from "./view-all-note.module.scss";
import Modal from "../Modal";
import { formatDateWithTime } from "../utils/formatDate";
import ViewNoteModal from "../ViewNoteModal";
import { useState } from "react";
import NoteSingle from "../NoteSingle";

const ViewAllNotesModal = ({ show, setShow, data }: any) => {
  console.log("data", data);
  const [viewNote, setViewNote] = useState(false);
  const [Note, setNote] = useState(null as any);
  return (
    <>
      <Modal
        cancelButton="Close Window"
        image="/view-user-Illustration.svg"
        show={show}
        setShow={setShow}
        close={setShow}
        onClose={() => setShow(false)}
        fullSize
        title="Notes"
      >
        <ViewNoteModal
          show={viewNote}
          setShow={setViewNote}
          item={Note}
          clearContainer
        />
        <div className={`${styles.main} `}>
          <div className={styles.notesContainer}>
            {data?.map((item: any, index: any) => (
              <NoteSingle
                item={item}
                key={`note-${index}`}
                cutoff
                setViewNote={setViewNote}
                setNote={setNote}
              />
            ))}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ViewAllNotesModal;
