import styles from "./send-email.module.scss";
import Modal from "../Modal";
import { PaperPlane } from "../svgs";
import { Input } from "../Input";
import { useState } from "react";
import { clearInput } from "../utils/InputHandlers";
import TextArea from "../TextArea";

const SendEmailModal = ({ show, setShow }: any) => {
  const reset = () => {};

  const [loading, setLoading] = useState(false);
  const [body, setBody] = useState("" as any);
  const [subject, setSubject] = useState("" as any);

  const createNote = () => {
    const finalEmail = {
      subject: subject,
      body: body,
    };
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      setShow(false);
      reset();
      setBody("");
      setSubject("");
    }, 300);
  };

  return (
    <Modal
      saveButton={
        <>
          <PaperPlane />
          Send Email
        </>
      }
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={createNote}
      close={setShow}
      onClose={() => setShow(false)}
      loading={loading}
    >
      <div className={`${styles.main} `}>
        <h3>Send Email</h3>
        <Input
          key="subject"
          label="Subject"
          placeholder="Enter Subject"
          value={subject}
          onChange={(e: any) => {
            setSubject(e.target.value);
          }}
          // onKeyDown={editUser}
          clear={() => {
            clearInput(subject, setSubject);
          }}
          disabled={loading}
          white
        />
        <TextArea
          label="Message"
          placeholder="Enter your message"
          value={body}
          onChange={(e: any) => {
            setBody(e.target.value);
          }}
          error={body.error}
        />
      </div>
    </Modal>
  );
};

export default SendEmailModal;
