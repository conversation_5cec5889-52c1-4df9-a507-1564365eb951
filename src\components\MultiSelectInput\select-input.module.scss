@use "../../styles/theme.scss" as *;

.box {
  width: 100%;
  position: relative;
}

.menuButton {
  min-height: 56px;
  width: 100%;
  border-radius: 8px;
  border: 1px solid $grey;
  padding: 8px 12px 8px 16px;
  transition: border 0.1s ease;
  font-size: 16px;
  color: $black;
  background: none;
  display: grid;
  grid-template-columns: 1fr 24px;
  align-items: center;
  justify-content: space-between;
  color: $placeholder;
  cursor: pointer;
  transition: background 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &.selected {
    color: $black;
  }
  &:hover {
    border: 1px solid $black;
  }
  &.error {
    border: 2px solid $error !important;
  }
  &.disabled {
    border: 1px solid $disabled;
    color: $disabled-text;
    background: none;
    pointer-events: none;
    &.readonly {
      color: $black;
      border: 1px solid #cacaca;
    }
  }
  svg {
    transition: all 0.2s ease;
  }
  &.iconOpen {
    border: 2px solid $orange !important;
    svg {
      transform: rotate(180deg);
    }
  }
}

.label {
  position: absolute;
  z-index: 10;
  background: $off-white;
  transition: all 0.3s ease;
  pointer-events: none;
  color: $placeholder;
  top: -5px;
  left: 12px;
  font-size: 12px;
  line-height: 16px;
  padding: 0 4px;
  background: #fff;
  &.disabled {
    color: $disabled-text;
  }
  &.labelError {
    color: $error !important;
  }
}

.selectedContainer {
  display: flex;
  flex-wrap: wrap;
  .selectedLabel {
    font-size: 14px;
    font-weight: 600;
    background-color: $light-orange;
    padding: 2px 8px 2px 12px;
    border-radius: 6px;
    margin: 3px;
    display: flex;
    align-items: center;
    cursor: auto;
    .remove {
      margin-left: 6px;
      cursor: pointer;
      svg {
        vertical-align: middle;
        width: 12px;
        height: 12px;
        transform: rotate(0deg) !important;
      }
    }
  }
}

.menuItem {
  height: 48px;
  transition: all 0.1s ease;
  color: $black;
  padding: 0 10px;
  line-height: 21px;
  font-size: 14px;
  font-weight: 300;
  background: $off-white;
  margin-bottom: 5px;
  &:hover {
    background: $light-orange;
  }
  &.selected {
    background: $faded-orange;
    font-weight: 500;
  }
}

.errorText {
  margin: 0px 0px 0px 5px;
  font-size: 12px;
  color: $error;
}
