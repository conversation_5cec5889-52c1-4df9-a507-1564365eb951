import { useState, useEffect } from "react";
import { Calendar, CaretDown, CaretUp, ChevronDown } from "../svgs";
import styles from "./date-picker.module.scss";
import {
  getMonthDays,
  getMonthFirstDay,
  zeroPad,
  getMonthArray,
  formatDate,
  isDateNow,
} from "../utils/dateHelpers";
import MonthYearSelect from "../MonthYearSelect";
import Button from "../Button";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { useRef } from "react";
import MonthPicker from "../MonthPicker";

const now = new Date();

const DatePicker = ({
  label,
  masterFrom,
  masterUntil,
  onChange,
  background,
  reports,
}: any) => {
  const [fromDate, setFromDate] = useState(null as any);
  const [untilDate, setUntilDate] = useState(null as any);
  useEffect(() => {
    setFromDate(masterFrom);
    setUntilDate(masterUntil);
  }, [masterFrom, masterUntil]);

  const daysLetters = ["M", "T", "W", "T", "F", "S", "S"];

  const [month, setMonth] = useState(now.getMonth() + 1);
  const [year, setYear] = useState(now.getFullYear());
  const [displayMonths, setDisplayMonths] = useState([] as any);

  const [hoverDate, setHoverDate] = useState(null as any);

  useEffect(() => {
    const days = getMonthDays(month, year);
    const first = getMonthFirstDay(month, year);
    const newMonthArray = getMonthArray(
      Array.from({ length: days }, (v, i) => ({
        type: "main",
        value: i + 1,
        date: new Date(`${year}/${zeroPad(month)}/${zeroPad(i + 1)}`),
      })),
      days,
      first,
      month,
      year
    );
    setDisplayMonths(newMonthArray);
  }, [month, year]);

  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const reset = (e: any) => {
    console.log(e);
    toggleMenu(false);
    setFromDate(masterFrom);
    setUntilDate(masterUntil);
  };
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return (
    <div className={`${styles.box} date-select`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${reports && styles.reports} ${
          background && styles.background
        } ${
          (menuProps.state === "open" || menuProps.state === "opening") &&
          !reports
            ? styles.iconOpen
            : styles.iconClosed
        }`}
        onClick={(e: any) => {
          e.stopPropagation();
          if (menuProps.state === "closing") {
            toggleMenu(false);
          } else {
            toggleMenu(true);
          }
        }}
      >
        {label}
        {!reports && <ChevronDown />}
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={reset}
        align="start"
        position="auto"
        viewScroll="close"
        onItemClick={(e: any) => (e.stopPropagation = true)}
      >
        <div className={styles.main}>
          <div className={styles.input}>
            <Calendar />
            <div className={styles.text}>
              {fromDate && formatDate(fromDate)}
              {fromDate && " - "}
              {untilDate && formatDate(untilDate)}
            </div>
          </div>
          <div className={styles.mainDatePicker}>
            <div className={styles.fromUntil}>
              <div
                style={{ borderRadius: "8px 0px 0px 0px" }}
                className={`${styles.selection} ${
                  untilDate === null && styles.active
                }`}
              >
                From
              </div>
              <div
                style={{ borderRadius: "0px 8px 0px 0px" }}
                className={`${styles.selection} ${
                  untilDate !== null && styles.active
                }`}
              >
                Until
              </div>
            </div>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                padding: "12px 26px 0 26px",
              }}
            >
              <MonthPicker
                activeMonth={month}
                setActiveMonth={setMonth}
                activeYear={year}
                setActiveYear={setYear}
              />
              <input
                style={{
                  position: "absolute",
                  opacity: 0,
                  zIndex: -1,
                }}
                id="close-month-menu"
              />
              <div className={styles.prevNext}>
                <div
                  className={styles.prev}
                  onClick={() => {
                    if (month === 1) {
                      setMonth(12);
                      setYear(year - 1);
                    } else {
                      setMonth(month - 1);
                    }
                  }}
                >
                  <CaretUp />
                </div>
                <div
                  className={styles.next}
                  onClick={() => {
                    if (month === 12) {
                      setMonth(1);
                      setYear(year + 1);
                    } else {
                      setMonth(month + 1);
                    }
                  }}
                >
                  <CaretDown />
                </div>
              </div>
            </div>
            <div className={styles.calendar}>
              <div className={styles.days}>
                {daysLetters.map((letter) => (
                  <div key={letter} className={styles.letter}>
                    {letter}
                  </div>
                ))}
              </div>
              <div className={styles.datesGrid}>
                {displayMonths.map((day: any) => (
                  <div
                    className={styles.cellContainer}
                    onMouseEnter={() => {
                      setHoverDate(day.date);
                    }}
                    onMouseLeave={() => {
                      setHoverDate(null as any);
                    }}
                    key={`${day.type}-date-${day.date.getTime()}`}
                  >
                    <div
                      className={`${styles.gridCell} ${
                        untilDate
                          ? day.date.getTime() >= fromDate.getTime() &&
                            day.date.getTime() <= untilDate.getTime()
                            ? styles.highlight
                            : ""
                          : fromDate && hoverDate && !untilDate
                          ? day.date.getTime() >= fromDate.getTime() &&
                            day.date.getTime() <= hoverDate.getTime() &&
                            hoverDate.getTime() !== fromDate.getTime()
                            ? styles.highlight
                            : ""
                          : ""
                      } ${
                        fromDate
                          ? fromDate.getTime() === day.date.getTime()
                            ? styles.curveLeft
                            : ""
                          : ""
                      } ${
                        untilDate
                          ? untilDate.getTime() === day.date.getTime()
                            ? styles.curveRight
                            : ""
                          : hoverDate && !untilDate
                          ? hoverDate.getTime() === day.date.getTime()
                            ? styles.curveRight
                            : ""
                          : ""
                      }`}
                    />
                    <div
                      className={`${styles.day} ${
                        day.date > today && styles.disable
                      } ${isDateNow(day.date) && styles.now} ${
                        day.type === "pad" && styles.pad
                      } ${
                        fromDate
                          ? fromDate.getTime() === day.date.getTime()
                            ? styles.active
                            : untilDate
                            ? untilDate.getTime() === day.date.getTime()
                              ? styles.active
                              : ""
                            : ""
                          : ""
                      }`}
                      onClick={() => {
                        if (fromDate === null || (fromDate && untilDate)) {
                          setFromDate(day.date);
                          setUntilDate(null as any);
                        } else if (day.date.getTime() < fromDate.getTime()) {
                          setUntilDate(fromDate);
                          setFromDate(day.date);
                        } else {
                          setUntilDate(day.date);
                        }
                      }}
                    >
                      {day.value}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className={styles.buttons}>
              <Button
                onClick={reset}
                style={{ minWidth: "initial" }}
                color="quaternary"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  if (fromDate) {
                    if (untilDate) {
                      onChange(fromDate, untilDate);
                    } else {
                      onChange(fromDate, fromDate);
                    }
                    toggleMenu(false);
                  }
                }}
                style={{ minWidth: "initial" }}
              >
                Apply
              </Button>
            </div>
          </div>
        </div>
      </ControlledMenu>
    </div>
  );
};

export default DatePicker;
