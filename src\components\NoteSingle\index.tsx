import { formatDateWithTime } from "../utils/formatDate";
import styles from "./note-single.module.scss";
import { useState, useEffect } from "react";

const NoteSingle = ({ cutoff, item, setViewNote, setNote }: any) => {
  const [formatted, setFormatted] = useState({
    name: "",
    body: "",
  });

  useEffect(() => {
    let split, name, body;

    if (item.text.includes(" || ")) {
      split = item.text.split(" || ");
      name = split[0];
      body = split.slice(1).join(" || ");
    } else {
      name = "";
      body = item.text;
    }

    setFormatted({
      name: name,
      body: body,
    });
  }, [item]);

  return (
    <div className={styles.note}>
      <div className={styles.top}>
        <div className={styles.by}>{formatted.name}</div>
        <button
          className={styles.viewButton}
          onClick={() => {
            setViewNote(true);
            setNote(item);
          }}
        >
          View
        </button>
      </div>
      <div className={`${styles.noteContent} ${cutoff && styles.cutoff}`}>
        {formatted.body.slice(0, 165)}
        {formatted.body.length > 165 && "..."}
      </div>
      <div className={styles.date}>
        {formatDateWithTime(new Date(item.createdDate))}
      </div>
    </div>
  );
};

export default NoteSingle;
