@use "./theme.scss" as *;

.main {
  padding: 32px 40px;
}

.topBar {
  display: flex;
  margin-bottom: 6px;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}

.breadcrumbs {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: $black;
  a {
    color: $black;
  }
  .activeCrumb {
    font-weight: 700;
  }
}

.backLink {
  cursor: pointer;
  &:hover {
    color: $orange;
  }
}

.subscriberName {
  font-size: 20px;
  font-weight: 700;
  line-height: 30px;
  margin-bottom: 12px;
}

.selectionWrapper {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.selection {
  height: 47px;
  border-radius: 1000px;
  color: $black;
  font-size: 14px;
  font-weight: 600;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    color: $dark-orange;
  }
  span {
    position: relative;
    z-index: 6;
  }
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1000px;
  background-color: #fcc9a5;
  z-index: 5;
  left: 0;
}

.activeSelection {
  cursor: auto;
  &:hover {
    color: $black;
  }
}
