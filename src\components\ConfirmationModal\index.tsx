import styles from "./confirmation-modal.module.scss";
import Button from "../Button";
import { Fade } from "@mui/material";
import { useEffect } from "react";
import $ from "jquery";

const ConfirmationModal = ({
  show,
  close,
  proceed,
  continueButton,
  cancelButton = "Cancel",
  loading,
  children,
}: any) => {
  useEffect(() => {
    if (show) {
      $(document.body).css("overflow-y", "hidden");
    } else {
      $(document.body).css("overflow-y", "scroll");
    }
  }, [show]);

  return (
    <Fade in={show} unmountOnExit>
      <div className={styles.container}>
        <div className={styles.modal}>
          <div className={styles.main} id="modal-scroll">
            {children}
            <div className={styles.buttons}>
              <Button
                onClick={close}
                style={{ marginRight: 16 }}
                color="tertiary"
                disabled={loading}
              >
                {cancelButton}
              </Button>
              <Button
                onClick={() => {
                  close();
                  proceed();
                }}
                style={{ height: "auto", padding: "13px 24px" }}
                loading={loading}
              >
                {continueButton}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Fade>
  );
};

export default ConfirmationModal;
