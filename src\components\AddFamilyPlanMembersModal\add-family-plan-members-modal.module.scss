@use "../../styles/mixins.module.scss" as *;
@use "../../styles/theme.scss" as *;

.memberSubscriptions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-right: 16px;
}

.memberSubscription {
  border: 1px solid #d8d8d8;
  border-radius: 16px;
  overflow: hidden;
}

.addSubscriptionBtn {
  margin-top: 24px;
}

.memberSubscriptionHeader {
  display: flex;
  align-items: center;
  padding: 16px;
  cursor: pointer;

  &:hover {
    background: #f5f5f5;
  }

  .removeMemberSubscriptionBtn {
    margin-left: auto;
    @include resetBtn;
    font-weight: 400;
    text-decoration: underline;
  }
}

.memberSubscriptionHeaderTitle {
  font-weight: 600;
  color: #45474f;
}

.memberSubscriptionHeaderCaret {
  // margin-left: auto;
  // reset button styles
  border: none;
  background: none;
  padding: 0;
  padding-left: 24px;
  cursor: pointer;
  transition: all 0.1s ease;
  svg {
    transition: all 0.1s ease;
  }
  &.open {
    svg {
      transform: rotate(180deg);
    }
  }
}

.memberSubscriptionBody {
  padding: 16px;
  border-top: 1px solid #d8d8d8;
}

.memberSubscriptionEmailField {
  .emailFieldContainer {
    display: flex;
    align-items: center;
    gap: 16px;

    .inputWrapper {
      width: 365px;
    }

    button {
      @include resetBtn;
      text-decoration: underline;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

.emailCheckResult {
  display: flex;
  align-items: center;
  gap: 6px;
  // margin-top: 8px;
  color: #e88e08;

  &.hasAccount {
    color: #0b9c32;
  }
}

.createAccountBtn {
  margin-top: 16px;
}

.selectExistingMDN {
  margin-top: 16px;

  .selectExistingMDNInput {
    cursor: pointer;
    height: 56px;
    width: 365px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-radius: 8px;
    border: 1px solid #74767e;
    color: #45474f;

    &.open {
      svg {
        transform: rotate(180deg);
      }
    }
  }
}

:global {
  .select-mdn-dropdown .szh-menu {
    width: 365px;
    border-radius: 16px;
    z-index: 10000;
  }
}

.createNewMDNBtn {
  @include resetBtn;
  color: #45474f;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding-block: 16px;
}

.MDNMenuItem {
  @include resetBtn;
  display: flex;
  padding: 16px 24px;
  color: #45474f;
  gap: 8px;
  font-weight: 400;
  width: 100%;
}

.existingSub {
  border-radius: 16px;
  background: #f7f6f6;
  padding: 16px;
  margin-top: 16px;
  width: 365px;

  .row {
    display: grid;
    grid-template-columns: 52px 1fr;
    gap: 12px;
  }
}

.newSubFields {
  width: 365px;
}

.isAdmin {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 16px;
}

.setAdmin {
  @include resetBtn;
  margin-left: 16px;
  font-weight: 400;
  text-decoration: underline;
}

.backStep {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  cursor: pointer;
}

.reviewStepError {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 32px;

  .errorTitle {
    font-size: 16px;
    color: #45474f;
    font-weight: 600;
    text-align: center;
  }

  img {
    width: 100px;
  }

  .errorDescription {
    font-size: 14px;
    line-height: 24px;
    color: #45474f;
    text-align: center;
    max-width: 360px;
    border: 1px solid #45474f;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
  }
}

.reviewStep {
  margin-inline: auto;
  text-align: center;

  .subscriptions {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .subscription {
    background: #fbfbfb;
    border-radius: 24px;
    padding: 20px 24px;

    .subscriptionHeader {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      border-bottom: 1px solid #e6e6e6;
      padding-bottom: 8px;

      .title {
        color: #45474f;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
      }
    }

    .subscriptionList {
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .subscriptionListItem {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .mdn,
      .plan {
        display: flex;
        justify-content: space-between;
        gap: 24px;
      }
    }

    .total {
      display: flex;
      justify-content: space-between;
      margin-top: 14px;
      font-weight: 600;
      border-top: 1px solid #e6e6e6;
      padding-top: 8px;
    }
  }

  .summary {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 34px;
    border-top: 1px solid #e6e6e6;
    padding-top: 14px;
    margin-inline: 24px;

    .item {
      display: flex;
      justify-content: space-between;
      gap: 24px;
    }

    .total {
      font-weight: 700;
    }

    .discount {
      color: #0a7f16;
    }
  }
}

.cancelConfirmContent {
  text-align: center;
  margin: auto;
}

.validationErrors {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 12px 16px;
  margin: 12px 16px;

  .validationErrorsHeader {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #dc2626;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 8px;

    svg {
      width: 16px;
      height: 16px;
      fill: #dc2626;
    }
  }

  .validationErrorsList {
    margin: 0;
    padding-left: 20px;
    color: #991b1b;
    font-size: 13px;
    line-height: 1.4;

    li {
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.mdnSearchContainer {
  padding: 8px 12px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;

  .mdnSearchInput {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    outline: none;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &::placeholder {
      color: #9ca3af;
    }
  }
}

.noMDNResults {
  padding: 12px 16px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
  font-style: italic;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}