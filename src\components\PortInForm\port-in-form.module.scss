@use "../../styles/theme.scss" as *;

.main {
  margin: auto 0px;
  width: 100%;
  // max-width: 350px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.back {
  position: absolute;
  top: 35px;
  left: 15px;
  cursor: pointer;
  transition: all 0.1s ease;
  &:hover {
    color: $orange;
  }
}

.section {
  width: 100%;
  h3 {
    font-size: 24px;
    line-height: 36px;
    font-weight: 700;
    margin: 0;
  }
  .info {
    text-align: center;
    font-size: 14px;
    line-height: 21px;
    margin: 12px 0px 35px 0px;
  }
}

.inputTitle {
  color: $placeholder;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 12px;
}

.eligible {
  border: 2px solid $success;
  color: $success;
  margin-bottom: 12px;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  display: grid;
  grid-template-columns: 32px 1fr;
  grid-column-gap: 12px;
}
.inputsGrid {
  display: grid;
  width: 100%;
  max-width: 389px;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(5, auto);
  grid-column-gap: 16px;
  grid-row: 16px;
  form:nth-child(1) {
    grid-area: 1/1/2/2;
  }
  form:nth-child(2) {
    grid-area: 1/2/2/3;
  }
  form:nth-child(3) {
    grid-area: 2/1/3/3;
  }
  form:nth-child(4) {
    grid-area: 3/1/4/3;
  }
}
.oldInputsGrid {
  display: grid;
  align-items: end;
  width: 389px;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(5, auto);
  grid-column-gap: 16px;
  grid-row: 16px;
  margin: 0 auto;
}
.oldNumber {
  width: 430px;
}

.subscriberCont {
  display: grid;
  width: 389px;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(6, auto);
  grid-column-gap: 16px;
  grid-row: 16px;
  align-items: end;
  form:nth-child(1) {
    grid-area: 1 / 1 / 2 / 2;
  }
  form:nth-child(2) {
    grid-area: 1/2/2/3;
  }
  form:nth-child(3) {
    grid-area: 2/1/3/2;
  }
  form:nth-child(4) {
    grid-area: 2/2/3/3;
  }
  form:nth-child(5) {
    grid-area: 3/1/4/2;
  }
  form:nth-child(6) {
    grid-area: 3 / 2 / 4 / 3;
  }
  form:nth-child(7) {
    grid-area: 4 / 1 / 5 / 2;
  }
  form:nth-child(8) {
    grid-area: 4 / 2 / 5 / 3;
  }
  form:nth-child(9) {
    grid-area: 5 / 1 / 6 / 3;
  }
  form:nth-child(10) {
    grid-area: 6 / 1 / 7 / 3;
  }
}
.subscriberBox {
  display: flex;
  padding: 16px 24px;
  align-items: flex-start;
  gap: 10px;
  border-radius: 16px;
  background: #f7f6f6;
  width: fit-content;
  .titles {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}
.notEligible {
  border: 2px solid $urgent;
  margin-bottom: 24px;
  color: $urgent;
  .topText {
    margin-bottom: 6px;
    color: $placeholder;
  }
  .bottomText {
    color: $placeholder;
    font-size: 12px;
    line-height: 18px;
  }
  svg {
    width: 32px;
    height: 32px;
  }
}
.eligible {
  border: 2px solid $success;
  color: $success;
  margin-bottom: 12px;
  align-items: center;
}

.eligible,
.notEligible {
  padding: 12px 16px;
  border-radius: 8px;
  display: grid;
  grid-template-columns: 32px 1fr;
  grid-column-gap: 12px;
}

.useSubAddress {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
  margin-bottom: 12px;
  width: 100%;
  max-width: 389px;
  font-size: 14px;
  font-weight: 600;
}

.backButton {
  position: absolute;
  top: 35px;
  left: 15px;
  cursor: pointer;
  transition: all 0.1s ease;
  padding: 6px;
  border-radius: 6px;
  outline: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  svg {
    vertical-align: middle;
  }
  &:hover {
    background: #f6f6f6;
  }
}
