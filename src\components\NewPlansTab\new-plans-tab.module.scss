.topContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filtersAndSorts {
  display: flex;
  gap: 32px;

  .item {
    display: flex;
    align-items: center;
    gap: 12px;

    .label {
      font-size: 14px;
      color: #000;
    }
  }
}

.plansGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  align-items: start;
  gap: 22px;
  margin-top: 16px;
}
