import { useState } from "react";
import { ApiPostAuth } from "../../pages/api/api";
import Modal from "../Modal";
import styles from "./confirm-port-cancel.module.scss";

const ConfirmPortCancel = ({
  show,
  setShow,
  handleCancel,
  number,
  zipCode,
}: any) => {
  const [loading, setLoading] = useState(false);

  const closeAll = () => {
    handleCancel();
    setShow(false);
    setLoading(false);
  };

  const cancelPortIn = () => {
    setLoading(true);
    ApiPostAuth("/accounts/portin/cancel", {
      msisdn: number,
      zipCode: zipCode,
    })
      .then(closeAll)
      .catch(closeAll);
  };

  return (
    <Modal
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={cancelPortIn}
      image="/bulk_edit_confirm_graphic.svg"
      saveButton="Yes, cancel port in"
      cancelButton="No"
      clearContainer
      loading={loading}
    >
      <div className={styles.main}>
        <div className={styles.text}>
          Are you sure you want to cancel port in for number {number}?
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmPortCancel;
