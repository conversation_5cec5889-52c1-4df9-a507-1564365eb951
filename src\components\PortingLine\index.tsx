import { ApiGetSubscriber, ApiPostAuth } from "../../pages/api/api";
import Button from "../Button";
import PortingProgressPill from "../PortingProgressPill";
import styles from "./porting-line.module.scss";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch } from "react-redux";
import PortingLoading from "../PortingLoading";
import { ArrowRight, ClockTimer } from "../svgs";

type PortingLineProps = {
  port: any;
  setCurrentModal: any;
  setActivePort?: any;
  failed?: boolean;
  repopulate: any;
};

const PortingLine = ({
  port,
  setCurrentModal,
  setActivePort,
  failed,
  repopulate,
}: PortingLineProps) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { subscriberId, mvnoId } = useParams();

  const [loading, setLoading] = useState(false);

  const closeView = (port: any) => {
    setLoading(true);
    ApiPostAuth("/accounts/portin/flag", {
      msisdn: port.subscriberNumber,
      view: false,
    })
      .then((response) => {
        repopulate();
        setLoading(false);
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <div className={styles.portingProgress}>
      {port ? (
        <>
          <div className={styles.top}>
            <div className={styles.heading}>Porting In</div>
            <PortingProgressPill
              status={port.status}
              setActivePort={setActivePort}
              port={port}
              setCurrentModal={setCurrentModal}
            />
          </div>
          <div className={styles.detailsGrid}>
            <div>Phone number:</div>
            <div>{port.subscriberNumber}</div>
            <div>ZIP Code:</div>
            <div>{port.zipCode}</div>
          </div>
          {port.subscriberNumber && (
            <div className={styles.buttons}>
              <Button
                style={{
                  width: "100%",
                  fontSize: 12,
                  padding: "0 10px",
                  height: 45,
                }}
                color="secondary"
                loading={loading}
                onClick={() => {
                  navigate(
                    // todo: navigate to activity log tab
                    `/${mvnoId}/mdn-activity/${subscriberId}/${port?.subscriberNumber}`,
                  );
                }}
              >
                <ClockTimer />
                Activity Log
              </Button>
            </div>
          )}
          <div className={styles.buttons}>
            {port.status === "Completed" || port.status === "Cancelled" ? (
              <Button
                style={{
                  width: "100%",
                  fontSize: 12,
                  padding: "0 10px",
                  height: 45,
                }}
                color="secondary"
                loading={loading}
                onClick={() => {
                  closeView(port);
                }}
              >
                Close view
              </Button>
            ) : port.status === "Failed" ? (
              <>
                <Button
                  style={{
                    minWidth: "initial",
                    marginRight: 10,
                    width: "100%",
                    fontSize: 12,
                    padding: "0 10px",
                    height: 45,
                  }}
                  color="secondary"
                  onClick={() => {
                    setActivePort(port);
                    setCurrentModal("cancel-port");
                  }}
                >
                  Cancel
                </Button>
                <Button
                  style={{
                    minWidth: "initial",
                    width: "100%",
                    fontSize: 12,
                    padding: "0 10px",
                    height: 45,
                  }}
                  onClick={() => {
                    setActivePort(port);
                    setCurrentModal("update-port");
                  }}
                >
                  Re-submit
                </Button>
              </>
            ) : (
              <Button
                onClick={() => {
                  setActivePort(port);
                  setCurrentModal("cancel-port");
                }}
                style={{
                  width: "100%",
                  fontSize: 12,
                  padding: "0 10px",
                  height: 45,
                }}
                color="secondary"
              >
                Cancel
              </Button>
            )}
          </div>
        </>
      ) : (
        <PortingLoading />
      )}
    </div>
  );
};

export default PortingLine;
