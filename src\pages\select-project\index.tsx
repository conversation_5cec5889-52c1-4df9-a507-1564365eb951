import styles from "../../styles/select-project.module.scss";
import ProjectTile from "../../components/ProjectTile";
import Header from "../../components/Header";
import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { ApiGet } from "../api/api";
import { logOut } from "../../components/utils/logOut";
import { useNavigate } from "react-router-dom";
import ProjectSkeleton from "../../components/ProjectSkeleton";

const SelectProject = () => {
  const [projects, setProjects] = useState([] as any);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { resetMessage } = useSelector((state: any) => state);

  useEffect(() => {
    ApiGet("/users")
      .then((response) => {
        setProjects(response.data.mvnos);
        console.log(response);
      })
      .catch((error) => {
        logOut(dispatch, navigate);
      });

    dispatch({
      type: "set",
      subscribers: [],
      throttleNotifications: null,
    });
  }, []);

  useEffect(() => {
    if (resetMessage !== null) {
      dispatch({
        type: "notify",
        payload: {
          error: false,
          message: resetMessage,
        },
      });
      dispatch({
        type: "set",
        resetMessage: null,
      });
    }
  }, [resetMessage]);

  const hiddenMvnos = ["Escape cellular"] as any;

  return (
    <>
      <Header />
      <div className={styles.main}>
        <img src="/AireSpring-Logo.png" width="141" />
        <div className={styles.projects}>
          {projects.length === 0
            ? Array.from({ length: 20 }).map((i: any) => (
                <ProjectSkeleton key={i} />
              ))
            : projects.map((project: any) => {
                if (
                  !hiddenMvnos.some((mvno: string) =>
                    project.name.toLowerCase().includes(mvno.toLowerCase()),
                  )
                ) {
                  return (
                    <ProjectTile
                      name={project.name}
                      mvnoId={project.id}
                      logo={project.brandLogo}
                      key={project.name}
                    />
                  );
                }
              })}
        </div>
      </div>
    </>
  );
};

export default SelectProject;
