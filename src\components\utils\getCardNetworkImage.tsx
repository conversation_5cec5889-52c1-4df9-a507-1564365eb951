import { Visa, Master, Amex } from "../svgs";

export const getCardNetworkImage = (card: any) => {
  let network =
    "network" in card && card.network ? card.network.toLowerCase() : "";
  return network === "visa" ? (
    <Visa />
  ) : network === "mastercard" ? (
    <Master />
  ) : network === "amex" ? (
    <Amex />
  ) : network === "jcb" ? (
    <img src="/JCB.png" width={38} />
  ) : network === "discover" ? (
    <img src="/discover.jpg" width={38} />
  ) : network === "diners" ? (
    <img src="/diners.png" width={38} />
  ) : network === "unionpay" ? (
    <img src="/unionpay.svg" width={38} />
  ) : (
    ""
  );
};
