import { Link, useParams, useSearchParams } from "react-router-dom";
import UserMenu from "../../components/UserMenu";
import styles from "../../styles/subscription-details.module.scss";
import { useEffect, useMemo, useState } from "react";
import { ApiGet, ApiGetSubscriber } from "../api/api";
import { useDispatch } from "react-redux";
import { motion } from "framer-motion";
import { CircularProgress } from "@mui/material";
import SubscriptionDetailsTab from "../../components/SubscriptionDetailsTab";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import PaymentHistory from "../../components/PaymentHistory";
import SubscriptionConfigurations from "../../components/SubscriptionConfigurations";
import MdnActivityLogTab from "../../components/MdnActivityLogTab";
import ManageFamily from "../../components/ManageFamily";
import PlanManageProvider from "../../components/PlanManageContext";

const SubscriptionDetails = () => {
  const dispatch = useDispatch();

  const { mvnoId, subscriberId, subscriptionId } = useParams();
  const [subscriberInfo, setSubscriberInfo] = useState(null as any);

  const [initialLoading, setInitialLoading] = useState(true);
  const [loadingError, setLoadingError] = useState(false);
  const [planData, setPlanData] = useState(null as any);

  const [searchParams, setSearchParams] = useSearchParams();
  const activeOrTemp = searchParams.get("type") || "active";
  const isPorting = searchParams.get("porting") === "true";
  const planType = useMemo(() => {
    if (activeOrTemp === "active") {
      return isPorting ? "activePortingProcess" : "activeSubscription";
    }
    if (activeOrTemp === "temp") {
      return isPorting ? "tempPortingProcess" : "tempSubscription";
    }
    return "uknown";
  }, [activeOrTemp, isPorting]);

  const repopulate = () => {
    setInitialLoading(true);

    const subscriptionApiRequest = isPorting
      ? ApiGetSubscriber(
        `/accounts/by-account/${subscriberId}/portin/${subscriptionId}`,
        activeOrTemp,
      )
      : ApiGetSubscriber(
        `/accounts/by-account/${subscriberId}/sub/${subscriptionId}`,
        activeOrTemp,
      );

    ApiGet("/accounts/by-account/" + subscriberId)
      .then((response) => {
        setSubscriberInfo(response.data);
        subscriptionApiRequest
          .then((response) => {
            setInitialLoading(false);
            setPlanData({
              // subscription utils expect this field
              type: planType,
              ...response.data,
            });
          })
          .catch((error) => {
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((error) => {
        setLoadingError(true);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };


  // Get the active section from URL search param 'tab'
  const activeSection = searchParams.get("tab") || "subscription-details";

  const sections = useMemo(
    () => [
      {
        label: "Subscription Details",
        key: "subscription-details",
      },
      // {
      //   label: "Payment History",
      //   key: "payment-history",
      // },
      {
        label: "Configurations",
        key: "config",
      },
      // Hide activity log for rejected activations
      ...(planData?.subscriberNumberStatus === "Rejected"
        ?
        []
        :
        [
          {
            label: "Activity Log",
            key: "activity-log",
          }]),
      ...(planData?.familyPlanId
        ? [
          {
            label: "Manage Family",
            key: "manage-family",
          },
        ]
        : []),
    ],
    [planData],
  );

  useEffect(repopulate, []);

  return (
    <PlanManageProvider
      repopulate={repopulate}
      subscriberMid={subscriberInfo?.mid}
      plan={planData}
    >
      <div className={styles.main}>
        <div className={styles.topBar}>
          <div className={styles.breadcrumbs}>
            <Link
              style={{ textDecoration: "none" }}
              to={`/${mvnoId}/subscriber-management`}
            >
              <div className={styles.backLink}>Subscriber Management</div>
            </Link>
            &nbsp;&nbsp;/&nbsp;&nbsp;
            <Link
              style={{ textDecoration: "none" }}
              to={`/${mvnoId}/subscriber/${subscriberId}`}
            >
              <div className={styles.backLink}>
                {subscriberInfo && (
                  <>
                    {subscriberInfo.subscriberFirstName}{" "}
                    {subscriberInfo.subscriberLastName}
                  </>
                )}
              </div>
            </Link>
            <div className={styles.activeCrumb}>
              &nbsp;&nbsp;/&nbsp;&nbsp;
              {planData?.product?.product}
            </div>
          </div>
          <UserMenu />
        </div>
        {!initialLoading ? (
          <div className={styles.mainContainer}>
            <div className={styles.subscriberName}>
              {planData?.product?.product}
            </div>
            <div className={styles.selectionWrapper}>
              {sections.map((type: any) => (
                <div
                  className={`${styles.selection} ${activeSection === type.key && styles.activeSelection
                    }`}
                  style={{
                    // allow clicking on subscription details tab to refresh
                    ...(activeSection === 'subscription-details' && { cursor: 'pointer' })
                  }}
                  onClick={() => {
                    const newParams = new URLSearchParams(searchParams);
                    newParams.set("tab", type.key);
                    setSearchParams(newParams, { replace: true });

                    // refresh subscription detail if subscription details tab is clicked
                    if (type.key === 'subscription-details') {
                      repopulate();
                    }
                  }}
                >
                  <span>{type.label}</span>
                  {activeSection === type.key && (
                    <motion.div
                      className={styles.background}
                      layoutId="underline"
                    />
                  )}
                </div>
              ))}
            </div>
            <SwitchTransition>
              <CSSTransition
                key={activeSection}
                addEndListener={(node, done) =>
                  node.addEventListener("transitionend", done, false)
                }
                classNames="fade"
              >
                {(() => {
                  switch (activeSection) {
                    case "subscription-details":
                      return (
                        <SubscriptionDetailsTab
                          subInfo={subscriberInfo}
                          planData={planData}
                          repopulate={repopulate}
                          isPorting={isPorting}
                        />
                      );
                    case "payment-history":
                      return <PaymentHistory />;
                    case "config":
                      return <SubscriptionConfigurations planData={planData} />;
                    case "activity-log":
                      return (
                        <MdnActivityLogTab
                          subscriberId={subscriberId || ""}
                          mdn={
                            planData?.mdn ||
                            planData?.cancelledMdn ||
                            planData?.subscriberNumber ||
                            planData?.attDetails?.msisdn ||
                            ""
                          }
                        />
                      );
                    case "manage-family":
                      return <ManageFamily planId={planData?.familyPlanId} />;
                    default:
                      return <div></div>;
                  }
                })()}
              </CSSTransition>
            </SwitchTransition>
          </div>
        ) : (
          <div
            style={{
              padding: "200px 0px",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <CircularProgress />
          </div>
        )}
      </div>
    </PlanManageProvider>
  );
};

export default SubscriptionDetails;
