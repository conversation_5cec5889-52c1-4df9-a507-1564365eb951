import Modal from "../Modal";
import styles from "./delete-promotion-modal.module.scss";
import { Delete } from "../svgs";
import { ApiDelete } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { formatDateWithTime } from "../utils/formatDate";

const DeletePromotionModal = ({ show, setShow, promoDetails }: any) => {
  const dispatch = useDispatch();

  const navigate = useNavigate();

  const [deleting, setDeleting] = useState(false);

  const deletePromotion = () => {
    setDeleting(true);
    ApiDelete(`/promotions/${promoDetails.id}`, {})
      .then((response) => {
        setShow(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: "Promotion deleted successfully",
            message: response.data?.message,
          },
        });
        navigate(-1);
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "Something went wrong",
            message: error.response?.data?.message || "Unknown error.",
          },
        });
      })
      .finally(() => {
        setDeleting(false);
      });
  };

  return (
    <Modal
      saveButton={
        <>
          <Delete />
          Yes, Delete Promotion
        </>
      }
      image="/delete_user_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={deletePromotion}
      loading={deleting}
      close={() => {
        setShow(false);
      }}
      fullsize
      title="Delete Promotion"
    >
      <div className={`${styles.detailsContainer}`}>
        <h2>{promoDetails?.name}</h2>
        <table>
          <tbody>
            <tr>
              <td>Type</td>
              <td>{formatType(promoDetails?.type)}</td>
            </tr>
            <tr>
              <td>Amount</td>
              <td>{promoDetails?.amount * 100}%</td>
            </tr>
            <tr>
              <td>Applied to</td>
              <td>{formatFeeToDiscount(promoDetails?.feeToDiscount)}</td>
            </tr>
            <tr>
              <td>Starts on</td>
              <td>{formatDateWithTime(new Date(promoDetails?.startDate))}</td>
            </tr>
            <tr>
              <td>Expiry Date</td>
              <td>{formatDateWithTime(new Date(promoDetails?.expiryDate))}</td>
            </tr>
            <tr>
              <td>Status</td>
              <td>{promoDetails?.status ? "Active" : "Inactive"}</td>
            </tr>
            <tr>
              <td>Promo Code</td>
              <td>{promoDetails?.promoCode}</td>
            </tr>
            <tr>
              <td>Reusable</td>
              <td>
                {promoDetails?.maxCodeUses
                  ? `Max ${promoDetails.maxCodeUses} times`
                  : "No"}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </Modal>
  );
};

export default DeletePromotionModal;

function formatFeeToDiscount(value: string) {
  switch (value) {
    case "ACTIVATION":
      return "Activation Fee";

    case "REGULATORY":
      return "Regulatory Fee";

    case "TOTAL":
      return "Total";

    case "SUBTOTAL":
      return "Subtotal";

    default:
      return "";
  }
}

function formatType(value: string) {
  switch (value) {
    case "FEE_DISCOUNT":
      return "Fee Discount";

    case "ACCOUNT_DISCOUNT":
      return "Discount";
  }
}
