export const getPortingLine = () => {
  return [
    {
      subscriberNumber: "**********",
      zipCode: "90003",
      status: "Pending",
      attDetails: {
        oldService: {
          localId: "string",
          networkId: "string",
          billingAccountNumber: "**********",
          authorizationName: "<PERSON>",
          firstName: "<PERSON>",
          lastName: "Davila",
          npaNxx: "972689",
          fromLine: "2168",
          serviceArea: "**********",
        },
        accountDetails: {
          firstName: "Test",
          lastName: "Tester",
          streetNumber: "12",
          streetDirection: "N",
          streetName: "Street",
          city: "Dallas",
          state: "TX",
          zipCode: "45657",
          email: "<EMAIL>",
          contactNumber: "12435",
          mvnoId: 1,
        },
        msisdn: "********",
      },
    },
  ];
};
