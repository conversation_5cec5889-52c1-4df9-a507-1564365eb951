import styles from "../../styles/login.module.scss";
import LoginForm from "../../components/LoginForm";
import Button from "../../components/Button";
import { Link, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import FirstPasswordChange from "../../components/FirstPasswordChange";
import CheckOtp from "../../components/CheckOtp";
import { logOut } from "../../components/utils/logOut";
import { useDispatch, useSelector } from "react-redux";
import { ApiPost } from "../api/api";

const Login = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [section, setSection] = useState("login");

  const [oldPassword, setOldPassword] = useState("");

  const handleBackToLogin = () => {
    logOut();
    setSection("login");
  };

  const { resetMessage, userInfo } = useSelector((state: any) => state);

  useEffect(() => {
    console.log(resetMessage);
    if (resetMessage !== null) {
      dispatch({
        type: "notify",
        payload: {
          error: false,
          message: resetMessage,
        },
      });
      dispatch({
        type: "set",
        resetMessage: null,
      });
    }
  }, [resetMessage]);

  useEffect(() => {
    if (localStorage.getItem("token") && userInfo) {
      if (userInfo.roleName === "mvne") {
        navigate("/select-project");
      } else {
        navigate(`/${userInfo.mvnoId}/subscriber-management`);
      }
    }
  }, []);

  const [otpLoginInfo, setOtpLoginInfo] = useState({
    email: "",
    password: "",
  });

  const handleSendOtp = (loginData: any) => {
    ApiPost("/users/sendotp", loginData)
      .then((response) => {
        setSection("otp");
        setOtpLoginInfo(loginData);
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: "Something went wrong, please try again",
          },
        });
      });
  };

  return (
    <div className={styles.container}>
      <div className={styles.main}>
        <div className={styles.logos}>
          <img src="/Logo.png" className={styles.logo} />
          <img src="/AireSpring-Logo.png" width="142" />
        </div>

        <SwitchTransition>
          <CSSTransition
            key={section}
            addEndListener={(node, done) =>
              node.addEventListener("transitionend", done, false)
            }
            classNames="fade"
          >
            {section === "change-password" ? (
              <div className={styles.formContainer}>
                <FirstPasswordChange
                  oldPassword={oldPassword}
                  proceed={handleBackToLogin}
                />
              </div>
            ) : section === "otp" ? (
              <div className={styles.formContainer}>
                <CheckOtp loginInfo={otpLoginInfo} />
              </div>
            ) : (
              <>
                <div className={styles.formContainer}>
                  <LoginForm handleOtp={handleSendOtp} />
                </div>
                <div className={styles.forgotPassword}>
                  <Link
                    to="/forgot-password"
                    style={{ textDecoration: "none" }}
                  >
                    <Button color="tertiary">Forgot Password?</Button>
                  </Link>
                </div>
              </>
            )}
          </CSSTransition>
        </SwitchTransition>
      </div>
      <img src="/Login_Graphic.svg" className={styles.graphic} />
    </div>
  );
};

export default Login;
