import { useEffect, useState } from "react";
import Button from "../Button";
import { GearSix } from "../svgs";
import styles from "./subscription-config.module.scss";
import { ApiGetNoAuth } from "../../pages/api/api";
import { tetherList } from "../utils/tetherList";
import { usePlanManageContext } from "../PlanManageContext";
import Tooltip from "../Tooltip";
import formatDate from "../utils/formatDate";
import { useDispatch } from "react-redux";
import Spinner from "../Spinner";

const SubscriptionConfigurations = ({ planData }: any) => {
  const [allFeatures, setAllFeatures] = useState([] as any);
  const { setCurrentModal } = usePlanManageContext();
  const [loadingAllFeatures, setLoadingAllFeatures] = useState(true);
  const dispatch = useDispatch();

  useEffect(() => {
    getAllFeatures();
  }, []);

  const getAllFeatures = () => {
    ApiGetNoAuth("/accounts/features/all")
      .then((response) => {
        setAllFeatures(response.data);
        setLoadingAllFeatures(false);
      })
      .catch((error) => {
        setLoadingAllFeatures(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  const [featureList, setFeatureList] = useState({
    throttle: [],
    boltons: [],
    toggleFeatures: [],
  });

  useEffect(() => {
    if (planData && planData.features && allFeatures.length) {
      let throttleList = [] as any;
      let boltonsList = [] as any;
      let toggleFeaturesList = [] as any;
      planData.features.forEach((planFeature: any) => {
        if (
          allFeatures
            .filter(
              (featureItem: any) =>
                featureItem.classificationName === "throttle",
            )
            .some((throt: any) => throt.code === planFeature.code)
        ) {
          throttleList.push(planFeature);
        } else if (
          allFeatures
            .filter((item: any) => item.classificationName === "bolton")
            .some((bolt: any) => bolt.code === planFeature.code)
        ) {
          boltonsList.push(planFeature);
        } else {
          toggleFeaturesList.push(planFeature);
        }
      });
      if (tetherList.tether.includes(planData.product.soc)) {
        toggleFeaturesList.unshift({ name: "Tether" });
      }
      setFeatureList({
        throttle: throttleList,
        boltons: boltonsList,
        toggleFeatures: toggleFeaturesList,
      });
    }
  }, [planData, allFeatures]);

  if (loadingAllFeatures) {
    return (
      <div className={styles.loading}>
        <Spinner />
      </div>
    );
  }

  return (
    <div className={styles.grid}>
      <div className={styles.panel}>
        <div className={styles.top}>
          <div className={styles.title}>Features</div>
          <Button
            onClick={() => {
              setCurrentModal("manage-features");
            }}
            color="grey"
          >
            <GearSix />
            Manage
          </Button>
        </div>
        <div className={styles.featuresContainer}>
          {featureList.toggleFeatures.map((feature: any) => (
            <div className={styles.feature} key={feature.name}>
              {feature.name}
            </div>
          ))}
        </div>
      </div>
      <div className={styles.panel}>
        <div className={styles.top}>
          <div className={styles.title}>Bolt-ons</div>
          <Button
            color="grey"
            onClick={() => {
              setCurrentModal("manage-features");
            }}
          >
            <GearSix />
            Manage
          </Button>
        </div>
        <div className={styles.featuresContainer}>
          {featureList.boltons.map((feature: any) => (
            <Tooltip
              show
              text={`Effective Date: ${formatDate(feature.effectiveDate)}`}
            >
              <div
                className={`${styles.feature} ${styles.bolton}`}
                key={feature.name}
              >
                {feature.name}
              </div>
            </Tooltip>
          ))}
        </div>
      </div>
      <div className={styles.panel}>
        <div className={styles.top}>
          <div className={styles.title}>Throttle</div>
          <Button
            color="grey"
            onClick={() => {
              setCurrentModal("manage-features");
            }}
          >
            <GearSix />
            Manage
          </Button>
        </div>
        <div className={styles.featuresContainer}>
          {featureList.throttle.map((feature: any) => (
            <div className={styles.feature} key={feature.name}>
              {feature.name}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionConfigurations;
