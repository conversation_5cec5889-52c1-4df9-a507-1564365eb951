import { useEffect, useReducer, useState } from "react";
import styles from "../../styles/subscriber-management.module.scss";
import {
  ClockTimer,
  Export,
  Pause,
  Pencil,
  Play,
  Plus,
  X,
} from "../../components/svgs";
import StatusPill from "../../components/StatusPill";
import Pagination from "../../components/Pagination";
import Button from "../../components/Button";
import { useDispatch, useSelector } from "react-redux";
import Select from "../../components/SelectMenu";
import ModalGroup from "../../components/SubscriberModals";
import { Link, useNavigate, useParams } from "react-router-dom";
import Tooltip from "../../components/Tooltip";
import AddSubscriberModal from "../../components/AddSubscriberModal";
import { getFieldObject } from "../../components/subscriberFields";
import { padArrayToLength } from "../../components/utils/padArray";
import UserSkeleton from "../../components/UserSkeleton";
import { removeHtml } from "../../components/utils/removeHtml";
import { ApiGet } from "../api/api";
import ActivateSubscriberModal from "../../components/ActivateSubscriberModal";
import { v4 } from "uuid";
import SubscriberSearchSection from "../../components/SubscriberSearchSection";
import { Collapse, Fade } from "@mui/material";
import Shimmer from "../../components/Shimmer";
import MultiSelect from "../../components/MultiSelect";
import ChannelActionMenu from "../../components/ChannelActionMenu";
import EditSubscriberModal from "../../components/EditSubscriberModal";
import RemoveFromChannelModal from "../../components/RemoveFromChannelModal";
import ChangeChannelModal from "../../components/ChangeChannelModal";
import RemoveFilter from "../../components/RemoveFilter";
import { AnimatePresence } from "framer-motion";
import TableControl from "../../components/TableControl";
import qs from "qs";
import SubscriberStatus from "../../components/SubscriberStatus";
import UserMenu from "../../components/UserMenu";

const SubscriberManagement = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [initialLoading, setInitialLoading] = useState(true);

  const { mvnoId, channelId: channelIdParam } = useParams();
  const { loginMessage, userInfo } = useSelector((state: any) => state);

  const [totalActive, setTotalActive] = useState(null);
  const [totalSubscribers, setTotalSubscribers] = useState(null);
  const [totalActiveLoading, setTotalActiveLoading] = useState(false);

  const [maxSearchPage, setMaxSearchPage] = useState(1);

  const [channelName, setChannelName] = useState("");
  const [channelOptions, setChannelOptions] = useState([] as any);
  const [channelsLoading, setChannelsLoading] = useState(false);

  useEffect(() => {
    setChannelsLoading(true);
    ApiGet(`/channels/mvno/${mvnoId}`)
      .then((response) => {
        if (channelIdParam) {
          setChannelName(
            response.data.find((channel: any) => channel.id == channelIdParam)
              ?.name,
          );
        }
        setChannelOptions(
          response.data.map((channel: any) => ({
            key: channel.id,
            label: channel.name,
          })),
        );
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setChannelsLoading(false);
      });
  }, [channelIdParam]);

  const repopulate = (after = () => {}) => {
    const controller2 = new AbortController();

    getTotalActiveSubscribers(selectedChannelIds);

    if (after) {
      after();
    }

    return () => {
      controller2.abort();
    };
  };

  const [itemsPerPage, setItemsPerPage] = useState(10);

  const [currentSearchPage, setCurrentSearchPage] = useState(1);

  const [selectedChannelIds, setSelectedChannelIds] = useState([]);

  const getTotalActiveSubscribers = (selectedChannelIds: any[]) => {
    setTotalActiveLoading(true);

    let channelIdQueryString = "";

    if (selectedChannelIds.length) {
      channelIdQueryString = qs.stringify(
        {
          channelId: selectedChannelIds,
        },
        { arrayFormat: "repeat" },
      );
    }

    ApiGet(`/accounts/active-sub/${mvnoId}?${channelIdQueryString}`)
      .then((response: any) => {
        setTotalActive(response.data.activeSubscriptions);
        setTotalSubscribers(response.data.totalSubscribers);
      })
      .finally(() => {
        setTotalActiveLoading(false);
      });
  };

  useEffect(() => {
    getTotalActiveSubscribers(selectedChannelIds);
  }, [selectedChannelIds]);

  useEffect(() => {
    if (loginMessage !== null) {
      dispatch({
        type: "notify",
        payload: {
          error: false,
          message: loginMessage,
        },
      });
      dispatch({
        type: "set",
        loginMessage: null,
      });
    }
  }, [loginMessage]);

  ///////////////////////////////////////////////////////////////////////////
  /*****                Table Field Selection                        *******/

  // Fields which will always show in table
  const nonRemovableFields = getFieldObject([
    "subscriberName",
    "subscriberNumber",
    "channelName",
    "iccid",
    "imei",
    "ban",
    "address",
    "email",
    "subscriberStatus",
  ]);

  // Search query states
  const [queryDisplay, setQueryDisplay] = useState("");

  const initialModalState = {
    confirmResume: false,
    confirmRestore: false,
    confirmCancel: false,
    confirmSuspend: false,
    tbs: false,
  };

  const [currentChannelAction, setCurrentChannelAction] = useState("");

  // Handles opening and closing of subscriber status management modals
  const modalReducer = (state: any, action: any) => {
    switch (action.type) {
      case "OPEN_MODAL":
        return { ...state, [action.modal]: true };
      case "CLOSE_MODAL":
        return { ...state, [action.modal]: false };
      case "RESUME_SUBSCRIBER":
        return { ...state, confirmResume: false };
      case "CANCEL_SUBSCRIBER":
        return { ...state, confirmResume: false };
      default:
        return state;
    }
  };

  const [modalState, dispatchModal] = useReducer(
    modalReducer,
    initialModalState,
  );
  const [showActivatePlan, setShowActivatePlan] = useState(false);

  const [activeSub, setActiveSub] = useState(null as any);
  const [activeAccount, setActiveAccount] = useState(null as any);

  const [activeUser, setActiveUser] = useState(null as any);

  const [showAddSub, setShowAddSub] = useState(false);

  const [searchResults, setSearchResults] = useState(null as any);

  const [exportLoading, setExportLoading] = useState(false);

  const handleExportSubscribers = () => {
    setExportLoading(true);
    ApiGet(`/accounts/export/${mvnoId}`)
      .then((response) => {
        setExportLoading(false);
        const blob = new Blob([response.data], {
          type: "text/csv;charset=utf-8;",
        });
        const url = URL.createObjectURL(blob);

        // Create a link to download it
        var pom = document.createElement("a");
        pom.href = url;
        pom.setAttribute("download", "subscribers");
        pom.click();

        dispatch({
          type: "notify",
          payload: {
            message: ".CSV file exported.",
            error: false,
          },
        });
      })
      .catch((error: any) => {
        setExportLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  const SubscriberRow = ({ user, index }: any) => {
    return (
      <tr
        className={`${user === null && styles.hidden}`}
        onClick={() => {
          // allow user to select text without triggering navigation
          const selection = window.getSelection();
          if (selection && selection.toString().length === 0) {
            if (typeof user.mid === "number") {
              if (!user.subscriptionId) {
                navigate(`/${mvnoId}/subscriber/${user.mid}`);
                return;
              };
              navigate(`/${mvnoId}/subscription-details/${user.mid}/${user.subscriptionId}`);
            } else {
              if (!user.subscriptionId) {
                navigate(`/${mvnoId}/subscriber/${removeHtml(
                  user.mid.props.dangerouslySetInnerHTML.__html,
                )}`);
                return;
              };
              navigate(
                `/${mvnoId}/subscription-details/${removeHtml(
                  user.mid.props.dangerouslySetInnerHTML.__html,
                )}/${user.subscriptionId}`,
              );
            }
          }
        }}
      >
        {nonRemovableFields.map((field: any) => {
          if (user === null) {
            return <td key={`null-${field.key}-${index}`}></td>;
          } else if (field.key === "subscriberStatus") {
            return (
              <td key={`${field.key}-${user.mid}`}>
                <div className={styles.tdBox}>
                  {user?.subscriberNumberStatus ? (
                    <SubscriberStatus status={user.subscriberNumberStatus} />
                  ) : (
                    "-"
                  )}
                </div>
              </td>
            );
          } else {
            return (
              <td key={`${field.key}-${user.mid}`}>
                <div className={styles.tdBox}>
                  {field.key === "subscriberNumber" ? (
                    <div
                      key={`${field.key}-${user.mid}-${v4()}`}
                      className={styles.tdBox}
                    >
                      {user[field.key] || user.cancelledMdn || "-"}
                    </div>
                  ) : (
                    <div
                      key={`${field.key}-${user.mid}-${v4()}`}
                      className={styles.tdBox}
                    >
                      {user[field.key] || "-"}
                    </div>
                  )}
                </div>
              </td>
            );
          }
        })}
        <td>
          <div className={styles.tdBox}>
            <ChannelActionMenu
              setCurrentChannelAction={setCurrentChannelAction}
              subscriber={user}
              setActiveUser={setActiveUser}
            />
          </div>
        </td>
      </tr>
    );
  };

  return (
    <div className={styles.main}>
      {activeSub !== null && (
        <ModalGroup
          state={modalState}
          dispatch={dispatchModal}
          activeSub={activeSub}
          repopulate={repopulate}
        />
      )}
      <AddSubscriberModal
        show={showAddSub}
        setShow={setShowAddSub}
        repopulate={repopulate}
      />
      <ActivateSubscriberModal
        show={showActivatePlan}
        setShow={setShowActivatePlan}
        complete={() => {
          console.log("");
        }}
        repopulate={repopulate}
        addingNew
        mid={activeAccount ? activeAccount.mid : 0}
      />
      <EditSubscriberModal
        fromListingTable
        show={currentChannelAction === "edit-subscriber"}
        setShow={setCurrentChannelAction}
        subscriber={activeUser}
        repopulate={repopulate}
      />
      <RemoveFromChannelModal
        show={currentChannelAction === "remove-from-channel"}
        setShow={setCurrentChannelAction}
        subscriber={activeUser}
        repopulate={repopulate}
      />
      <ChangeChannelModal
        show={currentChannelAction === "change-channel"}
        setShow={setCurrentChannelAction}
        subscriber={activeUser}
        repopulate={repopulate}
      />
      <div
        style={{ display: "flex", justifyContent: "flex-end", width: "100%" }}
      >
        <UserMenu />
      </div>
      <div className={`${styles.headingSection} multi-select select`}>
        {channelIdParam ? (
          <div className={styles.breadcrumbs}>
            <Link
              to={`/${mvnoId}/channel-management`}
              className={styles.channelsLink}
            >
              Channel Management
            </Link>
            <div className={styles.channelName}> / {channelName}</div>
          </div>
        ) : (
          <div className={styles.leftWrapper}>
            <h2>Subscriber Listing</h2>
            <div className={styles.stats}>
              <div className={styles.item}>
                <div className={styles.label}>Total Subscribers:</div>
                {totalActiveLoading ? (
                  <div className={styles.activeCountSkel}>
                    <Shimmer />
                  </div>
                ) : (
                  <div className={styles.data}>{totalSubscribers || 0}</div>
                )}
              </div>
              <div className={styles.item}>
                <div className={styles.label}>Active Subscriptions:</div>
                {totalActiveLoading ? (
                  <div className={styles.activeCountSkel}>
                    <Shimmer />
                  </div>
                ) : (
                  <div className={styles.data}>{totalActive || 0}</div>
                )}
              </div>
            </div>
          </div>
        )}
        <div className={styles.buttons}>
          <Button
            onClick={() => {
              setShowAddSub(true);
            }}
          >
            <Plus />
            Add Subscriber
          </Button>
        </div>
      </div>
      <SubscriberSearchSection
        id="subscriber-search"
        currentPage={currentSearchPage}
        setCurrentPage={setCurrentSearchPage}
        setMaxPage={setMaxSearchPage}
        setLoading={setInitialLoading}
        setSubscribers={setSearchResults}
        selectedChannels={selectedChannelIds}
        itemsPerPage={itemsPerPage}
        channelOptions={channelOptions}
        setSelectedChannelIds={setSelectedChannelIds}
      />
      {/*<RemoveFiltersBar
        filters={quickFilters}
        setFilters={setQuickFilters}
        resetFilters={resetFilters}
          />*/}
      <div className={styles.tableTile}>
        {!channelIdParam && userInfo.roleName !== "Agent" && (
          <>
            <div className={`${styles.filters} menu-select select`}>
              <div className={styles.label}>Filters</div>
              <MultiSelect
                label="Channel"
                options={channelOptions}
                selected={selectedChannelIds}
                setSelected={(state: any) => {
                  setSelectedChannelIds(state);
                }}
              />
              <Button
                color="white"
                style={{ marginLeft: "auto" }}
                onClick={handleExportSubscribers}
                loading={exportLoading}
              >
                <Export />
                Export to CSV
              </Button>
            </div>
            <Collapse in={selectedChannelIds.length > 0}>
              <div className={styles.removeFilters}>
                <Button
                  style={{
                    padding: 0,
                    fontWeight: 500,
                    fontSize: 14,
                    marginRight: 24,
                    marginBottom: 8,
                  }}
                  color="quaternary"
                  onClick={() => {
                    setSelectedChannelIds([]);
                  }}
                >
                  Clear all
                </Button>
                <AnimatePresence>
                  {selectedChannelIds.map((channel: any) => (
                    <RemoveFilter
                      grey
                      handleRemoveFilter={() => {
                        setSelectedChannelIds((prev: any) =>
                          prev.filter(
                            (channelId: any) => channelId !== channel,
                          ),
                        );
                      }}
                      single
                    >
                      {
                        channelOptions?.find(
                          (channelOption: any) => channelOption.key === channel,
                        )?.label
                      }
                    </RemoveFilter>
                  ))}
                </AnimatePresence>
              </div>
            </Collapse>
          </>
        )}
        <div
          className={`${styles.tableContainer} table-scroll menu-select select`}
        >
          <table className={styles.subscriberTable}>
            <thead>
              <tr>
                {nonRemovableFields.map((field: any) => (
                  <th key={`heading-${field.key}`} className="filter-cell">
                    {/*"filter" in field ? (
                      field.filter === "date" ? (
                        <DatePicker
                          label={field.label}
                          masterFrom={quickFilters[field.key].start}
                          masterUntil={quickFilters[field.key].end}
                          onChange={(newFrom: Date, newUntil: Date) => {
                            setQuickFilters({
                              ...quickFilters,
                              [field.key]: {
                                start: newFrom,
                                end: newUntil,
                              },
                            });
                          }}
                        />
                      ) : (
                        <QuickFilter
                          label={field.label}
                          options={getAllOfType(subscribers, field.key).map(
                            (item: any) => ({
                              key: item,
                              label:
                                field.key === "subscriberStatus" ? (
                                  <StatusPill status={item} />
                                ) : (
                                  item
                                ),
                            })
                          )}
                          selected={quickFilters[field.key]}
                          setSelected={(state: any) => {
                            setQuickFilters({
                              ...quickFilters,
                              [field.key]: state,
                            });
                          }}
                          onChange={(option: string) => {
                            handleFilterChange(
                              field.key,
                              option,
                              quickFilters,
                              setQuickFilters
                            );
                          }}
                        />
                      )
                    ) : field.key === "dataUsed" ? (
                      <DataUnitSelector
                        unit={dataUnit}
                        setUnit={setDataUnit}
                        label={
                          <>
                            Total Data
                            <br />
                            Used ({dataUnit})
                          </>
                        }
                      />
                    ) : (
                      field.label
                    )*/}
                    {field.label}
                  </th>
                ))}
                <th />
              </tr>
            </thead>
            <tbody>
              {!initialLoading ? (
                searchResults?.length > 0 ? (
                  searchResults?.map((user: any, index: number) => (
                    <SubscriberRow
                      user={user}
                      index={index}
                      key={`table-row-${index}`}
                    />
                  ))
                ) : (
                  <tr style={{ background: "none" }}>
                    <td colSpan={100}>
                      <div className={styles.noneFound}>
                        <img src="/none_found.svg" />
                        <div className={styles.content}>
                          <h3>
                            We couldn't find anything matching
                            {queryDisplay ? <>"{queryDisplay}"</> : "."}
                          </h3>
                        </div>
                      </div>
                    </td>
                  </tr>
                )
              ) : (
                Array.from({ length: itemsPerPage }, (v, i) => i).map((i) => (
                  <UserSkeleton key={"sub-skeleton-" + i} noOfStandard={9} />
                ))
              )}
            </tbody>
          </table>
        </div>
        <div style={{ paddingLeft: 11 }}>
          <TableControl
            itemsPerPage={itemsPerPage}
            setItemsPerPage={setItemsPerPage}
            numberOfPages={maxSearchPage}
            currentPage={currentSearchPage}
            setCurrentPage={setCurrentSearchPage}
            label="results"
          />
        </div>
      </div>
    </div>
  );
};

export default SubscriberManagement;
