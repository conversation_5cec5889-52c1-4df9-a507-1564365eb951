import { carriers, productSizes, serviceTypes } from "./subscribers";

export const subscriberFields = [
  {
    key: "subscriberName",
    label: "Name",
    labelStr: "Name",
  },
  {
    key: "subscriberNumber",
    label: "Subscriber No.",
    labelStr: "Subscriber Number",
  },
  {
    key: "channelName",
    label: "Channel",
    labelStr: "Channel",
  },
  {
    key: "iccid",
    label: "ICCID",
    labelStr: "ICCID",
  },
  {
    key: "imei",
    label: "IMEI",
    labelStr: "IMEI",
  },
  {
    key: "ban",
    label: "BAN",
    labelStr: "BAN",
  },
  {
    key: "subscriberStatus",
    label: "Status",
    labelStr: "Status",
  },
  {
    key: "address",
    label: "Address",
    labelStr: "Address",
  },
  {
    key: "email",
    label: "Email",
    labelStr: "Email",
  },
];

export const getFieldObject = (keys: Array<string>) => {
  let output = [] as any;
  keys.forEach((key: string) => {
    output.push(subscriberFields.find((item: any) => item.key === key));
  });
  return output;
};
