import clsx from "clsx";
import Modal from "../Modal";
import styles from "./subscription-change-history-modal.module.scss";
import { useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import { ApiGetWithId } from "../../pages/api/api";
import qs from "qs";
import { useDispatch } from "react-redux";
import { padArrayToLength } from "../utils/padArray";
import UserSkeleton from "../UserSkeleton";
import formatDate, { formatDateWithTime } from "../utils/formatDate";
import { ArrowRight } from "../svgs";
import Pagination from "../Pagination";

type SubscriptionChangeHistoryModalProps = {
  show: boolean;
  onClose: () => void;
  subscriptionId: string;
};

const SubscriptionChangeHistoryModal = ({
  show,
  onClose,
  subscriptionId,
}: SubscriptionChangeHistoryModalProps) => {
  const { mvnoId } = useParams();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [subscriptionChangeHistory, setSubscriptionChangeHistory] = useState(
    [] as any,
  );

  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const itemsPerPage = 8;

  const fetchSubscriptionChangeHistory = () => {
    setLoading(true);

    const params = qs.stringify({
      subscriptionId: subscriptionId,
      page: page - 1,
      size: itemsPerPage,
      // sorted in descending order by default on BE
      sortBy: "createdAt",
    });

    ApiGetWithId(`/accounts/schedule-rateplan?${params}`, mvnoId)
      .then((response) => {
        setSubscriptionChangeHistory(response.data.content);
        setTotalPages(response.data.totalPages);
      })
      .catch((error) => {
        console.log(error);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response?.data.message || "Something went wrong",
          },
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    if (show) {
      fetchSubscriptionChangeHistory();
    }
  }, [show, page]);

  return (
    <Modal
      show={show}
      close={onClose}
      title="Subscription Change History"
      fullSize
    >
      <div className={clsx(styles.container)}>
        <div className={clsx(styles.tableContainer, "table-scroll")}>
          <table>
            <thead>
              <tr>
                {fields.map((field) => (
                  <th key={field.key}>{field.label}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {!loading ? (
                subscriptionChangeHistory.length > 0 ? (
                  padArrayToLength(
                    subscriptionChangeHistory,
                    itemsPerPage,
                    null,
                  ).map((item, index) =>
                    item === null ? (
                      <tr
                        key={`filler${index}`}
                        style={{ background: "none", pointerEvents: "none" }}
                      ></tr>
                    ) : (
                      <tr key={item.id}>
                        {fields.map((field: any) => {
                          if (field.key === "dateAndTime") {
                            return (
                              <td>{formatDateWithTime(item.createdAt)}</td>
                            );
                          } else if (field.key === "description") {
                            return (
                              <td>
                                {item.scheduleDate && (
                                  <p>
                                    Schedule Date:{" "}
                                    {formatDate(item.scheduleDate)}
                                  </p>
                                )}
                                <div className={styles.productChange}>
                                  {item.previousPlanName && (
                                    <>
                                      <p className={styles.oldPlan}>
                                        {item.previousPlanName}
                                      </p>{" "}
                                      <ArrowRight />
                                    </>
                                  )}
                                  {item.schedulePlanName && (
                                    <p className={styles.currentPlan}>
                                      {item.schedulePlanName}
                                    </p>
                                  )}
                                </div>
                                {item.description && (
                                  <p>Description: {item.description}</p>
                                )}
                              </td>
                            );
                          } else if (field.key === "user") {
                            return (
                              <td>
                                {item.firstName} {item.lastName}
                              </td>
                            );
                          } else if (field.key === "email") {
                            return <td>{item.email}</td>;
                          } else if (field.key === "status") {
                            return <td>{item.status}</td>;
                          }
                        })}
                      </tr>
                    ),
                  )
                ) : (
                  <tr style={{ background: "none" }}>
                    <td colSpan={100}>
                      <div className={styles.noneFound}>
                        <img src="/none_found.svg" />
                        <h3>No subscription change history</h3>
                      </div>
                    </td>
                  </tr>
                )
              ) : (
                Array.from({ length: 4 }, (v, i) => i).map((i) => (
                  <UserSkeleton key={"user-skeleton-" + i} noOfStandard={5} />
                ))
              )}
            </tbody>
          </table>
        </div>
        <div className={styles.pagination}>
          <Pagination
            currentPage={page}
            setCurrentPage={(currentPage: number) => {
              setPage(currentPage);
            }}
            numberOfPages={totalPages}
          />
        </div>
      </div>
    </Modal>
  );
};

export default SubscriptionChangeHistoryModal;

const fields = [
  {
    key: "dateAndTime",
    label: "Date and time",
  },
  {
    key: "user",
    label: "User",
  },
  {
    key: "email",
    label: "Email",
  },
  {
    key: "status",
    label: "Status",
  },
  {
    key: "description",
    label: "Description",
  },
];
