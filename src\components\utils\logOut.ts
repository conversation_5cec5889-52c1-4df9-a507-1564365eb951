import { ApiPostAuth } from "../../pages/api/api";

export const logOut = (
  dispatch: any = (x: any) => {},
  navigate: any = (x: any) => {}
) => {
  const reset = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("crmUserInfo");
    dispatch({
      type: "set",
      isLoggedIn: false,
      subscribers: [],
    });
    navigate("/login");
  };

  ApiPostAuth("/users/logout", {}).then(reset).catch(reset);
};
