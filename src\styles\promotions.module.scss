@use "./theme.scss" as *;

.main {
  width: 100%;
  background: #f1f1f1;
  padding: 50px 40px;
}

.titleBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 34px 0;

  h3 {
    margin: 0;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
  }
}

.tableContainer {
  overflow-x: auto;
  padding-bottom: 5px;
  border-radius: 24px;
  background: #fff;
  margin-top: 16px;
  padding: 24px 24px 12px 24px;

  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 6px;
    white-space: nowrap;

    tbody {
      tr {
        height: 40px;

        &:nth-child(2n + 1) {
          background: #f7f6f6;
        }

        td {
          font-size: 14px;
          font-weight: 400;
          line-height: 21px;
          padding: 0 16px;

          svg {
            vertical-align: middle;
          }
        }

        td:first-child {
          border-radius: 12px 0 0 12px;
          padding-left: 24px;
        }

        td:last-child {
          border-radius: 0 12px 12px 0;
          padding-right: 24px;
        }
      }

      &:before {
        content: "@";
        display: block;
        line-height: 0px;
        text-indent: -99999px;
      }
    }

    thead {
      tr {
        border-bottom: 1px solid #0000141f;

        th {
          border-bottom: 1px solid #0000141f;
        }

        th:first-child {
          padding-left: 24px;
        }
      }
    }

    th {
      font-size: 14px;
      font-weight: 500;
      line-height: 21px;
      text-align: start;
      border-bottom: 1px solid $disabled;
      padding: 0 16px;
      padding-bottom: 19px;
    }
  }

  .viewBtn {
    border: none;
    background: none;
    font-weight: 600;
    color: #b85e1d;
    cursor: pointer;
  }

  .editBtn {
    border: none;
    background: none;
    cursor: pointer;
  }
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 55px;

  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }

  h3 {
    width: 100%;
    text-align: center;
  }
}

.actionPanel {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: auto;
}

.pagination {
  height: 75px;
  display: flex;
  align-items: center;
  justify-content: right;
}
