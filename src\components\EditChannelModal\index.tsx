import styles from "./edit-channel.module.scss";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import { Input } from "../Input";
import { useEffect, useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch } from "react-redux";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import { ApiPut } from "../../pages/api/api";
import TextArea from "../TextArea";

const fields = ["channelName", "channelDescription"];
const rules = getRules(fields);
const messages = getMessages(fields);

const EditChannelModal = ({ show, setShow, channel, repopulate }: any) => {
  const dispatch = useDispatch();

  const [data, setData] = useState(createStateObject(fields, " "));

  // Reset modal data when closed
  const reset = () => {
    setData(createStateObject(fields, " "));
    setLoading(false);
  };

  useEffect(() => {
    if (show && channel) {
      setData({
        ...data,
        channelName: channel.name,
        channelDescription: channel.description,
      });
    }
  }, [show, channel]);

  // Handles saving changes for channel
  const saveChanges = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPut(`/channels/${channel.id}/update`, {
          name: data.channelName,
          description: data.channelDescription,
        })
          .then((response) => {
            reset();
            repopulate();
            dispatch({
              type: "notify",
              payload: {
                error: false,
                heading: "Success",
                message: response.data.message,
              },
            });
            setShow(false);
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                heading: "Something went wrong",
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const [loading, setLoading] = useState(false);

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={saveChanges}
      close={() => {
        setShow(false);
        setTimeout(() => {
          reset();
        }, 300);
      }}
      loading={loading}
      fullSize
    >
      <div className={`${styles.main} normal-select-input`}>
        <h3>Edit Channel</h3>
        {fields.map((field) => {
          if (field === "channelDescription") {
            return (
              <TextArea
                key={`${field}-input`}
                label={labels[field]}
                placeholder={placeholders[field]}
                value={data[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, data, setData);
                }}
                error={data.errors[field]}
                onKeyDown={saveChanges}
                clear={() => {
                  clearInput(field, setData);
                }}
                disabled={loading}
                white
              />
            );
          } else {
            return (
              <Input
                key={`${field}-input`}
                label={labels[field]}
                placeholder={placeholders[field]}
                value={data[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, data, setData);
                }}
                error={data.errors[field]}
                onKeyDown={saveChanges}
                clear={() => {
                  clearInput(field, setData);
                }}
                disabled={loading}
                white
              />
            );
          }
        })}
      </div>
    </Modal>
  );
};

export default EditChannelModal;
