import React, { useEffect, useState } from "react";
import {
  BrowserRouter as Router,
  Route,
  Routes,
  Navigate,
  Outlet,
  useLocation,
} from "react-router-dom";
import ScrollToTop from "./components/ScrollToTop";
import { AnimatePresence, motion } from "framer-motion";
import Layout from "./components/Layout";
import Notification from "./components/Notification";
import { useDispatch, useSelector } from "react-redux";
import jwt_decode from "jwt-decode";
import { CircularProgress } from "@mui/material";
import { useIdle } from "@uidotdev/usehooks";
import moment from "moment";

const root = getComputedStyle(document.getElementById("root")!);

const loading = (
  <div
    style={{
      display: "flex",
      width: "100%",
      height: "100vh",
      alignItems: "center",
      justifyContent: "center",
      background: "#f1f1f1",
      opacity: 0.5,
    }}
  >
    <CircularProgress
      style={{
        width: 50,
        height: 50,
        color: root.getPropertyValue("--orange"),
      }}
    />
  </div>
);

const pageVariants = {
  initial: {
    opacity: 0,
  },
  in: {
    opacity: 1,
  },
  out: {
    opacity: 0,
  },
};

const pageTransition = {
  type: "tween",
  ease: "easeInOut",
  duration: 0.3,
};

const LoginLayout = () => {
  const { pathname } = useLocation();
  const dispatch = useDispatch();
  const { notifications, closeResetMessage } = useSelector(
    (state: any) => state,
  );

  useEffect(() => {
    if (!closeResetMessage) {
      dispatch({
        type: "set",
        closeResetMessage: true,
      });
    } else {
      dispatch({
        type: "set",
        notifications: [],
      });
    }
  }, [pathname]);

  return (
    <motion.div
      key={pathname}
      initial="initial"
      animate="in"
      variants={pageVariants}
      transition={pageTransition}
    >
      <div className="notification-wrapper login-notification-wrapper">
        <AnimatePresence>
          {notifications.map((notification: any) => (
            <Notification
              id={notification.id}
              key={notification.id}
              message={notification.message}
              error={notification.error}
            />
          ))}
        </AnimatePresence>
      </div>
      <Outlet />
    </motion.div>
  );
};

const MainLayout = () => {
  const { pathname } = useLocation();
  const dispatch = useDispatch();
  const { notifications, closeLoginMessage } = useSelector(
    (state: any) => state,
  );

  useEffect(() => {
    if (!closeLoginMessage) {
      dispatch({
        type: "set",
        closeLoginMessage: true,
      });
    } else {
      dispatch({
        type: "set",
        notifications: [],
      });
    }
  }, [pathname]);

  return (
    <Layout>
      <motion.div
        key={pathname}
        initial="initial"
        animate="in"
        variants={pageVariants}
        transition={pageTransition}
      >
        <div className="notification-wrapper">
          <AnimatePresence>
            {notifications.map((notification: any) => (
              <Notification
                id={notification.id}
                key={notification.id}
                message={notification.message}
                error={notification.error}
              />
            ))}
          </AnimatePresence>
        </div>
        <Outlet />
      </motion.div>
    </Layout>
  );
};

const Protected = ({ children }: any) => {
  const idle = useIdle(1800000);

  const [activityLogger, setActivityLogger] = useState(null as any);

  useEffect(() => {
    if (!activityLogger) {
      setTimeout(() => {
        const now = moment();
        localStorage.setItem("lastActive", now.format());
      }, 10000);
      setActivityLogger(
        setInterval(() => {
          const now = moment();
          console.log(now.format());
          localStorage.setItem("lastActive", now.format());
        }, 60000),
      );
    }
  }, []);

  useEffect(() => {
    if (idle) {
      if (activityLogger) {
        clearInterval(activityLogger);
      }
      localStorage.removeItem("token");
      localStorage.removeItem("refreshToken");
      localStorage.removeItem("crmUserInfo");
      window.location.replace("/login");
    }
  }, [idle]);

  return children;
};

function MyApp() {
  // Pages
  const Login = React.lazy(() => import("./pages/login"));
  const NewLogin = React.lazy(() => import("./pages/new-login"));
  const ForgotPassword = React.lazy(() => import("./pages/forgot-password"));
  const SelectProject = React.lazy(() => import("./pages/select-project"));
  const ResetPassword = React.lazy(() => import("./pages/reset-password"));
  const SubscriberManagement = React.lazy(
    () => import("./pages/subscriber-management"),
  );
  const Subscriber = React.lazy(() => import("./pages/subscriber"));
  const UserManagement = React.lazy(() => import("./pages/user-management"));
  const ActivityLogs = React.lazy(() => import("./pages/activity-logs"));
  const ProductCatalogue = React.lazy(
    () => import("./pages/product-catalogue"),
  );
  const ProductCatalogueFeeDetails = React.lazy(
    () => import("./pages/product-catalogue-fee-details"),
  );
  const UserProfile = React.lazy(() => import("./pages/user-profile"));
  const Reports = React.lazy(() => import("./pages/reports"));
  const MvneUserProfile = React.lazy(() => import("./pages/mvne-user-profile"));
  const NotFound = React.lazy(() => import("./pages/404"));
  const Tickets = React.lazy(() => import("./pages/tickets"));
  const SingleTicket = React.lazy(() => import("./pages/single-ticket"));
  const Support = React.lazy(() => import("./pages/support"));
  const Notifications = React.lazy(() => import("./pages/notifications"));
  const Orders = React.lazy(() => import("./pages/orders"));
  const OrderDetails = React.lazy(() => import("./pages/order-details"));
  const ChannelManagement = React.lazy(
    () => import("./pages/channel-management"),
  );
  const Promotions = React.lazy(() => import("./pages/promotions"));
  const PromotionDetail = React.lazy(() => import("./pages/promotion-detail"));
  const MdnActivity = React.lazy(() => import("./pages/mdn-activity"));
  const SubscriptionDetails = React.lazy(
    () => import("./pages/subscription-details"),
  );

  return (
    <Router>
      <React.Suspense fallback={loading}>
        <Routes>
          <Route element={<LoginLayout />}>
            <Route path="*" element={<NotFound />} />
            <Route path="/" element={<Navigate to="/login" replace />} />
            <Route path="/login" element={<Login />} />
            <Route path="/new-login" element={<NewLogin />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route
              path="/select-project"
              element={
                <Protected>
                  <SelectProject />
                </Protected>
              }
            />
          </Route>
          <Route
            element={
              <Protected>
                <MainLayout />
              </Protected>
            }
          >
            <Route path="/:mvnoId/tickets" element={<Tickets />} />
            <Route
              path="/:mvnoId/tickets/:ticketId"
              element={<SingleTicket />}
            />
            <Route
              path="/:mvnoId/subscriber-management"
              element={<SubscriberManagement />}
            />
            <Route path="/:mvnoId/notifications" element={<Notifications />} />
            <Route path="/:mvnoId/subscriber/:id" element={<Subscriber />} />
            <Route
              path="/:mvnoId/subscription-details/:subscriberId/:subscriptionId"
              element={<SubscriptionDetails />}
            />
            <Route
              path="/:mvnoId/user-management"
              element={<UserManagement />}
            />
            <Route path="/:mvnoId/activity-logs" element={<ActivityLogs />} />
            <Route
              path="/:mvnoId/product-catalogue"
              element={<ProductCatalogue />}
            />
            <Route
              path="/:mvnoId/product-catalogue/fee/:id"
              element={<ProductCatalogueFeeDetails />}
            />
            <Route path="/:mvnoId/promotions" element={<Promotions />} />
            <Route
              path="/:mvnoId/promotions/:id"
              element={<PromotionDetail />}
            />

            <Route path="/:mvnoId/user-profile" element={<UserProfile />} />
            <Route path="/:mvnoId/reports" element={<Reports />} />
            <Route path="/:mvnoId/support" element={<Support />} />
            <Route path="/:mvnoId/orders" element={<Orders />} />
            <Route path="/:mvnoId/orders/:id" element={<OrderDetails />} />
            <Route
              path="/:mvnoId/channel-management"
              element={<ChannelManagement />}
            />
            <Route
              path="/:mvnoId/channel-management/:channelId"
              element={<SubscriberManagement />}
            />
            <Route
              path="/:mvnoId/mdn-activity/:id/:mdn"
              element={<MdnActivity />}
            />
          </Route>
          <Route path="/user-profile" element={<MvneUserProfile />} />
        </Routes>
      </React.Suspense>
    </Router>
  );
}

export default MyApp;
