@use "../../styles/theme.scss" as *;

.main {
  max-width: 500px;
  width: 100%;
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 16px;
}

.summaryContainer {
  background: #f7f6f6;
  border-radius: 16px;
  padding: 16px 24px;
  width: 100%;
  max-width: 360px;
  margin-bottom: 24px;
  .summarySection {
    display: grid;
    grid-template-columns: 119px 1fr;
    justify-items: start;
    text-align: start;
    margin-bottom: 13px;
    font-size: 14px;
    line-height: 21px;
    &:last-child {
      margin-bottom: 0px;
    }
  }
}

.message {
  font-size: 14px;
  line-height: 21px;
  max-width: 360px;
  .heading {
    font-weight: 600;
  }
  ol {
    margin: 0;
    padding-inline-start: 18px;
  }
}
