import styles from "./add-user.module.scss";
import Modal from "../Modal";
import { AddUser, CheckCircle, Pencil, XCircle } from "../svgs";
import { Input } from "../Input";
import { useEffect, useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch, useSelector } from "react-redux";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import Button from "../Button";
import SearchBar from "../SearchBar";
import RatePlan from "../RatePlan";
import Feature from "../Feature";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import ActivateSubscriberModal from "../ActivateSubscriberModal";
import AddFeaturesModal from "../AddFeaturesModal";
import { handleFilterChange } from "../utils/searchAndFilter";
import { Collapse } from "@mui/material";
import { states } from "../utils/usStates";
import SelectDropdown from "../SelectDropdown";
import { ApiGet, ApiPost, ApiPostAuth } from "../../pages/api/api";
import { useNavigate, useParams } from "react-router-dom";
import AddSubscriberSuccessModal from "../AddSubscriberSuccessModal";
import AddSubscriberErrorModal from "../AddSubscriberErrorModal";
import PortInForm from "../PortInForm";
import ConfirmPortCancel from "../ConfirmPortCancel";

const fields = [
  "firstName",
  "lastName",
  "streetNumber",
  "streetDirection",
  "streetName",
  "city",
  "state",
  "zipCode",
  "email",
  "contactNumber",
];

const rules = getRules(fields);
const messages = getMessages(fields);

const portInFields = ["portInNumber", "zipCode"];
const portInRules = getRules(portInFields);
const portInMessages = getMessages(portInFields);

type AddSubscriberModalProps = {
  show: boolean;
  setShow: (show: boolean) => void;
  repopulate?: (callback: () => void) => void;

  /**
   * If provided, force the email to this value and disable the email input
   */
  forcedEmail?: string;

  /**
   * If true, only show the create user form and don't show the activate subscriber form or CTA
   */
  createOnly?: boolean;

  /**
   * Callback to run after creating the user. Only runs if createOnly is true
   */
  afterCreate?: () => void;
};

const AddSubscriberModal = ({
  show,
  setShow,
  repopulate,
  createOnly,
  afterCreate,
  forcedEmail,
}: AddSubscriberModalProps) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { mvnoId } = useParams();

  const [data, setData] = useState(createStateObject(fields));

  const { userInfo } = useSelector((state: any) => state);

  const [deviceData, setDeviceData] = useState(null as any);

  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [successType, setSuccessType] = useState("activate");
  const [newMid, setNewMid] = useState(-1);

  // Reset modal data when closed
  const reset = () => {
    setData(createStateObject(fields));
    setLoading(false);
    setActiveFeature([] as any);
    setSelectedPlan(null as any);
    setSuccess(false);
    setError(false);
    setNumberVerified(false);
    setNumberIneligible(false);
    setPortInData(createStateObject(portInFields));
    setNewMid(-1);
  };

  // Complete activation
  const completeActivation = (
    prevResponse: any,
    setActivateLoading: any = null,
  ) => {
    ApiPostAuth("/accounts/attactivation", {
      accountId: prevResponse.data.accountId,
      tempSubscriptionId: prevResponse.data.tempSubscriptionId,
    })
      .then((response) => {
        repopulate?.(() => {
          setLoading(false);
          setSuccess(true);
          setSuccessType("activate");
        });
      })
      .catch((error) => {
        setLoading(false);
        setError(true);
        setErrorMessage(error.response.data.message);
        setShowPlans(true);
        if (setActivateLoading) {
          setActivateLoading(false);
        }
      });
  };

  // Activate acount with AT&T
  const activateAccount = (
    mid: any,
    plan: any,
    device: any,
    savingForLater = false,
    setActivateLoading: any = null,
  ) => {
    ApiPostAuth("/accounts/attactivation/start", {
      imei: device.imei,
      iccid: device.iccid,
      carrier: "AT&T",
      product: {
        deviceType: plan.deviceType,
        productFamily: plan.productFamily,
        size: plan.size,
        serviceType: plan.serviceType,
        offerId: plan.offerId,
        offerName: plan.offerName,
        soc: plan.soc,
      },
      mid: mid,
    })
      .then((response) => {
        if (!savingForLater) {
          completeActivation(response, setActivateLoading);
        } else {
          repopulate?.(() => {
            setLoading(false);
            handleFinish();
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
          });
        }
      })
      .catch((error) => {
        setLoading(false);
        setError(true);
        setErrorMessage(error.response.data.message);
        setShowPlans(true);
        if (setActivateLoading) {
          setActivateLoading(false);
        }
      });
  };

  // Handles creation of new user
  const createUser = (
    savingForLater = false,
    activating = false,
    plan: any,
    device: any,
    setActivateLoading: any = null,
  ) => {
    let testData = {
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      streetNumber: data.streetNumber.trim(),
      streetName: data.streetName.trim(),
      city: data.city.trim(),
      state: data.state.value,
      zipCode: data.zipCode.trim(),
      email: forcedEmail || data.email.trim(),
      contactNumber: data.contactNumber.trim(),
    } as any;

    validateAll(testData, rules, messages)
      .then((response) => {
        setLoading(true);
        if (newMid !== -1) {
          activateAccount(
            newMid,
            plan,
            device,
            savingForLater,
            setActivateLoading,
          );
        } else {
          if (selectedChannel) {
            testData.channelId = selectedChannel.value;
          }
          ApiPostAuth("/accounts/register", {
            ...testData,
            streetDirection:
              data.streetDirection === ""
                ? data.streetDirection
                : data.streetDirection.value,
            mvnoId: parseInt(mvnoId!),
          })
            .then((response) => {
              setNewMid(response.data.mid);
              if (createOnly && afterCreate) {
                setLoading(false);
                afterCreate();
                return;
              }
              if (!activating) {
                repopulate?.(() => {
                  setLoading(false);
                  setSuccess(true);
                  setSuccessType("reg");
                });
              } else {
                activateAccount(
                  response.data.mid,
                  plan,
                  device,
                  savingForLater,
                  setActivateLoading,
                );
              }
            })
            .catch((error) => {
              setLoading(false);
              setError(true);
              setErrorMessage(error.response.data.message);
              if (setActivateLoading) {
                setActivateLoading(false);
              }
            });
        }
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const [loading, setLoading] = useState(false);

  const [showPlans, setShowPlans] = useState(false);

  const addPlan = () => {
    setShowPlans(false);
  };

  const [showFeatures, setShowFeatures] = useState(false);

  const addFeatures = () => {
    setShowFeatures(false);
  };

  const [activeFeature, setActiveFeature] = useState([] as any);

  const [portingIn, setPortingIn] = useState(false);

  //////////////////////////////////////////////////////////////////
  /**********             Porting In                  *************/

  const [portInData, setPortInData] = useState(createStateObject(portInFields));

  const [numberIneligible, setNumberIneligible] = useState(false);
  const [ineligibleReason, setIneligibleReason] = useState("");

  const [numberVerified, setNumberVerified] = useState(false);

  const [savedSuccessfulCheck, setSavedSuccessfulCheck] = useState({
    msisdn: "",
    zipCode: "",
  });

  const cancelPortIn = () => {
    ApiPostAuth("/accounts/portin/cancel", {
      msisdn: savedSuccessfulCheck.msisdn,
      zipCode: savedSuccessfulCheck.zipCode,
    });
  };

  const [showPortIn, setShowPortIn] = useState(false);

  const [showConfirmCancel, setShowConfirmCancel] = useState(false);
  const [switchCancel, setSwitchCancel] = useState(false);

  const handleStateChange = (selectedOption: any) => {
    setData({
      ...data,
      state: selectedOption,
      errors: { ...data.errors, state: "" },
    });
  };

  const handleDirectionChange = (selectedOption: any) => {
    if (selectedOption.value === data.streetDirection.value) {
      setData({
        ...data,
        streetDirection: "",
        errors: { ...data.errors, streetDirection: "" },
      });
    } else {
      setData({
        ...data,
        streetDirection: selectedOption,
        errors: { ...data.errors, streetDirection: "" },
      });
    }
  };

  const handleStartActivation = () => {
    const testData = {
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      streetNumber: data.streetNumber.trim(),
      streetName: data.streetName.trim(),
      city: data.city.trim(),
      state: data.state.value,
      zipCode: data.zipCode.trim(),
      email: data.email.trim(),
      contactNumber: data.contactNumber.trim(),
    };

    validateAll(testData, rules, messages)
      .then((response) => {
        setShowPlans(true);
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const [selectedPlan, setSelectedPlan] = useState(null as any);

  const handleSelectPlan = (
    plan: any,
    imei: string,
    iccid: string,
    savingForLater = false,
    setActivateLoading = null,
  ) => {
    setSelectedPlan(plan);
    setDeviceData({
      imei: imei,
      iccid: iccid,
    });
    createUser(
      savingForLater,
      true,
      plan,
      {
        imei: imei,
        iccid: iccid,
      },
      setActivateLoading,
    );
  };

  const handleFinish = () => {
    setShow(false);
    setTimeout(() => {
      reset();
    }, 300);
  };

  const root = getComputedStyle(document.getElementById("root")!);

  const handleToSubscriberPage = () => {
    if (newMid === -1) {
      handleFinish();
    } else {
      navigate(`/${mvnoId}/subscriber/${newMid}`);
    }
  };

  const checkPortIn = () => {
    setNumberIneligible(false);
    validateAll(portInData, portInRules, portInMessages)
      .then((response) => {
        if (numberVerified) {
          cancelPortIn();
        }
        setLoading(true);
        setNumberVerified(false);
        ApiPostAuth("/accounts/portin/check", {
          msisdn: portInData.portInNumber,
          zipCode: portInData.zipCode,
        })
          .then((response) => {
            setSavedSuccessfulCheck({
              msisdn: portInData.portInNumber,
              zipCode: portInData.zipCode,
            });
            setLoading(false);
            setNumberVerified(true);
            setShowPortIn(true);
          })
          .catch((error) => {
            setLoading(false);
            setNumberIneligible(true);
            if (error?.response?.data?.attResponse?.reasonDescription) {
              setIneligibleReason(
                error?.response?.data?.attResponse?.reasonDescription,
              );
            } else if (error?.response?.data?.attResponse?.errorDescription) {
              setIneligibleReason(
                error?.response?.data?.attResponse?.errorDescription,
              );
            } else {
              setIneligibleReason(error.response.data.message);
            }
          });
      })
      .catch((errors) => {
        setNumberVerified(false);
        displayErrors(errors, setPortInData);
      });
  };

  const handlePortCancel = () => {
    setNumberVerified(false);
    setNumberIneligible(false);
    setPortInData(createStateObject(portInFields));
  };

  const [channels, setChannels] = useState([]);
  const [selectedChannel, setSelectedChannel] = useState(null as any);

  // Populate channel selection
  useEffect(() => {
    ApiGet(`/channels/mvno/${mvnoId}`)
      .then((response) => {
        setChannels(
          response.data.map((channel: any) => ({
            value: channel.id.toString(),
            label: channel.name,
          })),
        );
        if (userInfo?.roleName === "Agent") {
          let channels = response.data;
          if (response.data.length > 1) {
            channels = channels.filter(
              (channel: any) => channel.name !== "General",
            );
          }

          setSelectedChannel({
            value: channels[0].id.toString(),
            label: channels[0].name,
          });
        } else {
          let generalChannel = response.data.find(
            (channel: any) => channel.name === "General",
          );
          if (generalChannel) {
            setSelectedChannel({
              value: generalChannel.id.toString(),
              label: generalChannel.name,
            });
          } else {
            setSelectedChannel({
              value: response.data[0].id.toString(),
              label: response.data[0].name,
            });
          }
        }
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  const handleChannelChange = (selectedOption: any) => {
    setSelectedChannel(selectedOption);
  };

  return (
    <Modal
      saveButton={
        portingIn ? (
          numberVerified ? (
            "Next"
          ) : null
        ) : (
          <>
            <AddUser />
            Add subscriber
          </>
        )
      }
      cancelButton="Cancel"
      image="/add_user_graphic.svg"
      show={show}
      proceed={
        portingIn
          ? () => {
              setShowPortIn(true);
            }
          : createUser
      }
      close={() => {
        if (numberVerified) {
          setShowConfirmCancel(true);
        } else {
          setShow(false);
          setTimeout(() => {
            reset();
          }, 300);
        }
      }}
      loading={loading}
      fullSize
      title="Add subscriber"
      subtitle={
        // hide porting in switch if createOnly
        !createOnly && (
          <div className={styles.porting}>
            <div className={styles.portingLabel}>Porting in?</div>
            <div
              className={styles.portingSwitch}
              style={{
                backgroundColor: portingIn
                  ? root.getPropertyValue("--faded-orange")
                  : "#E0DCDC",
              }}
              onClick={() => {
                if (portingIn && numberVerified) {
                  setSwitchCancel(true);
                  setShowConfirmCancel(true);
                } else {
                  setPortingIn((prev: boolean) => !prev);
                }
              }}
            >
              <div
                className={styles.yesNo}
                style={{ marginRight: 45, color: portingIn ? "#fff" : "#000" }}
              >
                Yes
              </div>
              <div
                className={styles.yesNo}
                style={{ color: !portingIn ? "#fff" : "#000" }}
              >
                No
              </div>
              <div
                className={styles.thumb}
                style={{
                  right: portingIn ? "calc(100% - 77px)" : 0,
                  backgroundColor: portingIn
                    ? root.getPropertyValue("--orange")
                    : "#1a1a1a",
                }}
              />
            </div>
          </div>
        )
      }
    >
      <ActivateSubscriberModal
        show={showPlans}
        setShow={setShowPlans}
        complete={handleSelectPlan}
      />
      <AddFeaturesModal
        show={showFeatures}
        setShow={setShowFeatures}
        addFeatures={addFeatures}
      />
      <AddSubscriberSuccessModal
        show={success}
        setShow={setSuccess}
        handleFinish={handleFinish}
        type={successType}
        handleToSubscriberPage={handleToSubscriberPage}
      />
      <AddSubscriberErrorModal
        show={error}
        setShow={setError}
        handleFinish={handleFinish}
        error={errorMessage}
      />
      <PortInForm
        msisdn={portInData.portInNumber}
        zipCode={portInData.zipCode}
        show={showPortIn}
        setShow={setShowPortIn}
        repopulate={repopulate}
        updating={false}
        handleFinish={handleFinish}
        handleInitCancel={handlePortCancel}
      />
      <ConfirmPortCancel
        show={showConfirmCancel}
        setShow={setShowConfirmCancel}
        handleCancel={() => {
          if (switchCancel) {
            setPortInData(createStateObject(portInFields));
            setNumberVerified(false);
            setSwitchCancel(false);
            setPortingIn(false);
          } else {
            setShow(false);
            setTimeout(() => {
              reset();
            }, 300);
          }
        }}
        number={savedSuccessfulCheck.msisdn}
        zipCode={savedSuccessfulCheck.zipCode}
      />
      <div className={styles.main}>
        <SwitchTransition>
          <CSSTransition
            key={portingIn ? "porting-in" : "not-porting"}
            addEndListener={(node, done) =>
              node.addEventListener("transitionend", done, false)
            }
            classNames="fade"
          >
            {portingIn ? (
              <div>
                <div className={styles.inputTitle}>Eligibility Check</div>
                {portInFields.map((prop: string) => (
                  <Input
                    key={"portin-" + prop}
                    label={labels[prop]}
                    placeholder={placeholders[prop]}
                    value={portInData[prop]}
                    onChange={(e: any) =>
                      handleInputChange(prop, e, portInData, setPortInData)
                    }
                    error={portInData.errors[prop]}
                    clear={() => {
                      clearInput(prop, setPortInData);
                    }}
                    disabled={loading}
                    white
                    onKeyDown={checkPortIn}
                  />
                ))}
                <Collapse in={numberIneligible}>
                  <div className={styles.notEligible}>
                    <XCircle />
                    <div>
                      <div className={styles.topText}>
                        Number is not eligible for port in
                      </div>
                      <div className={styles.bottomText}>
                        {ineligibleReason}
                      </div>
                    </div>
                  </div>
                </Collapse>
                <Collapse style={{ width: "100%" }} in={numberVerified}>
                  <div className={styles.eligible}>
                    <CheckCircle />
                    <div>Number is eligible for port in</div>
                  </div>
                </Collapse>

                <Button
                  style={{ marginTop: 15, width: "100%" }}
                  onClick={checkPortIn}
                  loading={loading}
                >
                  Check Eligibility
                </Button>
              </div>
            ) : (
              <div>
                <div className={styles.inputTitle}>Subscriber Details</div>
                {fields.map((fieldName) => {
                  if (fieldName === "state") {
                    return (
                      <SelectDropdown
                        key={"sub-details-" + fieldName}
                        value={data.state}
                        error={data.errors.state}
                        onChange={handleStateChange}
                        placeholder="State"
                        options={states}
                        disabled={loading}
                      />
                    );
                  } else if (fieldName === "streetDirection") {
                    return (
                      <div>
                        <div style={{ fontSize: 10, color: "#525252" }}>
                          *Street direction is an optional field
                        </div>
                        <SelectDropdown
                          key={"sub-details-" + fieldName}
                          value={data.streetDirection}
                          error={data.errors.streetDirection}
                          onChange={handleDirectionChange}
                          placeholder="Street Direction (Optional)"
                          options={[
                            { label: "N", value: "N" },
                            { label: "E", value: "E" },
                            { label: "S", value: "S" },
                            { label: "W", value: "W" },
                            { label: "NE", value: "NE" },
                            { label: "NW", value: "NW" },
                            { label: "SE", value: "SE" },
                            { label: "SW", value: "SW" },
                          ]}
                          disabled={loading}
                        />
                      </div>
                    );
                  } else {
                    // disable email input if forcedEmail is provided
                    const disableEmail = fieldName === "email" && !!forcedEmail;
                    const value =
                      forcedEmail && fieldName === "email"
                        ? forcedEmail
                        : data[fieldName];

                    return (
                      <Input
                        key={"sub-details-" + fieldName}
                        label={labels[fieldName]}
                        placeholder={placeholders[fieldName]}
                        value={value}
                        onChange={(e: any) => {
                          handleInputChange(fieldName, e, data, setData, true);
                        }}
                        error={data.errors[fieldName]}
                        clear={() => {
                          clearInput(fieldName, setData);
                        }}
                        disabled={loading || disableEmail}
                        white
                      />
                    );
                  }
                })}
                <SelectDropdown
                  key="channel-select"
                  value={selectedChannel}
                  error={data.errors.channel}
                  onChange={handleChannelChange}
                  placeholder="Channel"
                  options={channels}
                  disabled={loading}
                  white
                />
                {!createOnly && (
                  <Button
                    disabled={loading}
                    onClick={handleStartActivation}
                    style={{
                      marginTop: 4,
                      width: "100%",
                      height: "auto",
                      padding: "10px 28px",
                    }}
                    color="secondary"
                  >
                    {selectedPlan ? (
                      <>
                        <Pencil />
                        {selectedPlan.offerName}
                      </>
                    ) : (
                      "Activate Subscriber"
                    )}
                  </Button>
                )}
                {/*<Button
                  disabled={loading}
                  onClick={() => {
                    setShowPlans(true);
                  }}
                  style={{ marginBottom: 16, marginTop: 4 }}
                  color="secondary"
                >
                  {activePlan >= 0 ? (
                    <div className={styles.planAdded}>
                      <span
                        style={{
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          width: 248,
                          textOverflow: "ellipsis",
                        }}
                      >
                        APEX 1GB LTE iPhone 2.32/GB Coverage no tether
                      </span>
                      <Pencil />
                    </div>
                  ) : (
                    "Select Rate Plan"
                  )}
                </Button>
                <Button
                  disabled={loading}
                  onClick={() => {
                    setShowFeatures(true);
                  }}
                  color="secondary"
                >
                  {activeFeature.length > 0 ? (
                    <div className={styles.planAdded}>
                      <span>
                        {activeFeature.length} feature
                        {activeFeature.length > 1 ? "s" : ""} selected
                      </span>
                      <Pencil />
                    </div>
                  ) : (
                    "Select Features"
                  )}
                  </Button>*/}
              </div>
            )}
          </CSSTransition>
        </SwitchTransition>
      </div>
    </Modal>
  );
};

export default AddSubscriberModal;
