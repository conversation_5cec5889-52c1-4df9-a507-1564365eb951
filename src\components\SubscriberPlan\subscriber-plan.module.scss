@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  background: #f2f2f2;
  border-radius: 24px;
  display: grid;
  grid-template-columns: 340px 1fr auto;
  margin-bottom: 24px;
  &.showMoreMain {
    .left {
      padding: 40px 24px;
    }
    .right {
      padding: 35px 24px;
    }
  }

  &.open {
    @media (max-width: 1700px) {
      grid-template-columns: 280px 1fr auto;
    }
    .planDataGrid {
      @media (max-width: 1660px) {
        grid-template-columns: 1fr 1fr;
      }
      @media (max-width: 1560px) {
        grid-template-columns: 1fr 1fr;
      }
      @media (max-width: 1420px) {
        grid-template-columns: 1fr 1fr;
      }
      @media (max-width: 1280px) {
        grid-template-columns: 1fr 1fr;
      }
    }
  }

  &.closed {
    @media (max-width: 1530px) {
      grid-template-columns: 280px 1fr auto;
    }
    .planDataGrid {
      @media (max-width: 1430px) {
        grid-template-columns: 1fr 1fr;
      }
      @media (max-width: 1320px) {
        grid-template-columns: 1fr 1fr;
      }
      @media (max-width: 1200px) {
        grid-template-columns: 1fr 1fr;
      }
      @media (max-width: 1000px) {
        grid-template-columns: 1fr 1fr;
      }
    }
  }
}

.left {
  border-right: 1px solid #b0a6a6;
  padding: 24px;
  border-radius: 24px 0 0 24px;
  transition:
    background-color 0.2s ease,
    padding 0.2s ease;
  display: flex;
  flex-direction: column;
}

.translucent {
  opacity: 0.6;
}

.planName {
  font-weight: 600;
}

.retailName {
  font-size: 14px;
  margin-top: 8px;
  padding-left: 32px;
}

.status {
  margin-top: 12px;
  font-size: 14px;
  line-height: 21px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  border-radius: 12px;
  &.doubleLabel {
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
  }
  .singleIccidLabel {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    transition: all 0.3s ease;
    border-radius: 12px;
  }
  .resumeButton {
    font-weight: 600;
    cursor: pointer;
  }
  &.active {
    opacity: 0;
  }
  &.rta {
    &:not(.doubleLabel) {
      background-color: #6361dc;
      color: #fff;
    }
  }
  &.iccid {
    &:not(.doubleLabel) {
      background-color: #eed922;
      color: $black;
    }
    .singleIccidLabel {
      background-color: #eed922;
      color: $black;
    }
  }
  &.suspended,
  &.tbs,
  &.cancelled,
  &.cancelledPast,
  &.pending,
  &.rejected,
  &.rta,
  &.iccid,
  &.portout {
    &:not(.doubleLabel) {
      padding: 12px;
      margin-top: 12px;
    }
    .singleIccidLabel {
      padding: 12px;
      margin-top: 12px;
    }
  }
  &.suspended {
    background-color: #f2a446;
  }
  &.tbs {
    background-color: #fbca90;
  }
  &.pending {
    background-color: #f8ec7e;
  }
  &.cancelled {
    background-color: #ea3d5c;
    color: #fff;
  }
  &.rejected {
    background-color: #b5b5b5;
  }
  &.cancelledPast {
    background-color: #1a1a1a;
    color: #fff;
  }
  &.portout {
    background-color: #fbca90;
  }
}

.removeSubscription {
  width: 100%;
  display: grid;
  align-content: center;
}

.restoreDays {
  margin-top: 12px;
  font-size: 14px;
  line-height: 21px;
}

.indicator {
  border-radius: 1000px;
  width: 14px;
  height: 14px;
  transition: all 0.3s ease;
}

.flexAlign {
  display: flex;
  align-items: center;
}

.badgeContainer {
  display: flex;
  flex-wrap: wrap;
}

.label {
  font-size: 14px;
  line-height: 21px;
  color: #474747;
  margin-bottom: 5px;
}

.featureType {
  margin-bottom: 16px;
  &:last-of-type {
    margin-bottom: 0px;
  }
}

.right {
  padding: 29px 24px;
  transition: padding 0.2s ease;
  position: relative;
}

.description {
  font-size: 14px;
  line-height: 21px;
  color: #474747;
  margin-bottom: 5px;
}

.descriptionData,
.mdn {
  font-size: 14px;
  line-height: 21px;
}

.mdn {
  margin-top: 12px;
  margin-bottom: 24px;
}

.lineSync {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: 4px;
  .lastSynced {
    font-weight: 400;
    font-size: 12px;
    line-height: 100%;
    line-height: 18px;
  }
  .syncButton {
    display: grid;
    align-items: center;
    justify-content: center;
    background-color: #e3e3e3;
    border: none;
    outline: none;
    border-radius: 53px;
    height: 30px;
    padding: 0px 8px;
    font-weight: 600;
    font-size: 12px;
    white-space: nowrap;
    cursor: pointer;
    &:hover {
      background-color: #d1d1d1;
    }
    &:disabled {
      cursor: auto;
      &:hover {
        background-color: #e3e3e3;
      }
    }
    .content {
      grid-area: 1 / 1 / 2 / 2;
      display: flex;
      align-items: center;
      svg {
        vertical-align: middle;
        margin-right: 8px;
        width: 16px;
        height: 16px;
      }
    }
  }
}

.planDataGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-row-gap: 40px;
  grid-column-gap: 20px;
  font-size: 14px;
  line-height: 21px;
  .planDataLabel {
    color: #474747;
    margin-bottom: 5px;
  }
}

.mdnPreview {
  position: absolute;
  font-size: 14px;
  line-height: 21px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-row-gap: 40px;
  grid-column-gap: 20px;
  width: calc(100% - 48px);
  .planDataLabel {
    color: #474747;
    margin-bottom: 5px;
  }
}

.controlPanel {
  background-color: $orange;
  border-radius: 0 24px 24px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 24px 9px;
  width: 61px;
  transition: width 0.2s ease;
  &.open {
    width: 135px;
    .collapseContainer {
      width: 117px;
    }
  }
  &.showingMoreButtons {
    .controlButton {
      .buttonLabel {
        opacity: 1;
      }
    }
  }
  .controlButton {
    background: none;
    border: none;
    height: 44px;
    width: 100%;
    border-radius: 1000px;
    display: grid;
    grid-template-columns: 24px 1fr;
    align-items: center;
    justify-content: flex-start;
    justify-items: flex-start;
    cursor: pointer;
    color: #fff;
    margin-bottom: 12px;
    padding: 0 10px;
    overflow: visible;
    transition: width 0.2s ease;
    white-space: nowrap;
    &:last-of-type {
      margin-bottom: 0px;
    }
    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
    &.showingMore {
      svg {
        transform: rotate(180deg);
      }
    }
    svg {
      vertical-align: middle;
      transition: all 0.2s ease;
      width: 24px;
    }
    .buttonLabel {
      margin-left: 8px;
      font-weight: 600;
      font-size: 14px;
      line-height: 21px;
      opacity: 0;
      transition: opacity 0.2s ease;
    }
  }
  .collapseContainer {
    width: 44px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    transition: width 0.2s ease;
  }
}

.failReason {
  padding: 12px;
  margin-top: 12px;
  font-size: 14px;
  line-height: 21px;
  color: #000;
  border-radius: 12px;
  font-weight: 400;
  background-color: #fbd6dc;
  .failTitle {
    display: flex;
    align-items: center;
    font-weight: 500;
    margin-bottom: 2px;
    color: #ea3d5c;
    svg {
      margin-right: 4px;
    }
  }
  .resubmitButtons {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 2px;
    .whiteButton {
      height: 41px;
      border-radius: 100px;
      background-color: #f8f7f7;
      border: none;
      font-size: 13px;
      font-weight: 600;
      line-height: 21px;
      padding: 0 16px;
      cursor: pointer;
      transition: background-color 0.1s ease;
      &:hover {
        background-color: #eeeeee;
      }
    }
  }
}
