@use "../../styles/theme.scss" as *;

.main {
  margin: 0 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  h3 {
    margin: 0 0 12px;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
  }
  .tag {
    font-size: 14px;
    line-height: 21px;
    margin: 0 0 40px 0;
  }
}

.newSub {
  width: 100%;
  max-width: 300px;
  margin-bottom: 32px;
  .label {
    margin-bottom: 5px;
    font-size: 14px;
    line-height: 21px;
  }
}

.buttonContent {
  display: grid;
  grid-template-columns: 1fr 24px;
  grid-column-gap: 8px;
  align-items: center;
  text-align: start;
  word-break: break-all;
  svg {
    margin: 0 !important;
  }
}

.error {
  background: #f7f6f6;
  padding: 16px 24px;
  border-radius: 16px;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .title {
    font-weight: 700;
    font-size: 18px;
    line-height: 21px;
    margin-bottom: 8px;
  }

  .message {
    font-size: 14px;
    line-height: 21px;
    color: $error;
    margin-bottom: 16px;
  }
}

.label.effectiveDate {
  margin-top: 28px;
}

.toggleControl {
  margin-bottom: 20px;
}

.upcomingChange {
  margin-inline: auto;
  width: 370px;

  .label {
    font-size: 14px;
  }

  .buttons {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
  }
}

.upcomingSubDetails {
  background: #f7f6f6;
  padding: 16px 24px;
  border-radius: 16px;
  margin-top: 8px;
  width: 370px;
  margin-inline: auto;

  table {
    font-size: 14px;
    row-gap: 10px;
  }

  td {
    vertical-align: top;
  }

  tr td:first-of-type {
    width: 160px;
  }
}
