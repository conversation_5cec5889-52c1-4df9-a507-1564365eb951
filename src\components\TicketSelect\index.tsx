import styles from "./ticket-select.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown } from "../svgs";
import { useRef } from "react";
import Radio from "../Radio";
import Tooltip from "../Tooltip";

const TicketSelect = ({
  assignees,
  label,
  options,
  selected,
  onChange,
  bulkEdit,
  disabled,
  white,
  orderby,
  small,
}: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  return (
    <Tooltip
      show={label === "Assignee" && selected}
      text={
        label === "Assignee"
          ? assignees?.find((item: any) => item?.id === selected)?.name
          : selected
      }
    >
      <div className={`${styles.box} ticket`}>
        <div
          ref={ref}
          className={`${styles.menuButton} ${white && styles.white} ${
            disabled && styles.disabled
          } ${bulkEdit && styles.bulkEdit} ${
            menuProps.state === "open" || menuProps.state === "opening"
              ? styles.iconOpen
              : styles.iconClosed
          } ${orderby && styles.orderby} ${small && styles.small}`}
          onClick={(e) => {
            e.stopPropagation();
            toggleMenu(true);
          }}
        >
          {(() => {
            if (selected) {
              return options.filter((item: any) => item.key === selected)[0]
                ?.displayLabel;
            } else {
              return label;
            }
          })()}
          {(!selected && !bulkEdit) ||
            (orderby && (
              <div className={styles.expand}>
                <ChevronDown />
              </div>
            ))}
        </div>
        <ControlledMenu
          {...menuProps}
          anchorRef={ref}
          onClose={() => toggleMenu(false)}
          align="start"
        >
          {options.map((item: any) => (
            <MenuItem
              onClick={() => {
                onChange(item.key);
              }}
              className={styles.menuItem}
              key={item.key}
            >
              <Radio
                checked={selected === item.key}
                onClick={(e: any) => {
                  e.stopPropagation();
                  onChange(item.key);
                  toggleMenu();
                }}
              />
              {item.label}
            </MenuItem>
          ))}
        </ControlledMenu>
      </div>
    </Tooltip>
  );
};

export default TicketSelect;
