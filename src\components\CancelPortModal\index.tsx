import { useState } from "react";
import Modal from "../Modal";
import styles from "./cancel-port-modal.module.scss";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";

const CancelPortModal = ({ show, setShow, repopulate, portData }: any) => {
  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();

  const cancelPort = () => {
    setLoading(true);
    ApiPostAuth("/accounts/portin/cancel", {
      msisdn: portData?.attDetails.msisdn,
      zipCode: portData?.zipCode,
    })
      .then((response) => {
        setLoading(false);
        setShow(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulate();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Modal
      show={show}
      close={() => {
        setShow("");
      }}
      saveButton="Yes, cancel port in"
      cancelButton="No"
      proceed={cancelPort}
      loading={loading}
      image="/bulk_edit_confirm_graphic.svg"
    >
      <h3 className={styles.text}>
        Are you sure you want to cancel port in for number{" "}
        {portData?.attDetails?.msisdn ||
          portData?.mdn ||
          portData?.subscriberNumber}
        ?
      </h3>
    </Modal>
  );
};

export default CancelPortModal;
