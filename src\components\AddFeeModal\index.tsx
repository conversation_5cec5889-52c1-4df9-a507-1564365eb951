import { useState } from "react";
import { Input } from "../Input";
import Modal from "../Modal";
import SelectInput from "../SelectInput";
import styles from "./add-fee-modal.module.scss";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";

const AddFeeModal = ({ show, setShow, mvnoId, refreshFees }: any) => {
  const [feeType, setFeeType] = useState<any>();
  const [feeName, setFeeName] = useState<any>();
  const [feeAmount, setFeeAmount] = useState<any>();

  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();

  const resetFields = () => {
    setFeeType(null);
    setFeeName(null);
    setFeeAmount(null);
  };

  // todo: Add validation...

  const addFee = () => {
    setLoading(true);
    ApiPostAuth("/fees/create", {
      name: feeName,
      amount: feeAmount,
      type: feeType,
      mvnoId,
    })
      .then((response) => {
        resetFields();
        refreshFees();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            heading: "Success",
            message: response.data.message,
          },
        });
        setShow(false);
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            heading: "Something went wrong",
            message: error.response.data.message,
          },
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Modal
      saveButton={<>Add fee</>}
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={addFee}
      loading={loading}
      close={() => {
        setShow(false);
      }}
      fullsize
      title="Add Fee"
    >
      <div className={`${styles.main} normal-select-input`}>
        <SelectInput
          placeholder="Fee Type"
          selected={feeType}
          options={["REGULATORY", "ACTIVATION"]}
          onChange={(value: any) => {
            setFeeType(value);
          }}
        />
        <div style={{ height: 12 }}></div>
        <Input
          label="Fee Name"
          value={feeName}
          onChange={(e: any) => setFeeName(e.target.value)}
        />
        <Input
          label="Fee Amount"
          number
          value={feeAmount}
          onChange={(e: any) => setFeeAmount(e.target.value)}
        />
      </div>
    </Modal>
  );
};

export default AddFeeModal;
