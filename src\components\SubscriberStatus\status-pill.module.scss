@use "../../styles/theme.scss" as *;

.main {
  padding: 4px 12px;
  font-size: 14px;
  border-radius: 6px;
  width: auto;
  font-weight: 500 !important;
  display: inline-block;
  &.hover:hover {
    box-shadow: 0 0 0 6px #fff;
  }
  &.inactive {
    background-color: $inactive;
    color: #000;
  }
  &.active {
    background-color: $active;
    color: #fff;
  }
  &.suspended {
    background-color: $suspended;
    color: #000;
  }
  &.tbs {
    background-color: $tbs;
    color: #000;
  }
  &.cancelled {
    background-color: $cancelled;
    color: #fff;
  }
  &.rejected {
    background-color: $rejected;
    color: $black;
  }
  &.pending {
    background-color: #f8ec7e;
  }
  &.swapping-mdn {
    background-color: #eed922;
  }
  &.portout {
    background-color: #fbca90;
  }
  &.reserved {
    background-color: #6361dc;
    color: #fff;
  }
}
