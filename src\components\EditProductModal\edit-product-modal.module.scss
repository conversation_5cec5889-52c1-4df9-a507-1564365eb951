@use "../../styles/theme.scss" as *;

.plansMain {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.plans {
  margin-right: 12px;
}

.titleSection {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 32px;
  left: 42px;
  width: calc(100% - 130px);
  h3 {
    white-space: nowrap;
    margin: 0 24px 0 0;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
  }
}

.imeiMain {
  width: 100%;
  margin: 0 auto;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 360px;
}

.inputContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 24px;
}

.summaryContainer {
  background: #f7f6f6;
  border-radius: 16px;
  padding: 16px 24px;
  width: 100%;
  margin-bottom: 24px;
  .summarySection {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 10px;
    justify-items: start;
    text-align: start;
    margin-bottom: 13px;
    font-size: 14px;
    line-height: 21px;
    &:last-child {
      margin-bottom: 0px;
    }
  }
  .editButton {
    cursor: pointer;
  }
}

.planType {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.planSwitch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px 0 35px;
  height: 50px;
  width: 326px;
  border-radius: 1000px;
  position: relative;
  user-select: none;
  overflow: hidden;
  transition: all 0.3s ease;
  .label {
    position: relative;
    z-index: 1000;
    font-weight: 600;
    transition: color 0.3s ease;
  }
  .thumb {
    position: absolute;
    height: 100%;
    border-radius: 1000px;
    width: 163px;
    transition: all 0.3s ease;
    z-index: 900;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
  }
}

.noPlans {
  width: 100%;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 150px;
  img {
    width: 60%;
    position: absolute;
    z-index: 1000;
  }
  span {
    z-index: 2000;
    font-weight: 700;
  }
}

.notEligible {
  border: 2px solid $urgent;
  margin-bottom: 24px;
  color: $urgent;
  padding: 12px 16px;
  border-radius: 8px;
  display: grid;
  grid-template-columns: 32px 1fr;
  grid-column-gap: 12px;
  text-align: start;
  .topText {
    margin-bottom: 6px;
    color: $placeholder;
  }
  .bottomText {
    color: $placeholder;
    font-size: 12px;
    line-height: 18px;
  }
  svg {
    width: 32px;
    height: 32px;
  }
}

.eligible {
  border: 2px solid $success;
  color: $success;
  margin-bottom: 12px;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  display: grid;
  grid-template-columns: 32px 1fr;
  grid-column-gap: 12px;
  width: 100%;
  justify-items: start;
  align-items: center;
}
