import styles from "./view-attachments.module.scss";
import Modal from "../Modal";
import formatDate, { formatDateWithTime } from "../utils/formatDate";
import ViewNoteModal from "../ViewNoteModal";
import { useState } from "react";
import { Delete, Mountains } from "../svgs";
import { getFileSize } from "../utils/getFileSize";
import Button from "../Button";
import DeleteFileModal from "../DeleteFileModal";
import Attachment from "../Attachment";

const ViewAttachmentsModal = ({ show, setShow, data }: any) => {
  console.log("data", data);
  const [showFiles, setShowFiles] = useState(false);
  const [file, setFile] = useState(null as any);
  return (
    <>
      <Modal
        cancelButton="Close Window"
        image="/view-user-Illustration.svg"
        show={show}
        setShow={setShow}
        close={setShow}
        onClose={() => setShow(false)}
        fullSize
        title="Attachments"
      >
        <DeleteFileModal show={showFiles} setShow={setShowFiles} item={file} />
        <div className={`${styles.main} `}>
          <div className={styles.attachmentsContainer}>
            {data?.map((item: any, index: any) => (
              <div className={styles.attachment}>
                <div className={styles.imageContainer}>
                  <Mountains />
                </div>
                <div>
                  <div className={styles.filename}>{item.name}</div>
                  <div className={styles.date}>
                    Upload date: {formatDate(item.date)}
                  </div>
                  <div className={styles.size}>
                    Size: {getFileSize(item.size)}
                  </div>
                </div>
                <Button
                  color="quaternary"
                  style={{ padding: 0, height: 24, fontSize: 14 }}
                  onClick={() => {
                    setFile(item);
                    setShowFiles(true);
                  }}
                >
                  <Delete />
                  Delete
                </Button>
              </div>
            ))}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ViewAttachmentsModal;
