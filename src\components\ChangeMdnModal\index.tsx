import { useState } from "react";
import { Input } from "../Input";
import Modal from "../Modal";
import { ArrowsClockwise, FloppyDisk } from "../svgs";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import styles from "./change-mdn-modal.module.scss";
import { validateAll } from "indicative/validator";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import Button from "../Button";

const ChangeMdnModal = ({ show, setShow, plan, repopulate }: any) => {
  const dispatch = useDispatch();

  const handleChange = () => {
    setLoading(true);
    ApiPostAuth(`/accounts/update/${plan.mdn}`, {})
      .then((response) => {
        setShow(false);
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulate();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  const [loading, setLoading] = useState(false);

  return (
    <Modal
      saveButton={null}
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      proceed={handleChange}
      close={() => {
        setShow("");
      }}
      loading={loading}
    >
      <div className={styles.main}>
        <h4>Change MDN for {plan ? plan.mdn : ""}</h4>
        <Button loading={loading} onClick={handleChange}>
          <ArrowsClockwise /> Request MDN Change
        </Button>
      </div>
    </Modal>
  );
};

export default ChangeMdnModal;
