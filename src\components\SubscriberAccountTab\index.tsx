import { useState } from "react";
import EditSubscriberModal from "../EditSubscriberModal";
import { Door, PencilWriting } from "../svgs";
import styles from "./subscriber-account-tab.module.scss";
import { useDispatch, useSelector } from "react-redux";
import { webAppUrls } from "../utils/webAppUrls";
import { useParams } from "react-router-dom";
import { ApiPostAuth } from "../../pages/api/api";

type SubscriberAccountTabProps = {
  subscriberInfo: any;
  repopulate: () => void;
};

const SubscriberAccountTab = ({
  subscriberInfo,
  repopulate,
}: SubscriberAccountTabProps) => {
  const dispatch = useDispatch();

  const [showEditSubscriber, setShowEditSubscriber] = useState(false);

  const userInfo = useSelector((state: any) => state.userInfo);

  const { mvnoId, id } = useParams();

  const handleLogIntoAccount = () => {
    const url = webAppUrls.find((mvno: any) => mvno.id == mvnoId)?.url;
    if (!url) {
      dispatch({
        type: "notify",
        payload: {
          error: true,
          message: "This MVNO does not have a commercial website to log into",
        },
      });
      return;
    }
    ApiPostAuth("/app/users/subscriber-web-view", {
      mid: id,
    })
      .then((response) => {
        window.open(
          `${url}/crm-login?token=${response.data.token}&mid=${id}&mvnoId=${mvnoId}`,
          "_blank",
        );
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error?.response?.data?.error,
          },
        });
      });
  };

  return (
    <>
      <div className={`${styles.subscriberTile} modal-scroll`}>
        <div className={styles.accountDetails}>
          <div className={styles.accountDetailsSection}>
            <div>
              <div className={styles.title}>Account Details</div>
              <div className={styles.accountDetailsGrid}>
                <div>
                  <div className={styles.itemLabel}>Name</div>
                  <div className={styles.itemDetail}>
                    {subscriberInfo?.subscriberFirstName}{" "}
                    {subscriberInfo?.subscriberLastName}
                  </div>
                </div>
                <div>
                  <div className={styles.itemLabel}>Email Address</div>
                  <div className={styles.itemDetail}>
                    {subscriberInfo?.email}
                  </div>
                </div>
                <div>
                  <div className={styles.itemLabel}>Phone Number</div>
                  <div className={styles.itemDetail}>
                    {subscriberInfo?.contactNumber || "-"}
                  </div>
                </div>
                <div>
                  <div className={styles.itemLabel}>Address</div>
                  <div className={styles.itemDetail}>
                    {subscriberInfo?.address?.streetNumber}{" "}
                    {subscriberInfo?.address?.streetDirection}{" "}
                    {subscriberInfo?.address?.streetName},{" "}
                    {subscriberInfo?.address?.city},{" "}
                    {subscriberInfo?.address?.state},{" "}
                    {subscriberInfo?.address?.zipCode}
                  </div>
                </div>
                <div>
                  <div className={styles.itemLabel}>Channel</div>
                  <div className={styles.itemDetail}>
                    {subscriberInfo?.channelName || "-"}
                  </div>
                </div>
              </div>
            </div>
            <div className={styles.buttons}>
              <button
                className={styles.accountButton}
                onClick={() => {
                  setShowEditSubscriber(true);
                }}
              >
                <PencilWriting />
                Edit Account Details
              </button>
              {userInfo?.roleName == "mvne" && (
                <button
                  className={styles.accountButton}
                  onClick={handleLogIntoAccount}
                  style={{ marginTop: 8 }}
                >
                  <Door />
                  Log into Account
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <EditSubscriberModal
        show={showEditSubscriber}
        setShow={setShowEditSubscriber}
        subscriber={subscriberInfo}
        repopulate={repopulate}
      />
    </>
  );
};

export default SubscriberAccountTab;
