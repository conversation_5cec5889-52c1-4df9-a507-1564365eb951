import { SwitchTransition, CSSTransition } from "react-transition-group";
import { Input } from "../Input";
import {
  clearInput,
  createStateObject,
  getMessages,
  getRules,
  labels,
  placeholders,
  handleInputChange,
  displayErrors,
} from "../utils/InputHandlers";
import styles from "./port-in-form.module.scss";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import SelectDropdown from "../SelectDropdown";
import { states } from "../utils/usStates";
import Modal from "../Modal";
import { ArrowBack, CheckCircle, XCircle } from "../svgs";
import { validateAll } from "indicative/validator";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import PortInErrorModal from "../PortInErrorModal";
import ConfirmPortCancel from "../ConfirmPortCancel";
import PortInSuccessModal from "../PortInSuccessModal";
import ActivateSubscriberModal from "../ActivateSubscriberModal";
import Button from "../Button";
import { Collapse } from "@mui/material";
import $ from "jquery";
import Toggle from "../Toggle";

const oldNumberFields = [
  "firstName",
  "lastName",
  "billingAccountNumber",
  "billingAccountPassword",
];
const oldNumberRules = getRules(oldNumberFields);
const oldNumberMessages = getMessages(oldNumberFields);

const oldAddressFields = [
  "streetNumber",
  "streetDirection",
  "streetName",
  "city",
  "state",
  "zipCode",
];
const oldAddressRules = getRules(oldAddressFields);
const oldAddressMessages = getMessages(oldAddressFields);

const addressFields = [
  "streetNumber",
  "streetDirection",
  "streetName",
  "city",
  "state",
  "zipCode",
];
const addressRules = getRules(addressFields);
const addressMessages = getMessages(addressFields);

const fields = ["firstName", "lastName", "email", "contactNumber"];
const portInFields = ["portInNumber", "zipCode"];
const portInRules = getRules(portInFields);
const portInMessages = getMessages(portInFields);
const rules = getRules(fields);
const messages = getMessages(fields);

const PortInForm = ({
  show,
  setShow,
  msisdn,
  zipCode,
  updating,
  temp,
  data = null,
  repopulate,
  handleFinish,
  checkEligibility,
  accountDataProvided,
  portData = null,
  handleInitCancel = null,
}: any) => {
  const dispatch = useDispatch();

  const [oldNumberData, setOldNumberData] = useState(
    createStateObject(oldNumberFields),
  );

  const [accountData, setAccountData] = useState(createStateObject(fields));
  const [addressData, setAddressData] = useState(
    createStateObject(addressFields),
  );
  const [portInData, setPortInData] = useState(createStateObject(portInFields));
  const [oldAddressData, setOldAddressData] = useState(
    createStateObject(oldAddressFields),
  );
  const [ineligibleReason, setIneligibleReason] = useState("");

  const [showPlans, setShowPlans] = useState(false);

  const addPlan = () => {
    setShowPlans(false);
  };

  const handleSelectPlan = (
    plan: any,
    imei: string,
    iccid: string,
    savingForLater = false,
    setActivateLoading: any = null,
  ) => {
    setLoading(true);

    const { teaserType, ...planData } = plan;

    const portPayload = {
      oldService: {
        billingAccountNumber: oldNumberData.billingAccountNumber,
        billingAccountPassword: oldNumberData.billingAccountPassword,
        firstName: oldNumberData.firstName,
        lastName: oldNumberData.lastName,
        addressDetails: {
          streetNumber: oldAddressData.streetNumber,
          streetDirection:
            oldAddressData.streetDirection === ""
              ? oldAddressData.streetDirection
              : oldAddressData.streetDirection.value,
          streetName: oldAddressData.streetName,
          city: oldAddressData.city,
          state: oldAddressData.state.value,
          zipCode: oldAddressData.zipCode,
        },
      },
      accountDetails: accountDataProvided
        ? {
            firstName: data?.subscriberFirstName,
            lastName: data?.subscriberLastName,
            streetNumber: data?.address.streetNumber,
            streetDirection: data?.address.streetDirection,
            streetName: data?.address.streetName,
            city: data?.address.city,
            state: data?.address.state,
            zipCode: data?.address.zipCode,
            email: data?.email,
            contactNumber: data?.contactNumber,
            mvnoId: parseInt(mvnoId!),
          }
        : {
            firstName: accountData.firstName,
            lastName: accountData.lastName,
            streetNumber: addressData.streetNumber,
            streetDirection:
              addressData.streetDirection === ""
                ? addressData.streetDirection
                : addressData.streetDirection.value,
            streetName: addressData.streetName,
            city: addressData.city,
            state: addressData.state.value,
            zipCode: addressData.zipCode,
            email: accountData.email,
            contactNumber: accountData.contactNumber,
            mvnoId: parseInt(mvnoId!),
          },
      activationDetails: {
        imei: imei,
        iccid: iccid,
        carrier: "AT&T",
        product: planData,
      },
      msisdn: checkEligibility ? portInData.portInNumber : msisdn,
    };

    ApiPostAuth("/accounts/portin/make/start", portPayload)
      .then((response) => {
        if (savingForLater) {
          repopulate();
          dispatch({
            type: "notify",
            payload: {
              error: false,
              message: response.data.message,
            },
          });
          setLoading(false);
          handleCancel();
          handleFinish();
        } else {
          completePortIn(response, setActivateLoading);
        }
      })
      .catch((error) => {
        setLoading(false);
        setShowError(true);
        setErrorMessage(error.response.data.message);
        if (setActivateLoading) {
          setActivateLoading(false);
        }
      });
  };

  const resubmitPortin = () => {
    setLoading(true);
    ApiPostAuth("/accounts/portin/resubmit", {
      msisdn: msisdn,
      billingAccountNumber: oldNumberData.billingAccountNumber,
      billingAccountPassword: oldNumberData.billingAccountPassword,
      iccid: portData?.iccid,
      addressDetails: {
        streetNumber: oldAddressData.streetNumber,
        streetDirection:
          oldAddressData.streetDirection === ""
            ? oldAddressData.streetDirection
            : oldAddressData.streetDirection.value,
        streetName: oldAddressData.streetName,
        city: oldAddressData.city,
        state: oldAddressData.state.value,
        zipCode: oldAddressData.zipCode,
      },
    })
      .then((response) => {
        repopulate(() => {
          setLoading(false);
          setShowSuccess(true);
        });
      })
      .catch((error) => {
        setLoading(false);
        setShowError(true);
        setErrorMessage(
          error.response.data.attResponse?.errorDescription ||
            error.response.data.message,
        );
      });
  };

  const resubmitTempPortin = () => {
    setLoading(true);
    ApiPostAuth("/accounts/portin/temp/update", {
      msisdn: msisdn,
      billingAccountNumber: oldNumberData.billingAccountNumber,
      billingAccountPassword: oldNumberData.billingAccountPassword,
      iccid: portData?.iccid,
      addressDetails: {
        streetNumber: oldAddressData.streetNumber,
        streetDirection:
          oldAddressData.streetDirection === ""
            ? oldAddressData.streetDirection
            : oldAddressData.streetDirection.value,
        streetName: oldAddressData.streetName,
        city: oldAddressData.city,
        state: oldAddressData.state.value,
        zipCode: oldAddressData.zipCode,
      },
    })
      .then((response) => {
        repopulate(() => {
          setLoading(false);
          setShowSuccess(true);
        });
      })
      .catch((error) => {
        setLoading(false);
        setShowError(true);
        setErrorMessage(
          error.response.data.attResponse?.errorDescription ||
            error.response.data.message,
        );
      });
  };

  const completePortIn = (response: any, setActivateLoading: any = null) => {
    ApiPostAuth("/accounts/portin/make", {
      accountId: response.data.accountId,
      tempPortInId: response.data.tempPortInId,
      oldService: {
        billingAccountNumber: oldNumberData.billingAccountNumber,
        billingAccountPassword: oldNumberData.billingAccountPassword,
        firstName: oldNumberData.firstName,
        lastName: oldNumberData.lastName,
      },
      msisdn: checkEligibility ? portInData.portInNumber : msisdn,
    })
      .then((response) => {
        if (response.data.mid) setNewMid(response.data.mid);
        repopulate(() => {
          setLoading(false);
          setShowSuccess(true);
        });
      })
      .catch((error) => {
        setLoading(false);
        setShowError(true);
        setErrorMessage(error.response.data.attResponse?.errorDescription);
        if (setActivateLoading) {
          setActivateLoading(false);
        }
      });
  };

  useEffect(() => {
    if (portData !== null && show) {
      setOldNumberData({ ...oldNumberData, ...portData.attDetails.oldService });
      setAccountData({
        ...accountData,
        ...portData.attDetails.accountDetails,
        streetDirection: {
          label: portData.attDetails.accountDetails.streetDirection,
          value: portData.attDetails.accountDetails.streetDirection,
        },
        state: {
          label: states.find(
            (state: any) =>
              state.value === portData.attDetails.accountDetails.state,
          )?.label,
          value: portData.attDetails.accountDetails.state,
        },
      });
    }
    if (portData !== null && show && updating) {
      setOldAddressData({
        ...portData?.attDetails?.oldService?.addressDetails,
        streetDirection: portData?.attDetails?.oldService?.addressDetails
          ?.streetDirection
          ? {
              label:
                portData?.attDetails?.oldService?.addressDetails
                  ?.streetDirection,
              value:
                portData?.attDetails?.oldService?.addressDetails
                  ?.streetDirection,
            }
          : "",
        state: portData?.attDetails?.oldService?.addressDetails?.state
          ? {
              label: states.find(
                (state: any) =>
                  state.value ===
                  portData?.attDetails?.oldService?.addressDetails?.state,
              )?.label,
              value: portData?.attDetails?.oldService?.addressDetails?.state,
            }
          : "",
        errors: oldAddressData.errors,
      });
    }
  }, [show, portData]);

  const [loading, setLoading] = useState(false);

  const [activeSection, setActiveSection] = useState(
    checkEligibility ? "check-eligibility" : "enter-details",
  );

  useEffect(() => {
    console.log(activeSection);
  }, [activeSection]);

  useEffect(() => {
    const handleUnload = (e: any) => {
      if (show && activeSection === "enter-details") {
        e.preventDefault();
        return "";
      }
    };

    $(window).on("beforeunload", handleUnload);
    return () => {
      $(window).off("beforeunload", handleUnload);
    };
  }, [show, activeSection]);

  const { mvnoId } = useParams();

  const [showError, setShowError] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const [showCancel, setShowCancel] = useState(false);
  const [showActivate, setShowActivate] = useState(false);
  const [numberIneligible, setNumberIneligible] = useState(false);
  const [numberVerified, setNumberVerified] = useState(false);
  const [showPortIn, setShowPortIn] = useState(false);
  const [newMid, setNewMid] = useState("");

  const [savedSuccessfulCheck, setSavedSuccessfulCheck] = useState({
    msisdn: "",
    zipCode: "",
  });

  const cancelPortIn = () => {
    ApiPostAuth("/accounts/portin/cancel", {
      msisdn: savedSuccessfulCheck.msisdn,
      zipCode: savedSuccessfulCheck.zipCode,
    });
  };

  const handleCancel = () => {
    setShow(false);
    if (handleInitCancel) {
      handleInitCancel();
    }
    setTimeout(() => {
      reset();
    }, 300);
  };

  const handleStateChange = (selectedOption: any) => {
    setAddressData({
      ...addressData,
      state: selectedOption,
      errors: { ...addressData.errors, state: "" },
    });
  };

  const handleOldStateChange = (selectedOption: any) => {
    setOldAddressData({
      ...oldAddressData,
      state: selectedOption,
      errors: { ...oldAddressData.errors, state: "" },
    });
  };

  const handleDirectionChange = (selectedOption: any) => {
    if (selectedOption.value === addressData.streetDirection.value) {
      setAddressData({
        ...addressData,
        streetDirection: "",
        errors: { ...addressData.errors, streetDirection: "" },
      });
    } else {
      setAddressData({
        ...addressData,
        streetDirection: selectedOption,
        errors: { ...addressData.errors, streetDirection: "" },
      });
    }
  };

  const handleOldDirectionChange = (selectedOption: any) => {
    if (selectedOption.value === oldAddressData.streetDirection.value) {
      setOldAddressData({
        ...oldAddressData,
        streetDirection: "",
        errors: { ...oldAddressData.errors, streetDirection: "" },
      });
    } else {
      setOldAddressData({
        ...oldAddressData,
        streetDirection: selectedOption,
        errors: { ...oldAddressData.errors, streetDirection: "" },
      });
    }
  };

  const submit = async () => {
    const oldNumber = {
      billingAccountNumber: oldNumberData.billingAccountNumber,
      billingAccountPassword: oldNumberData.billingAccountPassword,
      firstName: oldNumberData.firstName,
      lastName: oldNumberData.lastName,
    };

    let errorsField = false;

    let oldNumberValidationDone = false;
    let oldAddressValidationDone = false;

    function checkValidationDone() {
      if (oldNumberValidationDone && oldAddressValidationDone) {
        if (!errorsField) {
          if (updating) {
            if (temp) {
              resubmitTempPortin();
            } else {
              resubmitPortin();
            }
          } else {
            setShowPlans(true);
          }
        }
      }
    }

    validateAll(oldNumber, oldNumberRules, oldNumberMessages)
      .then(() => {
        oldNumberValidationDone = true;
        checkValidationDone();
      })
      .catch((errors) => {
        errorsField = true;
        displayErrors(errors, setOldNumberData);
        oldNumberValidationDone = true;
        checkValidationDone();
      });

    validateAll(oldAddressData, oldAddressRules, oldAddressMessages)
      .then(() => {
        oldAddressValidationDone = true;
        checkValidationDone();
      })
      .catch((errors) => {
        errorsField = true;
        displayErrors(errors, setOldAddressData);
        oldAddressValidationDone = true;
        checkValidationDone();
      });
  };

  const reset = () => {
    setActiveSection(checkEligibility ? "check-eligibility" : "enter-details");
    setNumberIneligible(false);
    setNumberVerified(false);
    setOldNumberData(createStateObject(oldNumberFields));
    setOldAddressData(createStateObject(oldAddressFields));
    setAccountData(createStateObject(fields));
    setPortInData(createStateObject(portInFields));
    setShowError(false);
    setShowCancel(false);
    setShowPlans(false);
    setShowSuccess(false);
  };

  const checkPortIn = () => {
    setNumberIneligible(false);
    validateAll(portInData, portInRules, portInMessages)
      .then((response) => {
        if (numberVerified) {
          cancelPortIn();
        }
        setLoading(true);
        setNumberVerified(false);
        ApiPostAuth("/accounts/portin/check", {
          msisdn: portInData.portInNumber,
          zipCode: portInData.zipCode,
        })
          .then((response) => {
            setSavedSuccessfulCheck({
              msisdn: portInData.portInNumber,
              zipCode: portInData.zipCode,
            });
            setLoading(false);
            setNumberVerified(true);
          })
          .catch((error) => {
            setLoading(false);
            setNumberIneligible(true);
            if (error?.response?.data?.attResponse?.reasonDescription) {
              setIneligibleReason(
                error?.response?.data?.attResponse?.reasonDescription,
              );
            } else if (error?.response?.data?.attResponse?.errorDescription) {
              setIneligibleReason(
                error?.response?.data?.attResponse?.errorDescription,
              );
            } else {
              setIneligibleReason(error.response.data.message);
            }
          });
      })
      .catch((errors) => {
        setNumberVerified(false);
        displayErrors(errors, setPortInData);
      });
  };

  const handleCheckDone = () => {
    setActiveSection("enter-old-carrier-details");
  };

  const checkDetails = () => {
    const testData = {
      firstName: accountData.firstName.trim(),
      lastName: accountData.lastName.trim(),
      email: accountData.email.trim(),
      contactNumber: accountData.contactNumber.trim(),
    };

    validateAll(testData, rules, messages)
      .then(() => {
        setActiveSection("enter-address");
      })
      .catch((errors) => {
        displayErrors(errors, setAccountData);
      });
  };

  const checkAddress = () => {
    validateAll(addressData, addressRules, addressMessages)
      .then(() => {
        setActiveSection("enter-old-carrier-details");
      })
      .catch((errors) => {
        displayErrors(errors, setAddressData);
      });
  };

  const checkOldDetails = () => {
    validateAll(oldNumberData, oldNumberRules, oldNumberMessages)
      .then(() => {
        setActiveSection("enter-old-carrier-address");
      })
      .catch((errors) => {
        displayErrors(errors, setOldNumberData);
      });
  };

  const checkOldAddress = () => {
    validateAll(oldAddressData, oldAddressRules, oldAddressMessages)
      .then(() => {
        setShowPlans(true);
      })
      .catch((errors) => {
        displayErrors(errors, setOldAddressData);
      });
  };

  const [useSubAddress, setUseSubAddress] = useState(false);

  const handleUseSubAddress = () => {
    if (!useSubAddress) {
      if (accountDataProvided) {
        setOldAddressData({
          ...oldAddressData,
          ...data.address,
          streetDirection: {
            label: data?.address?.streetDirection,
            value: data?.address?.streetDirection,
          },
          state: {
            label: states.find(
              (state: any) => state.value === data?.address?.state,
            )?.label,
            value: data?.address?.state,
          },
        });
      } else {
        setOldAddressData({ ...addressData });
      }
      setUseSubAddress(true);
    } else {
      setOldAddressData(createStateObject(oldAddressFields));
      setUseSubAddress(false);
    }
  };

  const handleBack = () => {
    if (activeSection === "enter-address") {
      setActiveSection("enter-details");
    } else if (activeSection === "enter-old-carrier-details") {
      if (!accountDataProvided) setActiveSection("enter-address");
    } else if (activeSection === "enter-old-carrier-address") {
      setActiveSection("enter-old-carrier-details");
    }
  };

  return (
    <Modal
      saveButton={
        activeSection === "check-eligibility" && !numberVerified
          ? null
          : "Continue"
      }
      cancelButton="Cancel"
      image="/add_user_graphic.svg"
      show={show}
      proceed={
        activeSection === "check-eligibility"
          ? handleCheckDone
          : activeSection === "enter-details"
            ? checkDetails
            : activeSection === "enter-address"
              ? checkAddress
              : activeSection === "enter-old-carrier-details"
                ? checkOldDetails
                : activeSection === "enter-old-carrier-address"
                  ? checkOldAddress
                  : null
      }
      close={() => {
        if (numberVerified || !checkEligibility) {
          if (updating) {
            setShow(false);
            setTimeout(() => {
              reset();
            }, 300);
          } else {
            setShowCancel(true);
          }
        } else {
          handleCancel();
        }
      }}
      loading={loading}
      fullSize
      clearContainer={!checkEligibility && !updating}
      onCancel={() => {
        if (numberVerified || !checkEligibility) {
          if (updating) {
            setShow(false);
            setTimeout(() => {
              reset();
            }, 300);
          } else {
            setShowCancel(true);
          }
        } else {
          handleCancel();
        }
      }}
      noCloseOnCancel={!updating}
      title={activeSection === "check-eligibility" ? "Port In" : ""}
    >
      <ActivateSubscriberModal
        key={show ? "show" : "hide"}
        show={showPlans}
        setShow={setShowPlans}
        porting
        portingUpdate={updating}
        portingCancel={() => {
          if (updating) {
            setTimeout(() => {
              reset();
            }, 300);
          } else {
            setShowCancel(true);
          }
        }}
        complete={handleSelectPlan}
        initialData={portData}
      />
      <PortInErrorModal
        show={showError}
        setShow={setShowError}
        setShowPlans={setShowPlans}
        error={errorMessage}
        msisdn={checkEligibility ? portInData.portInNumber : msisdn}
        zipCode={checkEligibility ? portInData.zipCode : zipCode}
        handleFinish={() => {
          if (updating) {
            setTimeout(() => {
              reset();
            }, 300);
          } else {
            setShowCancel(true);
          }
        }}
      />
      <PortInSuccessModal
        show={showSuccess}
        setShow={setShowSuccess}
        handleFinish={handleFinish}
        update={updating}
        exisitingSubscriber={accountDataProvided}
        handleCancel={handleCancel}
        newMid={newMid}
      />
      <ConfirmPortCancel
        show={showCancel}
        setShow={setShowCancel}
        handleCancel={handleCancel}
        number={checkEligibility ? savedSuccessfulCheck.msisdn : msisdn}
        zipCode={checkEligibility ? savedSuccessfulCheck.zipCode : zipCode}
      />
      <div className={styles.main} style={!showPortIn ? { width: "auto" } : {}}>
        {((accountDataProvided &&
          activeSection !== "enter-old-carrier-details") ||
          (!accountDataProvided && activeSection !== "enter-details")) &&
          activeSection !== "check-eligibility" && (
            <button className={styles.backButton} onClick={handleBack}>
              <ArrowBack />
            </button>
          )}
        <SwitchTransition>
          <CSSTransition
            key={activeSection}
            addEndListener={(node, done) =>
              node.addEventListener("transitionend", done, false)
            }
            classNames="fade"
          >
            {activeSection === "check-eligibility" ? (
              <div style={{ width: "350px" }}>
                <div className={styles.inputTitle}>Eligibility Check</div>
                {portInFields.map((prop: string) => (
                  <Input
                    key={"portin-" + prop}
                    label={labels[prop]}
                    placeholder={placeholders[prop]}
                    value={portInData[prop]}
                    onChange={(e: any) =>
                      handleInputChange(prop, e, portInData, setPortInData)
                    }
                    error={portInData.errors[prop]}
                    clear={() => {
                      clearInput(prop, setPortInData);
                    }}
                    disabled={loading}
                    white
                    onKeyDown={checkPortIn}
                  />
                ))}
                <Collapse in={numberIneligible}>
                  <div className={styles.notEligible}>
                    <XCircle />
                    <div>
                      <div className={styles.topText}>
                        Number is not eligible for port in
                      </div>
                      <div className={styles.bottomText}>
                        {ineligibleReason}
                      </div>
                    </div>
                  </div>
                </Collapse>
                <Collapse style={{ width: "100%" }} in={numberVerified}>
                  <div className={styles.eligible}>
                    <CheckCircle />
                    <div>Number is eligible for port in</div>
                  </div>
                </Collapse>

                <Button
                  style={{ marginTop: 15, width: "100%" }}
                  onClick={checkPortIn}
                  loading={loading}
                >
                  Check Eligibility
                </Button>
              </div>
            ) : activeSection === "enter-details" ? (
              <div className={styles.section} style={{ maxWidth: 356 }}>
                <h3>Port in</h3>
                <p className={styles.info}>Enter Subscriber Details</p>
                {fields.map((prop: string) => (
                  <Input
                    key={"account-" + prop}
                    label={labels[prop]}
                    placeholder={placeholders[prop]}
                    value={accountData[prop]}
                    onChange={(e: any) =>
                      handleInputChange(prop, e, accountData, setAccountData)
                    }
                    error={accountData.errors[prop]}
                    clear={() => {
                      clearInput(prop, setAccountData);
                    }}
                    disabled={loading}
                    white
                  />
                ))}
              </div>
            ) : activeSection === "enter-address" ? (
              <div className={styles.section} style={{ maxWidth: 430 }}>
                <h3>Port in</h3>
                <p className={styles.info}>Enter Subscriber Address</p>
                <div className={styles.oldInputsGrid}>
                  {oldAddressFields.map((prop: string) => {
                    if (prop === "state") {
                      return (
                        <SelectDropdown
                          value={addressData.state}
                          error={addressData.errors.state}
                          onChange={handleStateChange}
                          placeholder="State"
                          options={states}
                          dropDownMaxHeight={200}
                          white
                        />
                      );
                    } else if (prop === "streetDirection") {
                      return (
                        <div>
                          <div
                            style={{
                              fontSize: 10,
                              color: "#525252",
                              marginBottom: 6,
                            }}
                          >
                            *Street direction is an optional field
                          </div>
                          <SelectDropdown
                            value={addressData.streetDirection}
                            error={addressData.errors.streetDirection}
                            onChange={handleDirectionChange}
                            placeholder="Street Direction"
                            options={[
                              { label: "N", value: "N" },
                              { label: "E", value: "E" },
                              { label: "S", value: "S" },
                              { label: "W", value: "W" },
                              { label: "NE", value: "NE" },
                              { label: "NW", value: "NW" },
                              { label: "SE", value: "SE" },
                              { label: "SW", value: "SW" },
                            ]}
                            dropDownMaxHeight={200}
                            white
                          />
                        </div>
                      );
                    } else {
                      return (
                        <Input
                          key={"account-" + prop}
                          label={labels[prop]}
                          placeholder={placeholders[prop]}
                          value={addressData[prop]}
                          onChange={(e: any) =>
                            handleInputChange(
                              prop,
                              e,
                              addressData,
                              setAddressData,
                            )
                          }
                          error={addressData.errors[prop]}
                          clear={() => {
                            clearInput(prop, setAddressData);
                          }}
                          disabled={loading}
                          white
                        />
                      );
                    }
                  })}
                </div>
              </div>
            ) : activeSection === "enter-old-carrier-details" ? (
              <div className={styles.section} style={{ maxWidth: 291 }}>
                <h3>Port In</h3>
                <p className={styles.info}>Enter Old Carrier Details</p>
                <div className={styles.inputsGrid}>
                  {oldNumberFields.map((prop: string) => (
                    <Input
                      key={"oldnumber-" + prop}
                      label={labels[prop]}
                      placeholder={placeholders[prop]}
                      value={oldNumberData[prop]}
                      onChange={(e: any) =>
                        handleInputChange(
                          prop,
                          e,
                          oldNumberData,
                          setOldNumberData,
                          true,
                        )
                      }
                      error={oldNumberData.errors[prop]}
                      clear={() => {
                        clearInput(prop, setOldNumberData);
                      }}
                      disabled={loading}
                      white
                    />
                  ))}
                </div>
              </div>
            ) : activeSection === "enter-old-carrier-address" ? (
              <div className={styles.section}>
                <h3>Port In</h3>
                <p className={styles.info}>Enter Old Carrier Address</p>
                <div className={styles.useSubAddress}>
                  <span>Use Subscriber Address</span>
                  <Toggle on={useSubAddress} onChange={handleUseSubAddress} />
                </div>
                <div className={styles.oldInputsGrid}>
                  {oldAddressFields.map((prop: string) => {
                    if (prop === "state") {
                      return (
                        <SelectDropdown
                          value={oldAddressData.state}
                          error={oldAddressData.errors.state}
                          onChange={handleOldStateChange}
                          placeholder="State"
                          options={states}
                          dropDownMaxHeight={200}
                          white
                        />
                      );
                    } else if (prop === "streetDirection") {
                      return (
                        <div>
                          <div
                            style={{
                              fontSize: 10,
                              color: "#525252",
                              marginBottom: 6,
                            }}
                          >
                            *Street direction is an optional field
                          </div>
                          <SelectDropdown
                            value={oldAddressData.streetDirection}
                            error={oldAddressData.errors.streetDirection}
                            onChange={handleOldDirectionChange}
                            placeholder="Street Direction"
                            options={[
                              { label: "N", value: "N" },
                              { label: "E", value: "E" },
                              { label: "S", value: "S" },
                              { label: "W", value: "W" },
                              { label: "NE", value: "NE" },
                              { label: "NW", value: "NW" },
                              { label: "SE", value: "SE" },
                              { label: "SW", value: "SW" },
                            ]}
                            dropDownMaxHeight={200}
                            white
                          />
                        </div>
                      );
                    } else {
                      return (
                        <Input
                          key={"account-" + prop}
                          label={labels[prop]}
                          placeholder={placeholders[prop]}
                          value={oldAddressData[prop]}
                          onChange={(e: any) =>
                            handleInputChange(
                              prop,
                              e,
                              oldAddressData,
                              setOldAddressData,
                            )
                          }
                          error={oldAddressData.errors[prop]}
                          clear={() => {
                            clearInput(prop, setOldAddressData);
                          }}
                          disabled={loading}
                          white
                        />
                      );
                    }
                  })}
                </div>
              </div>
            ) : (
              <div></div>
            )}
          </CSSTransition>
        </SwitchTransition>
      </div>
    </Modal>
  );
};

export default PortInForm;
