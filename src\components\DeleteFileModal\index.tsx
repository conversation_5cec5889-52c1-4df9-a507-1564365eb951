import styles from "./view-note.module.scss";
import Modal from "../Modal";
import { formatDateWithTime } from "../utils/formatDate";
import Attachment from "../Attachment";
import { Delete } from "../svgs";

const DeleteFileModal = ({ show, setShow, item }: any) => {
  return (
    <Modal
      saveButton={
        <>
          <Delete />
          Yes, Delete File
        </>
      }
      cancelButton="No"
      image="/delete_user_graphic.svg"
      show={show}
      setShow={setShow}
      close={setShow}
      onClose={() => setShow(false)}
    >
      <div className={`${styles.main} `}>
        <h3>Are you sure you want to delete this file?</h3>
        <Attachment data={item} mode="del" />
      </div>
    </Modal>
  );
};

export default DeleteFileModal;
