export const throttles = [
  {
    featureCode: "APEX128",
    featureName: "APEX128",
    featureDescription: "Throttles to 128Kbps when added",
  },
  {
    featureCode: "APEX256",
    featureName: "APEX256",
    featureDescription: "Throttles to 256Kbps when added",
  },
  {
    featureCode: "APEX512",
    featureName: "APEX512",
    featureDescription: "Throttles to 512Kbps when added",
  },
  {
    featureCode: "APEX3",
    featureName: "APEX3",
    featureDescription: "Throttles to 3Mbps when added",
  },
  {
    featureCode: "APEX6",
    featureName: "APEX6",
    featureDescription: "Throttles to 6Mbps when added",
  },
  {
    featureCode: "APEX12",
    featureName: "APEX12",
    featureDescription: "Throttles to 12Mbps when added",
  },
  {
    featureCode: "APEXBLOCK",
    featureName: "APEXBLOCK",
    featureDescription: "Blocks all data on Smartphones/basic phones/wearables",
  },
];

export const toggleFeatures = [
  {
    featureCode: "NIRMAPEX",
    featureName: "Block International Roaming Except Mexico/Canada",
    featureDescription:
      "Block International Roaming Except Mexico/Canada - Compatible with all APEX plans (APEX Unlimited, APEX Mobile Select, APEX Exclusive, and Custom Plans).",
  },
  {
    featureCode: "TRKSOCV56",
    featureName: "Message Suppression",
    featureDescription:
      "Blocks DUCCS notice for international alerts and other automated AT&T messages",
  },
  {
    featureCode: "SMRTBLOCK",
    featureName: "Mobile Security Call Protect Block",
    featureDescription: "Mobile Security Call Protect Block",
  },
  {
    featureCode: "CIBL",
    featureName: "Caller ID Block",
    featureDescription: "Blocks featureName and number on caller id",
  },
  {
    featureCode: "ZZNOILD2",
    featureName: "International Long Distance Block",
    featureDescription:
      "Blocks international LD calls from the US.  Must delete ILDSMXCAO bundled SOC when adding blocking SOC for block to occur.  Blocks calls to Canada & Mexico even if included in price plan.",
  },
  {
    featureCode: "NOILDTEXT",
    featureName: "Blocking international texting",
    featureDescription: "Blocks international text from the US.  ",
  },
  {
    featureCode: "SMARTBCRU",
    featureName: "ActiveArmour Basic",
    featureDescription:
      "AT&T ActiveArmour Basic blocks spam and fraud calls when activated.  Add this SOC to the line and the end user will receive a text to activate the service",
  },
  {
    featureCode: "DSABR2",
    featureName: "Stream Saver",
    featureDescription: "Video Management",
  },
];

export const boltons = [
  {
    featureCode: "AZIRRLHDF",
    featureName: "Day Pass for Resale including roaming",
    featureDescription: "AT&T International Day Pass for Resale*",
  },
  {
    featureCode: "AZIR100VM",
    featureName: "International Data Bolt On 1GB Voice including roaming",
    featureDescription:
      "must be added with AZIRDV1GB to enable voice on smartphones",
  },
  {
    featureCode: "APEXILD",
    featureName: "Business World Connect Advantage",
    featureDescription: "International Long Distance Business Advantage Plan",
  },
  {
    featureCode: "AZIRDV1GB",
    featureName: "International Data Bolt On 1GB Data including roaming",
    featureDescription: "Must be added with API250VM to enable data on smartphones.",
  }
];
