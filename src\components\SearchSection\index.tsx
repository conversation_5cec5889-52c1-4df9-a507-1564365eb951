import { useState } from "react";
import SearchBar from "../SearchBar";
import { filterList, submitSearch } from "../utils/searchAndFilter";
import styles from "./search-section.module.scss";
import UserMenu from "../UserMenu";

const SearchSection = ({
  data,
  filters = null,
  setFilteredData,
  setQueryDisplay,
  placeholder,
  id,
  setCurrentPage,
  noHighlight = false,
  agents = null,
}: any) => {
  const [searchQuery, setSearchQuery] = useState("");

  return (
    <div className={styles.topBar}>
      <SearchBar
        query={searchQuery}
        setQuery={setSearchQuery}
        onSubmit={() => {
          if (filters === null) {
            submitSearch(
              data,
              setFilteredData,
              searchQuery,
              setQueryDisplay,
              id
            );
          } else {
            submitSearch(
              filterList(data, filters),
              setFilteredData,
              searchQuery,
              setQueryDisplay,
              id,
              agents
            );
          }
          setCurrentPage(1);
        }}
        placeholder={placeholder}
        id={id}
      />
      <UserMenu />
    </div>
  );
};

export default SearchSection;
