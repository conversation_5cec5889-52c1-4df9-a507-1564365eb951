import styles from "../../styles/mdn-activity.module.scss";
import { ArrowBack, Info } from "../../components/svgs";
import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import Button from "../../components/Button";
import Pagination from "../../components/Pagination";
import UserSkeleton from "../../components/UserSkeleton";
import { ApiGet } from "../api/api";
import { Link, useParams } from "react-router-dom";
import UserMenu from "../../components/UserMenu";
import { formatDateWithTime } from "../../components/utils/formatDate";
import MdnActivityResponse from "../../components/MdnActivityResponseInfo";

const MdnActivity = () => {
  const dispatch = useDispatch();

  const [initialLoading, setInitialLoading] = useState(true);

  const { mvnoId, mdn, id } = useParams();

  const [activityData, setActivityData] = useState([] as any);

  const [currentPage, setCurrentPage] = useState(1);
  const [maxPage, setMaxPage] = useState(1);

  const repopulate = () => {
    setInitialLoading(true);
    ApiGet(
      `/accounts/mdnActivity?mdn=${mdn}&mid=${id}&size=10&page=${currentPage - 1}&limit=0`,
    )
      .then((response) => {
        setInitialLoading(false);
        //setCurrentPage(response.data.pageable.pageNumber + 1);
        setMaxPage(response.data.totalPages);
        setActivityData(response.data.content);
      })
      .catch((error) => {
        setInitialLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message || "Something went wrong",
          },
        });
      });
  };

  useEffect(repopulate, [currentPage]);

  const [showInfo, setShowInfo] = useState(false);
  const [activeRecord, setActiveRecord] = useState(null as any);

  return (
    <div className={styles.main}>
      <MdnActivityResponse
        show={showInfo}
        setShow={setShowInfo}
        data={activeRecord}
      />
      <div className={styles.topBar}>
        <div className={styles.backLink}>
          <Link
            style={{ textDecoration: "none" }}
            to={`/${mvnoId}/subscriber/${id}`}
          >
            <Button color="tertiary" style={{ padding: 0, height: 26 }}>
              <ArrowBack /> Back to Subscriber Profile
            </Button>
          </Link>
        </div>
        <UserMenu />
      </div>
      <div className={styles.titleBar}>
        <h3>
          MDN Activity Log{" "}
          <span style={{ fontWeight: 400, fontSize: 18 }}>- {mdn}</span>
        </h3>
        {/*<Button style={{ marginLeft: "auto" }}>
          <AddUser />
          Add User
        </Button>*/}
      </div>
      <div className={styles.usersPanel} style={{ minHeight: "initial" }}>
        <div className={`${styles.tableContainer} table-scroll`}>
          <table>
            <thead>
              <tr>
                <th>Date & Time</th>
                <th>Email</th>
                <th>Source</th>
                <th>Activity</th>
                <th>Response</th>
              </tr>
            </thead>
            <tbody>
              {!initialLoading ? (
                activityData.length > 0 ? (
                  activityData.map((mdnRecord: any, index: number) => {
                    if (mdnRecord === null) {
                      return (
                        <tr
                          style={{
                            visibility: "hidden",
                            pointerEvents: "none",
                          }}
                        ></tr>
                      );
                    } else {
                      return (
                        <tr
                          key={"mdn-row-" + index}
                          style={{ cursor: "pointer" }}
                        >
                          <td>
                            {formatDateWithTime(mdnRecord.timestamp, false)}
                          </td>
                          <td>{mdnRecord.email || "-"}</td>
                          <td>{mdnRecord.source}</td>
                          <td>{mdnRecord.activity}</td>
                          <td>
                            <div
                              style={{ display: "flex", alignItems: "center" }}
                            >
                              {mdnRecord.successful ? (
                                <span
                                  style={{ color: "#037B53", fontWeight: 600 }}
                                >
                                  Success
                                </span>
                              ) : (
                                <span
                                  style={{ color: "#EA3D5C", fontWeight: 600 }}
                                >
                                  Fail
                                </span>
                              )}
                              <button
                                className={styles.infoButton}
                                onClick={() => {
                                  setActiveRecord(mdnRecord);
                                  setShowInfo(true);
                                }}
                              >
                                <Info />
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    }
                  })
                ) : (
                  <tr style={{ background: "none" }}>
                    <td colSpan={100}>
                      <div className={styles.noneFound}>
                        <img src="/none_found.svg" />
                        <h3>No logs found for this MDN</h3>
                      </div>
                    </td>
                  </tr>
                )
              ) : (
                Array.from({ length: 10 }, (v, i) => i).map((i) => (
                  <UserSkeleton key={"user-skeleton-" + i} noOfStandard={5} />
                ))
              )}
            </tbody>
          </table>
        </div>
        <div className={styles.pagination}>
          <Pagination
            numberOfPages={maxPage}
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
          />
        </div>
      </div>
    </div>
  );
};

export default MdnActivity;
