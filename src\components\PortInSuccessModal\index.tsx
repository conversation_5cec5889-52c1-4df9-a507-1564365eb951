import { useNavigate, useParams } from "react-router-dom";
import Modal from "../Modal";
import styles from "./port-in-success-modal.module.scss";

const PortInSuccessModal = ({
  show,
  setShow,
  handleFinish,
  handleCancel,
  update,
  exisitingSubscriber,
  newMid,
}: any) => {
  const navigate = useNavigate();
  const { mvnoId } = useParams();

  const handleToManagement = () => {
    if (update || exisitingSubscriber) {
      navigate(`/${mvnoId}/subscriber-management`);
    } else {
      handleCancel();
      handleFinish();
    }
  };

  const handleToProfile = () => {
    if (update || exisitingSubscriber) {
      handleCancel();
    } else {
      navigate(`/${mvnoId}/subscriber/${newMid}`);
    }
  };

  return (
    <Modal
      show={show}
      setShow={setShow}
      close={handleFinish}
      image="/green_robot.svg"
      clearContainer
      cancelButton="Subscriber Management"
      saveButton="Subscriber Profile Screen"
      onCancel={handleToManagement}
      proceed={handleToProfile}
    >
      <div className={styles.main}>
        <h3>Port-in Request {update ? "Updated" : "Sent"}!</h3>
        <div className={styles.message}>
          We have received your request and it is currently in progress. You can
          track the progress of your request by visiting your subscriber profile
          screen.
        </div>
      </div>
    </Modal>
  );
};

export default PortInSuccessModal;
