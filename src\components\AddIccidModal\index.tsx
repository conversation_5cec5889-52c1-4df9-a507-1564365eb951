import { useState, useEffect } from "react";
import { Input } from "../Input";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import {
  clearInput,
  createStateObject,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
  displayErrors,
} from "../utils/InputHandlers";
import styles from "./add-iccid-modal.module.scss";
import { useDispatch } from "react-redux";
import { validateAll } from "indicative/validator";
import { ApiPatch, ApiPostAuth } from "../../pages/api/api";

const fields = ["iccid"];
const rules = getRules(fields);
const messages = getMessages(fields);

const AddIccidModal = ({ show, setShow, repopulate, plan, name }: any) => {
  const dispatch = useDispatch();

  useEffect(() => {
    console.log(plan);
  }, [plan]);

  const handleChange = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPatch(
          "attDetails" in plan
            ? "/accounts/portin/temp/iccid"
            : "/accounts/tempsubscription/iccid",
          "attDetails" in plan
            ? {
                tempPortInId: plan.id,
                iccid: data.iccid,
              }
            : {
                tempSubscriptionId: plan.id,
                iccid: data.iccid,
              },
        )
          .then((response) => {
            reset();
            repopulate("temp");
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const reset = () => {
    setShow(false);
    setTimeout(() => {
      setData(createStateObject(fields));
      setLoading(false);
    }, 300);
  };

  const [data, setData] = useState(createStateObject(fields));
  const [loading, setLoading] = useState(false);

  return (
    <Modal
      saveButton={`${plan?.iccid ? "Edit" : "Add"} ICCID`}
      setData={setData}
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      proceed={handleChange}
      setShow={setShow}
      close={() => {
        clearInput("iccid", setData);
        setShow("");
      }}
      loading={loading}
    >
      <div className={styles.main}>
        <h4>{plan?.iccid ? "Edit" : "Add"} ICCID</h4>
        <p>{name}</p>
        <Input
          label={labels.iccid}
          placeholder={placeholders.iccid}
          value={data.iccid}
          onChange={(e: any) => {
            handleInputChange("iccid", e, data, setData);
          }}
          clear={() => {
            clearInput("iccid", setData);
          }}
          onKeyDown={handleChange}
          disabled={loading}
          error={data.errors.iccid}
        />
      </div>
    </Modal>
  );
};

export default AddIccidModal;
