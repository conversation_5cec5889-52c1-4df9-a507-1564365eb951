import { SwitchTransition, CSSTransition } from "react-transition-group";
import { Input } from "../Input";
import {
  clearInput,
  createStateObject,
  getMessages,
  getRules,
  labels,
  placeholders,
  handleInputChange,
  displayErrors,
} from "../utils/InputHandlers";
import styles from "./update-temp-sub.module.scss";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import Modal from "../Modal";
import { ArrowBack, CheckCircle, XCircle } from "../svgs";
import { validateAll } from "indicative/validator";
import { ApiPostAuth, ApiPutWithId } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { Collapse } from "@mui/material";
import RatePlan from "../RatePlan";
import AddSubscriberErrorModal from "../AddSubscriberErrorModal";

const iccidFields = ["iccid"];
const iccidRules = getRules(iccidFields);
const iccidMessages = getMessages(iccidFields);

const imeiFields = ["imei"];
const imeiRules = getRules(imeiFields);
const imeiMessages = getMessages(imeiFields);

const UpdateTempSubscription = ({
  show,
  setShow,
  repopulate,
  subData,
}: any) => {
  const dispatch = useDispatch();

  const [iccidData, setIccidData] = useState(createStateObject(iccidFields));
  const [imeiData, setImeiData] = useState(createStateObject(imeiFields));

  const [iccidError, setIccidError] = useState("");

  const [numberIneligible, setNumberIneligible] = useState(false);
  const [numberVerified, setNumberVerified] = useState(false);
  const [ineligibleReason, setIneligibleReason] = useState("");

  const [plans, setPlans] = useState([] as any);

  const [activePlan, setActivePlan] = useState({
    offerId: -1,
    offerName: "",
  } as any);

  useEffect(() => {
    if (subData !== null && show) {
      setIccidData({
        ...iccidData,
        iccid: subData.iccid,
      });
      setImeiData({ ...imeiData, imei: subData.imei });

      setActivePlan({
        deviceType: subData.product.deviceType,
        offerId: subData.product.offerId,
        offerName: subData.product.product,
        productFamily: subData.product.productFamily,
        size: subData.product.productSize,
        soc: subData.product.soc,
        serviceType: subData.product.serviceType,
        retailName: subData.product.retailName,
        retailPrice: subData.product.retailPrice,
      });
    }
  }, [show, subData]);

  const [loading, setLoading] = useState(false);

  const [activeSection, setActiveSection] = useState("update-imei");

  const { mvnoId } = useParams();

  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const reset = () => {
    setActiveSection("update-imei");
    setIccidData(createStateObject(iccidFields));
    setImeiData(createStateObject(imeiFields));
    setIccidError("");
    setShowError(false);
    setNumberIneligible(false);
    setNumberVerified(false);
    setActivePlan({
      offerId: -1,
      offerName: "",
    });
    setIneligibleReason("");
  };

  const checkImei = () => {
    validateAll(imeiData, imeiRules, imeiMessages)
      .then((response) => {
        setLoading(true);
        setNumberIneligible(false);
        setNumberVerified(false);
        setIneligibleReason("");
        ApiPostAuth("/products/availableproducts", {
          imei: imeiData.imei,
          mvnoId: parseInt(mvnoId!),
        })
          .then((response: any) => {
            setLoading(false);
            if (response.data.length) {
              setPlans(response.data);
              setNumberVerified(true);
            } else {
              setNumberIneligible(true);
            }
          })
          .catch((error) => {
            setLoading(false);
            setNumberIneligible(true);
            setIneligibleReason(error.response.data.message);
          });
      })
      .catch((errors) => {
        displayErrors(errors, setImeiData);
      });
  };

  const selectPlan = () => {
    if (activePlan.offerId !== -1) {
      setActiveSection("update-iccid");
    }
  };

  const checkIccid = () => {
    validateAll(iccidData, iccidRules, iccidMessages)
      .then(() => {
        /*if (iccidData.iccid !== subData.iccid) {
          setLoading(true);
          ApiPostAuth("/accounts/validate/iccid", {
            iccid: iccidData.iccid,
          })
            .then(() => {
              updateSubscription();
            })
            .catch((error) => {
              setLoading(false);
              setIccidError(error.response.data.message);
            });
        } else {
          updateSubscription();
        }*/
        updateSubscription();
      })
      .catch((errors) => {
        displayErrors(errors, setIccidData);
      });
  };

  const goToPlans = () => {
    setActiveSection("select-plan");
  };

  const handleBack = () => {
    if (activeSection === "select-plan") {
      setActiveSection("update-imei");
    } else if (activeSection === "update-iccid") {
      setActiveSection("select-plan");
    }
  };

  const [tether, setTether] = useState(true);

  const updateSubscription = () => {
    setLoading(true);
    ApiPutWithId("/accounts/tempsubscription/update", mvnoId, {
      tempSubId: subData?.id?.toString(),
      imei: imeiData.imei,
      iccid: iccidData.iccid,
      product: activePlan,
    })
      .then((response) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulate("temp");
        setShow(false);
        setLoading(false);
        setTimeout(() => {
          reset();
        }, 300);
      })
      .catch((error) => {
        setLoading(false);
        setShowError(true);
        setErrorMessage(
          error.response.data.attResponse?.errorDescription ||
            error.response.data.message,
        );
      });
  };

  return (
    <Modal
      saveButton="Continue"
      cancelButton="Cancel"
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      proceed={
        activeSection === "update-imei"
          ? numberVerified
            ? goToPlans
            : checkImei
          : activeSection === "select-plan"
            ? selectPlan
            : checkIccid
      }
      close={() => {
        setShow(false);
        setTimeout(() => {
          reset();
        }, 300);
      }}
      loading={loading}
      fullSize
      title={activeSection === "select-plan" && "Update Plan"}
      subtitle={
        activeSection === "select-plan" ? (
          <SelectPlanSubtitle tether={tether} setTether={setTether} />
        ) : null
      }
      onCancel={() => {
        setShow(false);
        setTimeout(() => {
          reset();
        }, 300);
      }}
    >
      <AddSubscriberErrorModal
        show={showError}
        setShow={setShowError}
        error={errorMessage}
      />
      <div className={styles.main} style={{ width: "auto" }}>
        {activeSection !== "update-imei" && (
          <button className={styles.backButton} onClick={handleBack}>
            <ArrowBack />
          </button>
        )}
        {activeSection === "update-imei" ? (
          <div className={styles.imeiMain}>
            <h3>Update IMEI</h3>
            <h4>Enter device IMEI</h4>
            <Input
              label={labels.imei}
              placeholder={placeholders.imei}
              value={imeiData.imei}
              onChange={(e: any) => {
                handleInputChange("imei", e, imeiData, setImeiData);
              }}
              error={imeiData.errors.imei}
              clear={() => {
                clearInput("imei", setImeiData);
              }}
              disabled={loading}
              onKeyDown={checkImei}
            />
            <Collapse style={{ width: "100%" }} in={numberVerified}>
              <div className={styles.eligible}>
                <CheckCircle />
                <div>IMEI is eligible</div>
              </div>
            </Collapse>
            <Collapse style={{ width: "100%" }} in={numberIneligible}>
              <div className={styles.notEligible}>
                <XCircle />
                <div>
                  <div className={styles.topText}>IMEI not eligible</div>
                  <div className={styles.bottomText}>{ineligibleReason}</div>
                </div>
              </div>
            </Collapse>
          </div>
        ) : activeSection === "select-plan" ? (
          <div className={styles.plansMain}>
            <SwitchTransition>
              <CSSTransition
                key={tether ? "tether" : "no-tether"}
                addEndListener={(node, done) =>
                  node.addEventListener("transitionend", done, false)
                }
                classNames="fade"
              >
                {tether ? (
                  <div className={styles.plans}>
                    {plans.filter(
                      (item: any) => item.teaserType === "Custom Overage",
                    ).length > 0 ? (
                      plans
                        .filter(
                          (item: any) => item.teaserType === "Custom Overage",
                        )
                        .map((plan: any) => (
                          <RatePlan
                            plan={plan}
                            activePlan={activePlan}
                            setActivePlan={setActivePlan}
                            key={`rate-plan-${plan.offerId}`}
                          />
                        ))
                    ) : (
                      <div className={styles.noPlans}>
                        <span>
                          There aren't any tether plans available for this IMEI
                        </span>
                        <img src="/none_found.svg" />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className={styles.plans}>
                    {plans.filter(
                      (item: any) => item.teaserType !== "Custom Overage",
                    ).length > 0 ? (
                      plans
                        .filter(
                          (item: any) => item.teaserType !== "Custom Overage",
                        )
                        .map((plan: any) => (
                          <RatePlan
                            plan={plan}
                            activePlan={activePlan}
                            setActivePlan={setActivePlan}
                            noTether
                            key={`rate-plan-${plan.offerId}`}
                          />
                        ))
                    ) : (
                      <div className={styles.noPlans}>
                        <span>
                          There aren't any no tether plans available for this
                          IMEI
                        </span>
                        <img src="/none_found.svg" />
                      </div>
                    )}
                  </div>
                )}
              </CSSTransition>
            </SwitchTransition>
          </div>
        ) : activeSection === "update-iccid" ? (
          <div className={styles.section}>
            <h3>Update ICCID</h3>
            <p className={styles.info}>Number should be between 18-22 digits</p>
            <div style={{ maxWidth: 350, margin: "0 auto" }}>
              <Input
                label={labels.iccid}
                placeholder={placeholders.iccid}
                value={iccidData.iccid}
                onChange={(e: any) =>
                  handleInputChange("iccid", e, iccidData, setIccidData)
                }
                error={iccidData.errors.iccid}
                clear={() => {
                  clearInput("iccid", setIccidData);
                }}
                disabled={loading}
                white
              />
              <Collapse in={iccidError !== ""}>
                <div className={styles.notEligible}>
                  <XCircle />
                  <div>
                    <div className={styles.topText}>{iccidError}</div>
                  </div>
                </div>
              </Collapse>
            </div>
          </div>
        ) : (
          ""
        )}
      </div>
    </Modal>
  );
};

export default UpdateTempSubscription;

const root = getComputedStyle(document.getElementById("root")!);

const SelectPlanSubtitle = ({ tether, setTether }: any) => (
  <div className={styles.planType}>
    <div>Select a plan</div>
    <div
      className={styles.planSwitch}
      style={{
        backgroundColor: tether
          ? root.getPropertyValue("--faded-orange")
          : "#E0DCDC",
      }}
      onClick={() => {
        setTether((prev: boolean) => !prev);
      }}
    >
      <div className={styles.label} style={{ color: tether ? "#fff" : "#000" }}>
        Tether Plans
      </div>
      <div
        className={styles.label}
        style={{ color: !tether ? "#fff" : "#000" }}
      >
        No Tether Plans
      </div>
      <div
        className={styles.thumb}
        style={{
          right: tether ? "163px" : "0px",
          backgroundColor: tether
            ? root.getPropertyValue("--orange")
            : "#1a1a1a",
        }}
      />
    </div>
  </div>
);
