import styles from "../../styles/login.module.scss";
import { Input } from "../../components/Input";
import Button from "../../components/Button";
import { Link } from "react-router-dom";
import { ArrowBack } from "../../components/svgs";
import { validateAll } from "indicative/src/Validator";
import { useState } from "react";
import { useDispatch } from "react-redux";
import $ from "jquery";
import { ApiPost } from "../api/api";

const rules = {
  email: "required|email",
};

const messages = {
  "email.required":
    "Please enter a valid email address. Example: <EMAIL>",
  "email.email":
    "Please enter a valid email address. Example: <EMAIL>",
};

const ForgotPassword = () => {
  const dispatch = useDispatch();

  const [email, setEmail] = useState({
    value: "",
    error: "",
  });

  const [loading, setLoading] = useState(false);

  const handleChange = (e: any) => {
    setEmail({
      value: e.target.value,
      error: "",
    });
  };

  const sendEmail = () => {
    const data = {
      email: email.value.trim(),
    };

    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPost("/users/forgotpassword", {
          email: data.email,
        })
          .then((response) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
            setEmail({
              value: "",
              error: "",
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((error) => {
        setEmail({
          ...email,
          error: error[0].message,
        });
        $("#forgot-email").trigger("focus");
      });
  };

  const root = getComputedStyle(document.getElementById("root")!);

  return (
    <div className={styles.container}>
      <div className={styles.main}>
        <Link to="/login">
          <img src="/Logo.png" className={styles.logo} />
        </Link>
        <div className={styles.formContainer}>
          <div className={styles.form}>
            <h2 style={{ marginBottom: 12 }}>Forgot Password</h2>
            <p style={{ margin: "0 0 32px 0", textAlign: "center" }}>
              Enter your email address and we’ll send you a password reset link
            </p>
            <div style={{ maxWidth: 350, width: "100%" }}>
              <Input
                label="Email Address"
                placeholder="Enter your email"
                value={email.value}
                error={email.error}
                onChange={(e: any) => {
                  handleChange(e);
                }}
                onKeyDown={sendEmail}
                disabled={loading}
                clear={() => {
                  setEmail({ value: "", error: "" });
                }}
                id="forgot-email"
              />
            </div>
            <Button
              style={{ marginTop: 30 }}
              color="primary"
              loading={loading}
              onClick={sendEmail}
            >
              Send Password Reset Link
            </Button>
          </div>
        </div>
        <div className={styles.forgotPassword}>
          <Link to="/login" style={{ textDecoration: "none" }}>
            <Button color="tertiary">
              <ArrowBack color={root.getPropertyValue("--orange")} />
              Back to Login
            </Button>
          </Link>
        </div>
      </div>
      <img src="/forgot_password_graphic.svg" className={styles.graphic} />
    </div>
  );
};

export default ForgotPassword;
