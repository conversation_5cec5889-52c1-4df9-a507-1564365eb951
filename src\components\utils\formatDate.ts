import moment from "moment";

export const months = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

const monthsLong = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const formatDate = (dateInput: any, shortDate = false, noReplace = false) => {
  const date: any =
    typeof dateInput === "string" && noReplace === false
      ? moment(dateInput.replace(/-/g, "/").replaceAll("Z", ""))
      : moment(dateInput);
  if (!date._isValid) return "N/A";
  const day = date.date().toString();
  const month = months[date.month()];
  const year = shortDate
    ? date.year().toString().slice(2)
    : date.year().toString();
  return `${day} ${month} ${year}`;
};

export const formatDateWithTime = (dateInput: any, replace = true) => {
  const date =
    typeof dateInput === "string" && replace
      ? moment(dateInput.replace(/-/g, "/").replaceAll("Z", ""))
      : moment(dateInput);
  const day = date.date().toString();
  const month = months[date.month()];
  const year = date.year().toString();
  const hour = date.hour().toString().padStart(2, "0");
  const minutes = date.minute().toString().padStart(2, "0");
  const seconds = date.second().toString().padStart(2, "0");
  return `${day} ${month} ${year}, ${hour}:${minutes}:${seconds}`;
};

export const formatSyncedDate = (dateInput: any) => {
  const date = moment(dateInput);
  const day = date.date().toString().padStart(2, "0");
  const month = (date.month() + 1).toString().padStart(2, "0");
  const year = date.year().toString();
  const hour = date.hour().toString().padStart(2, "0");
  const minutes = date.minute().toString().padStart(2, "0");
  return `${day}.${month}.${year} ${hour}:${minutes}`;
};

export const formatChartDate = (dateInput: any) => {
  const date = moment(dateInput);
  const day = date.date().toString();
  const daySuffix = getSuffix(day);
  const month = monthsLong[date.month()];
  const year = date.year();
  const hours = date.hour().toString().padStart(2, "0");
  const minute = date.minute().toString().padStart(2, "0");
  return `${day}${daySuffix} ${month} ${year} ${dateInput.includes("T") ? `${hours}:${minute}` : ""}`;
};

export const formatChartDateLabel = (dateInput: any) => {
  const date = moment(dateInput);
  const day = date.date().toString().padStart(2, "0");
  const month = months[date.month()];
  const hours = date.hour().toString().padStart(2, "0");
  const minute = date.minute().toString().padStart(2, "0");
  return dateInput.includes("T") ? `${hours}:${minute}` : [day, month];
};

const getSuffix = (day: string) => {
  if (day === "11" || day === "12" || day === "13") {
    return "th";
  }

  const end = day.slice(day.length - 1, day.length);
  switch (end) {
    case "1":
      return "st";
    case "2":
      return "nd";
    case "3":
      return "rd";
    default:
      return "th";
  }
};

export default formatDate;
