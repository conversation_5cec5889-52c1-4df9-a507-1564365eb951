import { statuses } from "../StatusPill";
import { isADate } from "./dateHelpers";

// Handle adding and removal of column filters
export const handleFilterChange = (
  type: string,
  option: string,
  filterObject: any,
  setFilterObject: any
) => {
  let current = filterObject[type];
  if (current.includes(option)) {
    current = current.filter((item: any) => item !== option);
  } else {
    current.push(option);
  }
  setFilterObject({
    ...filterObject,
    [type]: current,
  });
};

// Handle removal of a filter group
export const handleRemoveFilterType = (
  type: string,
  filterObject: any,
  setFilterObject: any
) => {
  setFilterObject({
    ...filterObject,
    [type]: [],
  });
};

export const subscriptionFields = [
  "activationDate",
  "carrier",
  "creationDate",
  "subscriberStatus",
];

export const productFields = ["productSize", "serviceType"];

const checkDateRange = (item: any, filterObject: any, key: string) => {
  const date =
    typeof item[key] === "string"
      ? new Date(item[key].replace(/-/g, "/").replaceAll("Z", ""))
      : new Date(item[key]);
  return (
    date.getTime() >= filterObject[key].start.getTime() &&
    date.getTime() < filterObject[key].end.getTime() + 86400000
  );
};

// Handles filtering of data list each time the filters change
export const filterList = (data: any, filterObject: any) => {
  const filterKeys = Object.keys(filterObject);
  let filteredArr = [...data];
  filterKeys.forEach((key: string) => {
    // Check if a standard filter array of strings
    if (Array.isArray(filterObject[key])) {
      if (filterObject[key].length > 0) {
        if (subscriptionFields.includes(key)) {
          filteredArr = filteredArr.filter((sub: any) => {
            if (
              key === "subscriberStatus" &&
              filterObject[key].includes("No Subscription")
            ) {
              return (
                sub.subscriptions.length === 0 ||
                sub.subscriptions.some((item: any) =>
                  filterObject[key].includes(item[key])
                )
              );
            } else {
              return sub.subscriptions.some((item: any) =>
                filterObject[key].includes(item[key])
              );
            }
          });
          filteredArr.forEach((subscriber: any, index: number) => {
            filteredArr[index] = {
              ...subscriber,
              subscriptions: subscriber.subscriptions.filter(
                (subscription: any) =>
                  filterObject[key].includes(subscription[key])
              ),
            };
          });
        } else {
          filteredArr = filteredArr.filter((sub: any) =>
            filterObject[key].includes(sub[key])
          );
        }
      }

      // Otherwise it's a date range, so filter date accordingly
    } else {
      if (filterObject[key].start && filterObject[key].end) {
        if (subscriptionFields.includes(key)) {
          filteredArr = filteredArr.filter((sub: any) =>
            sub.subscriptions.some((item: any) => {
              return checkDateRange(item, filterObject, key);
            })
          );
          filteredArr.forEach((subscriber: any, index: number) => {
            filteredArr[index] = {
              ...subscriber,
              subscriptions: subscriber.subscriptions.filter(
                (subscription: any) => {
                  return checkDateRange(subscription, filterObject, key);
                }
              ),
            };
          });
        } else {
          filteredArr = filteredArr.filter((sub: any) => {
            if (sub[key]) {
              return checkDateRange(sub, filterObject, key);
            } else {
              return false;
            }
          });
        }
      }
    }
  });
  return filteredArr;
};

// Highlight search query in the results
const getHighlightSpan = (
  searchingIn: any,
  query: any,
  exactMatch: boolean,
  allWordsMatch: boolean
) => {
  const combinedRegex = exactMatch
    ? new RegExp(query.join(" "), "gi")
    : new RegExp(query.join("|"), "gi");
  searchingIn = searchingIn.replace(
    combinedRegex,
    (prev: any) => `<span class="highlight-search-result">${prev}</span>`
  );

  return <span dangerouslySetInnerHTML={{ __html: searchingIn }}></span>;
};

export const highlightSearch = (singleItem: any, searchQuery: string) => {
  let query = searchQuery.replace(/[.*+?^${}()|[\]\\]/g, "\\$&").split(" ");
  let result = singleItem as any;
  if (searchQuery !== "") {
    if (typeof singleItem === "string") {
      let searchingIn = singleItem;
      const combinedRegex = new RegExp(query.join("|"), "gi");
      searchingIn = searchingIn.replace(
        combinedRegex,
        (prev: any) => `<span class="highlight-search-result">${prev}</span>`
      );

      result = <span dangerouslySetInnerHTML={{ __html: searchingIn }}></span>;
    }
  }
  return result;
};

const checkExactMatch = (item: any, searchQuery: any, agents: any) => {
  let values = Object.values(item);

  if (agents) {
    let assigned = agents.find((i: any) => item.id === i.agentId);

    values = Object.values({
      ...item,
      agentId: assigned ? assigned.name : null,
      description: "",
    });
  }

  if ("subscriptions" in item) {
    item.subscriptions.forEach((subscription: any) => {
      values = [...values, ...Object.values(subscription)];
    });
  }
  return values.some((objValue: any) => {
    if (typeof objValue === "string") {
      return objValue.toLowerCase().includes(searchQuery.toLowerCase());
    } else {
      return false;
    }
  });
};

const checkAllWordsMatch = (item: any, query: any, agents: any) => {
  let values = Object.values(item);

  if (agents) {
    let assigned = agents.find((i: any) => item.id === i.agentId);

    values = Object.values({
      ...item,
      agentId: assigned ? assigned.name : null,
      description: "",
    });
  }

  if ("subscriptions" in item) {
    item.subscriptions.forEach((subscription: any) => {
      values = [...values, ...Object.values(subscription)];
    });
  }
  return query.every((queryStr: any) => {
    return values.some((objValue: any) => {
      if (typeof objValue === "string") {
        return objValue.toLowerCase().includes(queryStr.toLowerCase());
      } else {
        return false;
      }
    });
  });
};

// Submits a search for user - filters users and then highlights query in the table
export const submitSearch = (
  data: any,
  setFilteredData: any,
  searchQuery: string,
  setQueryDisplay: any,
  id: any,
  agents: any = null
) => {
  if (searchQuery === "") {
    setQueryDisplay("");
    setFilteredData(data);
    return;
  }
  setQueryDisplay(searchQuery);
  let query = searchQuery.split(" ");

  if (id === "notes-search") {
    let filteredData = data.filter((note: any) =>
      note.text.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredData(filteredData);
    return;
  }

  // Filter users to only show users that contain query string
  let usersToShow = data.filter((singleUser: any) => {
    let userObj = { ...singleUser };

    if (id === "tickets-search" && agents) {
      let assigned = agents.find((item: any) => item.id === userObj.agentId);

      userObj = {
        ...userObj,
        agentId: assigned ? assigned.name : null,
        description: "",
      };
    }
    /* //Replace numerical status with string versions
    userObj.subscriberStatus = statuses[userObj.subscriberStatus];*/

    let values = Object.values(userObj);
    if ("subscriptions" in userObj) {
      userObj.subscriptions.forEach((subscription: any) => {
        values = [...values, ...Object.values(subscription)];
      });
    }
    if ("feature" in userObj) {
      values = [...values, ...Object.values(userObj.feature)];
    }
    return values.some((objValue: any) => {
      if (typeof objValue === "string") {
        return query.some((singleQuery: string) => {
          if (isADate(objValue)) {
            return false;
          } else {
            return objValue
              .toString()
              .toLowerCase()
              .includes(singleQuery.toLowerCase());
          }
        });
      } else {
        return false;
      }
    });
  });

  const exactMatch = usersToShow.some((item: any) => {
    return id === "user-search"
      ? checkUsersExactMatch(item, searchQuery)
      : checkExactMatch(item, searchQuery, agents);
  });

  const allWordsMatch = usersToShow.some((item: any) => {
    return id === "user-search"
      ? checkUserAllWordsMatch(item, query)
      : checkAllWordsMatch(item, query, agents);
  });

  if (exactMatch) {
    usersToShow = usersToShow.filter((item: any) => {
      return id === "user-search"
        ? checkUsersExactMatch(item, searchQuery)
        : checkExactMatch(item, searchQuery, agents);
    });
  } else if (allWordsMatch) {
    usersToShow = usersToShow.filter((item: any) => {
      return id === "user-search"
        ? checkUserAllWordsMatch(item, query)
        : checkAllWordsMatch(item, query, agents);
    });
  }

  // Filter multiple subscriptions for a subscriber
  if (usersToShow.length !== 0 && "subscriptions" in usersToShow[0]) {
    usersToShow.forEach((subscriber: any, index: number) => {
      let filteredSubscriptions = subscriber.subscriptions.filter(
        (subscription: any) => {
          let values = [...Object.values(subscription)];
          return values.some((objValue: any) => {
            if (typeof objValue === "string") {
              return query.some((singleQuery: string) =>
                objValue.toLowerCase().includes(singleQuery.toLowerCase())
              );
            } else {
              return false;
            }
          });
        }
      );

      usersToShow[index] = {
        ...subscriber,
        subscriptions:
          filteredSubscriptions.length === 0
            ? subscriber.subscriptions
            : filteredSubscriptions,
      };
    });
  }

  // Highlight the query string within the table data
  let highlightedUsersToShow = usersToShow.map((singleUser: any) => {
    let entries = Object.entries(singleUser);
    let result = {} as any;
    entries.forEach((entry: any) => {
      if (
        typeof entry[1] === "string" &&
        entry[0] !== "time" &&
        entry[0] !== "roleName" &&
        entry[0] !== "video" &&
        entry[0] !== "answer" &&
        entry[0] !== "status" &&
        entry[0] !== "simType" &&
        entry[0] !== "category" &&
        entry[0] !== "assignee" &&
        entry[0] !== "desription"
      ) {
        let searchingIn = entry[1];
        result[entry[0]] = getHighlightSpan(
          searchingIn,
          query,
          exactMatch,
          allWordsMatch
        );
      } else if (entry[0] === "subscriptions") {
        let subscriptions = [...entry[1]];
        subscriptions.forEach((subscription: any, index: number) => {
          let subEntries = Object.entries(subscription);
          let subResult = {} as any;
          subEntries.forEach((subEntry: any) => {
            if (
              (typeof subEntry[1] === "string" ||
                typeof subEntry[1] === "number") &&
              subEntry[0] !== "creationDate" &&
              subEntry[0] !== "activationDate" &&
              subEntry[0] !== "subscriberStatus"
            ) {
              let searchingIn = subEntry[1].toString();
              subResult[subEntry[0]] = getHighlightSpan(
                searchingIn,
                query,
                exactMatch,
                allWordsMatch
              );
            } else {
              subResult[subEntry[0]] = subEntry[1];
            }
          });
          subscriptions[index] = subResult;
        });
        result.subscriptions = subscriptions;
      } else if (entry[0] === "feature") {
        let details = Object.entries(entry[1]);
        let featureResult = {} as any;
        details.forEach((featureEntry: any) => {
          let searchingIn = featureEntry[1].toString();
          featureResult[featureEntry[0]] = getHighlightSpan(
            searchingIn,
            query,
            exactMatch,
            allWordsMatch
          );
        });
        result.feature = featureResult;
      } else {
        result[entry[0]] = entry[1];
      }
    });
    return result;
  }) as any;

  if (id === "tickets-search") {
    setFilteredData(usersToShow);
  } else {
    setFilteredData(highlightedUsersToShow);
  }
};

export const submitFilterSelectSearch = (
  data: any,
  setFilteredData: any,
  searchQuery: string,
  setQueryDisplay: any
) => {
  if (searchQuery === "") {
    setFilteredData(data);
    return;
  }
  setQueryDisplay(searchQuery);
  let query = searchQuery.split(" ");

  // Filter users to only show users that contain query string
  let usersToShow = data.filter((singleUser: any) => {
    let userObj = { ...singleUser };

    /* //Replace numerical status with string versions
    userObj.subscriberStatus = statuses[userObj.subscriberStatus];*/

    let values = Object.values(userObj);
    return values.some((objValue: any) => {
      if (typeof objValue === "string") {
        return query.some((singleQuery: string) =>
          objValue.toLowerCase().includes(singleQuery.toLowerCase())
        );
      } else {
        return false;
      }
    });
  });

  setFilteredData(usersToShow);
};

const checkUsersExactMatch = (item: any, searchQuery: any) => {
  return item.firstName.toLowerCase() === searchQuery.toLowerCase();
};

const checkUserAllWordsMatch = (item: any, query: any) => {
  let values = Object.values(item);
  if ("subscriptions" in item) {
    item.subscriptions.forEach((subscription: any) => {
      values = [...values, ...Object.values(subscription)];
    });
  }
  return query.every((queryStr: any) => {
    return values.some((objValue: any) => {
      if (typeof objValue === "string") {
        return objValue.toLowerCase().includes(queryStr.toLowerCase());
      } else {
        return false;
      }
    });
  });
};
