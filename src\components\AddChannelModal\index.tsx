import styles from "./add-channel.module.scss";
import Modal from "../Modal";
import { Plus } from "../svgs";
import { Input } from "../Input";
import { useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch } from "react-redux";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import { ApiPostAuth } from "../../pages/api/api";
import { useParams } from "react-router-dom";
import TextArea from "../TextArea";

const fields = ["channelName", "channelDescription"];
const rules = getRules(fields);
const messages = getMessages(fields);

const AddChannelModal = ({ show, setShow, repopulate }: any) => {
  const dispatch = useDispatch();

  const [data, setData] = useState(createStateObject(fields));

  const { mvnoId }: any = useParams();

  // Reset modal data when closed
  const reset = () => {
    setData(createStateObject(fields));
    setLoading(false);
  };

  // Handles creation of new channel
  const createChannel = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPostAuth("/channels/create", {
          name: data.channelName,
          description: data.channelDescription,
          mvnoId: mvnoId,
        })
          .then((response) => {
            reset();
            repopulate();
            dispatch({
              type: "notify",
              payload: {
                error: false,
                heading: "Success",
                message: response.data.message,
              },
            });
            setShow(false);
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                heading: "Something went wrong",
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const [loading, setLoading] = useState(false);

  return (
    <Modal
      saveButton={
        <>
          <Plus />
          Add Channel
        </>
      }
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={createChannel}
      close={() => {
        setShow(false);
        setTimeout(() => {
          reset();
        }, 300);
      }}
      loading={loading}
      fullSize
    >
      <div className={`${styles.main} normal-select-input`}>
        <h3>Add Channel</h3>
        {fields.map((field) => {
          if (field === "channelDescription") {
            return (
              <TextArea
                key={`${field}-input`}
                label={labels[field]}
                placeholder={placeholders[field]}
                value={data[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, data, setData);
                }}
                error={data.errors[field]}
                onKeyDown={createChannel}
                clear={() => {
                  clearInput(field, setData);
                }}
                disabled={loading}
                white
              />
            );
          } else {
            return (
              <Input
                key={`${field}-input`}
                label={labels[field]}
                placeholder={placeholders[field]}
                value={data[field]}
                onChange={(e: any) => {
                  handleInputChange(field, e, data, setData);
                }}
                error={data.errors[field]}
                onKeyDown={createChannel}
                clear={() => {
                  clearInput(field, setData);
                }}
                disabled={loading}
                white
              />
            );
          }
        })}
      </div>
    </Modal>
  );
};

export default AddChannelModal;
