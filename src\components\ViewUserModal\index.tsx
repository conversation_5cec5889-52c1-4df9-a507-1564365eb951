import styles from "./view-user.module.scss";
import Modal from "../Modal";
import { Input } from "../Input";
import SelectInput from "../SelectInput";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import Toggle from "../Toggle";
import {
  clearInput,
  createStateObject,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";

const fields = ["firstName", "lastName", "email", "role"];

const ViewUserModal = ({ show, setShow, user, handleEditUser }: any) => {
  const dispatch = useDispatch();

  const [data, setData] = useState(createStateObject(fields, " "));
  const [twoFactor, setTwoFactor] = useState(false);

  // Populate with current user's data
  useEffect(() => {
    if (user) {
      setData({
        ...data,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.roleName,
      });
      setTwoFactor(user.is2faEnabled);
    }
  }, [user]);

  const [loading, setLoading] = useState(true);

  return (
    <Modal
      saveButton="Edit Details"
      image="/view_user_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={() => {
        handleEditUser(user);
      }}
      close={() => {
        setShow(false);
      }}
      loading={false}
      fullSize
      title="User Details"
      //secondary="Resend Welcome Email"
    >
      <div className={`${styles.main} normal-select-input`}>
        {fields.map((prop) =>
          prop === "role" ? (
            <SelectInput
              key="view-role"
              placeholder="Role"
              options={["Agent", "Admin"]}
              selected={data.role}
              onChange={(value: any) => {
                setData({
                  ...data,
                  role: value,
                  errors: {
                    ...data.errors,
                    role: "",
                  },
                });
              }}
              readonly
              error={data.errors.role}
            />
          ) : (
            <Input
              key={`view-user-${prop}`}
              label={labels[prop]}
              placeholder={placeholders[prop]}
              value={data[prop]}
              onChange={(e: any) => {
                handleInputChange(prop, e, data, setData);
              }}
              error={data.errors[prop]}
              onKeyDown={() => {}}
              clear={() => {
                clearInput(prop, setData);
              }}
              readonly
            />
          )
        )}
        <div className={styles.twoFactor}>
          <div className={styles.twoFaLabel}>Two-Factor Authentication</div>
          <div className={styles.toggleContainer}>
            <div className={styles.onOff}>{twoFactor ? "On" : "Off"}</div>
            <Toggle
              on={twoFactor}
              onChange={() => {
                setTwoFactor((prev: boolean) => !prev);
              }}
              readonly
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ViewUserModal;
