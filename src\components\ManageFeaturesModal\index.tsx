import { useEffect, useState } from "react";
import Modal from "../Modal";
import { FloppyDisk, Plus, XCircle } from "../svgs";
import styles from "./manage-features.module.scss";
import Toggle from "../Toggle";
import AddFeaturesModal from "../AddFeaturesModal";
import { ApiGetNoAuth, ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
// import { toggleFeatures } from "../utils/featureLists";
import AddBoltonsModal from "../AddBoltonsModal";
import { tetherList } from "../utils/tetherList";
import { useParams } from "react-router-dom";

const ManageFeatureModal = ({
  sub,
  show,
  setShow,
  repopulate,
  features,
}: any) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const { id } = useParams();

  const handleToggleFeature = (feature: any) => {
    if (featureAdd.some((item: any) => item.code === feature.code)) {
      setFeatureAdd((prev: any) =>
        prev.filter((item: any) => item.code !== feature.code),
      );
    } else if (featureRemove.some((item: any) => item.code === feature.code)) {
      setFeatureRemove((prev: any) =>
        prev.filter((item: any) => item.code !== feature.code),
      );
    } else if (sub.features?.some((item: any) => item.code === feature.code)) {
      setFeatureRemove((prev: any) => [...prev, feature]);
    } else {
      setFeatureAdd((prev: any) => [...prev, feature]);
    }
  };

  const [throttleRemove, setThrottleRemove] = useState([] as any);
  const [throttleAdd, setThrottleAdd] = useState([] as any);

  const [boltonRemove, setBoltonRemove] = useState([] as any);
  const [boltonAdd, setBoltonAdd] = useState([] as any);

  const [featureRemove, setFeatureRemove] = useState([] as any);
  const [featureAdd, setFeatureAdd] = useState([] as any);

  const [activeRemoveType, setActiveRemoveType] = useState("");
  const [activeRemove, setActiveRemove] = useState(null as any);

  const reset = () => {
    setThrottleRemove([] as any);
    setThrottleAdd([] as any);
    setBoltonRemove([] as any);
    setBoltonAdd([] as any);
    setFeatureRemove([] as any);
    setFeatureAdd([] as any);
  };

  const getStage = (typeList: any, removeList: any, addList: any) => {
    if (sub) {
      return [
        ...sub.features?.filter(
          (item: any) =>
            !removeList.some(
              (removeItem: any) => removeItem.code === item.code,
            ) && typeList.some((typeItem: any) => typeItem.code === item.code),
        ),
        ...addList,
      ];
    } else {
      return [];
    }
  };

  const getThrottleStage = () => {
    return getStage(
      features.filter((item: any) => item.classificationName === "throttle"),
      throttleRemove,
      throttleAdd,
    );
  };

  const getBoltonStage = () => {
    return getStage(
      features.filter((item: any) => {
        return (
          item.classificationName === "bolton" &&
          !["API250VM", "AZIDX10GB", "APIDV10GB"].includes(item.code)
        );
      }),
      boltonRemove,
      boltonAdd,
    );
  };

  const getFeatureStage = () => {
    return getStage(features, featureRemove, featureAdd);
  };

  const [showConfirmRemove, setShowConfirmRemove] = useState(false);

  const handleRemoveActive = () => {
    if (activeRemoveType === "Throttle") {
      if (throttleAdd.some((item: any) => item.code === activeRemove.code)) {
        setThrottleAdd((prev: any) =>
          prev.filter((item: any) => item.code !== activeRemove.code),
        );
      } else {
        setThrottleRemove((prev: any) => [...prev, activeRemove]);
      }
    } else if (activeRemoveType === "Bolt-On") {
      if (boltonAdd.some((item: any) => item.code === activeRemove.code)) {
        setBoltonAdd((prev: any) =>
          prev.filter((item: any) => item.code !== activeRemove.code),
        );
      } else {
        setBoltonRemove((prev: any) => [...prev, activeRemove]);
      }
    }
    setShowConfirmRemove(false);
  };

  const [showAddFeature, setShowAddFeature] = useState(false);
  const [showAddBoltons, setShowAddBoltons] = useState(false);

  const addBoltons = (featuresToAdd: any) => {
    let newBoltonRemove = [...boltonRemove];
    let newBoltonAdd = [...boltonAdd];
    featuresToAdd.forEach((item: any) => {
      if (newBoltonRemove.some((bolton: any) => bolton.code === item.code)) {
        newBoltonRemove = newBoltonRemove.filter(
          (bolton: any) => bolton.code !== item.code,
        );
      } else if (
        !sub.features?.some((bolton: any) => bolton.code === item.code) &&
        !boltonAdd.some((bolton: any) => item.code === bolton.code)
      ) {
        newBoltonAdd = [...newBoltonAdd, item];
      }
    });
    setBoltonAdd(newBoltonAdd);
    setBoltonRemove(newBoltonRemove);
    setShowAddBoltons(false);
  };

  const addFeatures = (featuresToAdd: any) => {
    let newThrottleRemove = [...throttleRemove];
    let newThrottleAdd = [...throttleAdd];
    featuresToAdd.forEach((item: any) => {
      if (
        newThrottleRemove.some((throttle: any) => throttle.code === item.code)
      ) {
        newThrottleRemove = newThrottleRemove.filter(
          (throttle: any) => throttle.code !== item.code,
        );
      } else if (
        !sub.features?.some((throttle: any) => item.code === throttle.code) &&
        !throttleAdd.some((throttle: any) => item.code === throttle.code)
      ) {
        newThrottleAdd = [...newThrottleAdd, item];
      }
    });
    setThrottleAdd(newThrottleAdd);
    setThrottleRemove(newThrottleRemove);
    setShowAddFeature(false);
  };

  const handleAddChanges = (after = () => {}) => {
    ApiPostAuth("/accounts/addfeatures", {
      subscriber: sub.mdn,
      features: [...throttleAdd, ...boltonAdd, ...featureAdd].map(
        (item: any) => ({
          featureCode: item.code,
        }),
      ),
    })
      .then((response: any) => {
        after();
      })
      .catch((error: any) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  const handleRemoveChanges = (after = () => {}) => {
    ApiPostAuth("/accounts/removefeatures", {
      subscriber: sub.mdn,
      features: [...throttleRemove, ...boltonRemove, ...featureRemove].map(
        (item: any) => ({
          featureCode: item.code,
        }),
      ),
    })
      .then((response: any) => {
        after();
      })
      .catch((error: any) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  const showSuccess = () => {
    repopulate();
    setShow("");
    reset();
    setLoading(false);
    dispatch({
      type: "notify",
      payload: {
        error: false,
        message: "Features edited successfully",
      },
    });
  };

  const continueWithAddRemove = (tetherChanged = false) => {
    if ([...throttleRemove, ...boltonRemove, ...featureRemove].length !== 0) {
      if ([...throttleAdd, ...boltonAdd, ...featureAdd].length !== 0) {
        handleAddChanges(() => {
          handleRemoveChanges(showSuccess);
        });
      } else {
        handleRemoveChanges(showSuccess);
      }
    } else if ([...throttleAdd, ...boltonAdd, ...featureAdd].length !== 0) {
      handleAddChanges(showSuccess);
    } else {
      if (tetherChanged) {
        showSuccess();
      } else {
        setLoading(false);
        setShow("");
      }
    }
  };

  const handleSaveChanges = () => {
    setLoading(true);

    if (tetherStage !== tether) {
      ApiPostAuth(`/products/toggleTether/${id}/${sub.id}`)
        .then((response) => {
          continueWithAddRemove(true);
        })
        .catch((error) => {
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: error.response.data.message,
            },
          });
        });
    } else {
      continueWithAddRemove();
    }
  };

  ////////////////////////////////////////////////////
  // Tether

  const [tetherStage, setTetherStage] = useState(false);
  const [tether, setTether] = useState(false);

  const [tetherNotFound, setTetherNotFound] = useState(false);

  const handleToggleTether = () => {
    setTetherStage((prev: boolean) => !prev);
  };

  useEffect(() => {
    if (show && sub) {
      const isTether = tetherList.tether.includes(sub.product.soc);
      const isNoTether = tetherList.noTether.includes(sub.product.soc);

      if (!isTether && !isNoTether) {
        setTetherNotFound(true);
      } else if (isTether) {
        setTetherStage(true);
        setTether(true);
      } else if (isNoTether) {
        setTetherStage(false);
        setTether(false);
      }
    }
  }, [sub, show]);

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      cancelButton="Cancel"
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      proceed={handleSaveChanges}
      close={() => {
        setShow("");
        reset();
      }}
      loading={loading}
      fullSize
      title={
        <div style={{ textAlign: "start" }}>Manage Features & Bolt-Ons</div>
      }
    >
      <Modal
        saveButton={`Yes, remove ${activeRemoveType.toLowerCase()}`}
        cancelButton="No"
        image="/bulk_edit_confirm_graphic.svg"
        show={showConfirmRemove}
        proceed={handleRemoveActive}
        close={() => {
          setShowConfirmRemove(false);
        }}
        clearContainer
      >
        <h4 className={styles.confirmRemove}>
          Are you sure you want to remove {activeRemoveType} “
          {activeRemove && activeRemove.name}” from the account?
        </h4>
      </Modal>
      <AddFeaturesModal
        show={showAddFeature}
        setShow={setShowAddFeature}
        addFeatures={addFeatures}
        base={
          sub
            ? sub.features?.filter(
                (item: any) => item.classificationName === "throttle",
              )
            : []
        }
        throttle={features.filter(
          (item: any) => item.classificationName === "throttle",
        )}
        adding={throttleAdd}
        removing={throttleRemove}
      />
      <AddBoltonsModal
        show={showAddBoltons}
        setShow={setShowAddBoltons}
        addFeatures={addBoltons}
        base={
          sub
            ? sub.features?.filter(
                (item: any) =>
                  item.classificationName === "bolton" &&
                  !["API250VM", "AZIDX10GB", "APIDV10GB"].includes(item.code),
              )
            : []
        }
        boltons={features.filter(
          (item: any) =>
            item.classificationName === "bolton" &&
            !["API250VM", "AZIDX10GB", "APIDV10GB"].includes(item.code),
        )}
        adding={boltonAdd}
        removing={boltonRemove}
      />
      <div className={`${styles.main} ${loading && styles.loading}`}>
        <div className={`${styles.featureType} ${styles.throttle}`}>
          <div className={styles.featureName}>
            <div>Throttle</div>
            <button
              onClick={() => {
                setShowAddFeature(true);
              }}
              className={styles.addButton}
            >
              <Plus />
            </button>
          </div>
          <div className={styles.featureItems}>
            {getThrottleStage().length !== 0 ? (
              getThrottleStage().map((throttle: any) => (
                <div
                  key={`throttle-stage-${throttle.code}`}
                  className={styles.item}
                >
                  {throttle.name}
                  <div
                    className={styles.removeButton}
                    onClick={() => {
                      setActiveRemoveType("Throttle");
                      setActiveRemove(throttle);
                      setShowConfirmRemove(true);
                    }}
                  >
                    <XCircle />
                  </div>
                </div>
              ))
            ) : (
              <div className={styles.noneAddedText}>
                No Throttle tiers added. Click the “+” icon to add one.
              </div>
            )}
          </div>
        </div>
        <div className={`${styles.featureType} ${styles.boltons}`}>
          <div className={styles.featureName}>
            <div>Bolt-Ons</div>
            <button
              onClick={() => {
                setShowAddBoltons(true);
              }}
              className={styles.addButton}
            >
              <Plus />
            </button>
          </div>
          <div className={styles.featureItems}>
            {getBoltonStage().length !== 0 ? (
              getBoltonStage().map((bolton: any) => (
                <div
                  key={`bolton-stage-${bolton.code}`}
                  className={styles.item}
                >
                  {bolton.name}
                  <div
                    className={styles.removeButton}
                    onClick={() => {
                      setActiveRemoveType("Bolt-On");
                      setActiveRemove(bolton);
                      setShowConfirmRemove(true);
                    }}
                  >
                    <XCircle />
                  </div>
                </div>
              ))
            ) : (
              <div className={styles.noneAddedText}>
                No Bolt-Ons added. Click the “+” icon to add one.
              </div>
            )}
          </div>
        </div>
        <div className={`${styles.featureType} ${styles.features}`}>
          <div className={styles.featureName}>Features</div>
          <div className={styles.featureItems}>
            {features
              .filter((item: any) => item.classificationName === "feature")
              .map((item: any) => (
                <div
                  key={`feature-toggle-${item.code}`}
                  className={styles.featureToggleItem}
                >
                  <div className={styles.item}>{item.name}</div>
                  <div className={styles.toggleContainer}>
                    {getFeatureStage().some(
                      (feature: any) => item.code === feature.code,
                    )
                      ? "On"
                      : "Off"}
                    <Toggle
                      onChange={() => {
                        handleToggleFeature(item);
                      }}
                      on={getFeatureStage().some(
                        (feature: any) => item.code === feature.code,
                      )}
                      style={{ marginLeft: 8 }}
                    />
                  </div>
                </div>
              ))}
            {!tetherNotFound && (
              <div
                key={`feature-toggle-tether`}
                className={styles.featureToggleItem}
              >
                <div className={styles.item}>Tether</div>
                <div className={styles.toggleContainer}>
                  {tetherStage ? "On" : "Off"}
                  <Toggle
                    onChange={handleToggleTether}
                    on={tetherStage}
                    style={{ marginLeft: 8 }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default ManageFeatureModal;
