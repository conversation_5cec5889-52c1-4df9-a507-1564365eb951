import styles from "./coloured-select.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { useRef } from "react";
import Radio from "../Radio";
import { ChevronDown, Tick } from "../svgs";
import { Collapse, Fade } from "@mui/material";

const ColouredSelect = ({
  label,
  options,
  selected,
  onChange,
  disabled,
  readonly,
  error,
  id = null,
  status,
}: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const reset = () => {
    toggleMenu(false);
  };

  const convertStatus = (status: number) => {
    switch (status) {
      case 2:
        return "Open";
      case 3:
        return "Pending";
      case 4:
        return "Resolved";
      case 5:
        return "Closed";
      default:
        return status;
    }
  };

  return (
    <div className={`${styles.box} select coloured-select`}>
      <div className={styles.inputLabel}>{label}</div>
      <div
        ref={ref}
        className={`${styles.menuButton} ${error && styles.error} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        } ${disabled && styles.disabled}  ${
          styles[
            `coloured-${selected.toString().replaceAll(" ", "").toLowerCase()}`
          ]
        }`}
        onClick={(e) => {
          e.stopPropagation();
          if (!readonly) toggleMenu(true);
        }}
        style={{ cursor: readonly ? "auto" : "" }}
      >
        {selected === "" ? "Select Category" : convertStatus(selected)}

        {!readonly && (
          <div style={{ display: "flex", gap: "8px" }}>
            {error && (
              <Fade in={error ? true : false}>
                <img
                  src="/input_error.svg"
                  className={styles.errorIcon}
                  onMouseDown={(e) => {
                    e.preventDefault();
                  }}
                  style={{ right: 45 }}
                />
              </Fade>
            )}

            <ChevronDown />
          </div>
        )}
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        align="start"
        viewScroll="auto"
        position="auto"
      >
        {options.map((item: any) => (
          <MenuItem
            className={`${styles.menuItem} ${
              styles[
                `cat-${item.label.toString().replaceAll(" ", "").toLowerCase()}`
              ]
            } ${selected === item.key && styles.active}`}
            key={item.key}
            onClick={(e: any) => {
              onChange(item.key);
            }}
          >
            {/*<Radio
              checked={selected === item.key}
              onClick={(e: any) => {
                e.stopPropagation();
                onChange(item.key);
                reset();
              }}
            />*/}
            <span>{item.label}</span>
            {selected === item.key && <Tick />}
          </MenuItem>
        ))}
      </ControlledMenu>
      <Collapse in={error ? true : false}>
        <p className={styles.errorText} id={`${id}-error`}>
          {error || <br />}
        </p>
      </Collapse>
    </div>
  );
};

export default ColouredSelect;
