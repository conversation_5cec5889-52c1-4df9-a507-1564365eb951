@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  background: #f5f6fa;
  border-radius: 24px;
  border: 1px solid #d3d7e9;
  cursor: pointer;
  position: relative;
  transition: background 0.2s ease;
  aspect-ratio: 1;
  .content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    .name {
      margin-top: auto;
      font-size: 16px;
      line-height: 24px;
      color: #000;
      background-color: #fff;
      padding: 16px;
      width: 100%;
      border-radius: 0px 0px 24px 24px;
    }
  }
  &.dark {
    background: #000;
    &:hover {
      background: #1d1d1d;
    }
  }
  .img {
    width: 40%;
    transition: all 0.3s ease;
    margin-top: auto;
  }
  &:hover {
    background: $light-orange;
  }
}

.overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: all 0.4s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $off-white;
  background-color: $light-primary;
  font-size: 24px;
  font-weight: 500;
  z-index: 10;
  border-radius: 4px;
  &:hover {
    opacity: 1;
  }
}
