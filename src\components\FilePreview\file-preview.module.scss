@use "../../styles/theme.scss" as *;

.main {
  display: grid;
  grid-template-columns: 1fr auto 24px;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  font-size: 16px;
  line-height: 24px;
  .fileName {
    color: $placeholder;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 10px;
  }
  .fileSize {
    color: #666;
    margin-right: 16px;
  }
}

.deleteButton {
  padding: 0;
  border: none;
  background: none;
  color: $orange;
  cursor: pointer;
  &:hover {
    color: $dark-orange;
  }
  svg {
    vertical-align: middle;
  }
}
