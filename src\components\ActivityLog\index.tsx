import StatusPill from "../StatusPill";
import { ArrowRight } from "../svgs";
import { formatDateWithTime } from "../utils/formatDate";
import styles from "./activity-log.module.scss";

const ActivityLog = ({ log }: any) => {
  return (
    <div className={styles.main}>
      <div className={styles.topBar}>
        <div className={styles.action}>
          <div style={{ marginRight: 10 }}>{log.agent}</div>
          <div>{log.data.display}</div>
        </div>
        <div className={styles.data}>
          <div style={{ marginRight: 24 }}>IP: ***********</div>
          <div>
            <b>{formatDateWithTime(log.date)}</b>
          </div>
        </div>
      </div>
      {log.data.extra && (
        <div className={styles.graphic}>
          {log.data.extra.old}
          <ArrowRight />
          {log.data.extra.new}
        </div>
      )}
    </div>
  );
};

export default ActivityLog;
