import Button from "../Button";
import Modal from "../Modal";
import styles from "./edit-subscription-modal.module.scss";

const EditSubscriptionModal = ({ show, setShow, topups }: any) => {
  return (
    <Modal
      show={show}
      close={() => {
        setShow("");
      }}
      image="/bulk_edit_confirm_graphic.svg"
      fullSize
    >
      <div className={styles.main}>
        <h3>Edit Subscription</h3>
        <div className={styles.buttons}>
          <Button
            onClick={() => {
              setShow("change-subscription");
            }}
            style={{ width: "100%", marginBottom: 12 }}
            color="secondary"
          >
            Change Subscription
          </Button>
          {topups.length > 0 && (
            <Button
              onClick={() => {
                setShow("add-top-up");
              }}
              style={{ width: "100%", marginBottom: 12 }}
              color="secondary"
            >
              Add Top ups
            </Button>
          )}
          <Button
            onClick={() => {
              setShow("change-mdn");
            }}
            style={{ width: "100%", marginBottom: 12 }}
            color="secondary"
          >
            Change MDN
          </Button>
          <Button
            onClick={() => {
              setShow("nickname");
            }}
            style={{ width: "100%", marginBottom: 12 }}
            color="secondary"
          >
            Manage Device Nickname
          </Button>
          <Button
            onClick={() => {
              setShow("change-imei");
            }}
            style={{ width: "100%", marginBottom: 12 }}
            color="secondary"
          >
            Change IMEI
          </Button>
          <Button
            onClick={() => {
              setShow("change-iccid");
            }}
            style={{ width: "100%", marginBottom: 12 }}
            color="secondary"
          >
            Change ICCID
          </Button>
          {/*<Button
            onClick={() => {
              setShow("change-bill-cycle");
            }}
            style={{ width: "100%" }}
            color="secondary"
          >
            Change Bill Cycle
          </Button>*/}
        </div>
      </div>
    </Modal>
  );
};

export default EditSubscriptionModal;
