import { useEffect, useState } from "react";
import styles from "../../styles/notifications.module.scss";
import SearchSection from "../../components/SearchSection";
import { ApiGetNoAuth } from "../api/api";
import { useParams } from "react-router-dom";
import NotificationSkeleton from "../../components/NotificationSkeleton";
import ThrottleNotification from "../../components/ThrottleNotification";
import { useDispatch } from "react-redux";
import OrderNotification from "../../components/OrderNotification";

const Notifications = () => {
  const dispatch = useDispatch();
  const [initialLoading, setInitialLoading] = useState(true);
  const { mvnoId } = useParams();

  const [notifications, setNotifications] = useState([] as any);

  const [filteredNotifications, setFilteredNotifications] = useState([] as any);

  const [error, setError] = useState(false);

  const repopulate = () => {
    ApiGetNoAuth(`/notifications/${mvnoId}`)
      .then((response) => {
        console.log(response.data);
        setNotifications(response.data.sort(sortByDate).sort(sortByRead));
        setFilteredNotifications(
          response.data.sort(sortByDate).sort(sortByRead),
        );
        setInitialLoading(false);
        dispatch({
          type: "set",
          throttleNotifications: response.data,
        });
      })
      .catch((error) => {
        setInitialLoading(false);
        setError(true);
      });
  };

  useEffect(repopulate, []);

  const [currentPage, setCurrentPage] = useState(1);
  const [queryDisplay, setQueryDisplay] = useState("");

  const sortByRead = (a: any, b: any) => {
    if (a.read && !b.read) {
      return 1;
    } else if (!a.read && b.read) {
      return -1;
    } else {
      return 0;
    }
  };

  const sortByDate = (a: any, b: any) => {
    let aTime = new Date(a.createdAt).getTime();
    let bTime = new Date(b.createdAt).getTime();

    if (!aTime || !bTime) {
      return 0;
    } else {
      return bTime - aTime;
    }
  };

  return (
    <div className={styles.main}>
      <SearchSection
        data={notifications}
        setFilteredData={setFilteredNotifications}
        setQueryDisplay={setQueryDisplay}
        placeholder="Search by Subscriber Name or Throttle Type"
        id="subscriber-search"
        setCurrentPage={setCurrentPage}
      />
      <div className={styles.titleBar}>
        <h3>Notifications</h3>
      </div>
      <div className={styles.mainTile}>
        <div className={styles.container}>
          {initialLoading ? (
            Array.from({ length: 10 }).map(() => <NotificationSkeleton />)
          ) : error ? (
            <div className={styles.loadingError}>
              <img src="/error_robot.svg" />
              <div>
                <h3>An unexpected error occurred</h3>
                <p>Please try again later</p>
              </div>
            </div>
          ) : filteredNotifications.length > 0 ? (
            filteredNotifications.map((item: any) => {
              if (item.notificationType === "Order") {
                return (
                  <OrderNotification
                    item={item}
                    repopulate={repopulate}
                    key={`notification-${item.id}`}
                  />
                );
              } else {
                return (
                  <ThrottleNotification
                    item={item}
                    repopulate={repopulate}
                    key={`notification-${item.id}`}
                  />
                );
              }
            })
          ) : notifications.length > 0 ? (
            <div className={styles.none}>
              <img src="/none_found.svg" className={styles.noneImage} />
              <span className={styles.noneText}>
                Nothing matching "{queryDisplay}"
              </span>
            </div>
          ) : (
            <div className={styles.none}>
              <img src="/none_found.svg" className={styles.noneImage} />
              <span className={styles.noneText}>
                There are no notifications
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Notifications;
