@use "./theme.scss" as *;

.main {
  width: 100%;
  background: #f1f1f1;
  padding: 50px 40px;
}
.titleBar {
  h3 {
    margin-top: -7px;
    margin-bottom: 24px;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
  }
}
.selectionWrapper {
  display: flex;
  align-items: center;
}

.selection {
  height: 47px;
  border-radius: 1000px;
  color: $black;
  font-size: 14px;
  font-weight: 600;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    color: $dark-orange;
  }
  span {
    position: relative;
    z-index: 6;
  }
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1000px;
  background-color: $light-orange;
  z-index: 5;
  left: 0;
}

.activeSelection {
  cursor: auto;
  &:hover {
    color: $black;
  }
}
.mainTile {
  width: 100%;
  //height: calc(100vh - 236px);
  background: #fff;
  border-radius: 24px;
  padding: 40px 0px;
  padding-right: 24px;
}
.faq {
  //height: calc(100% - 71px);
  max-height: 500px;
  overflow-y: scroll;
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: flex-start;
  flex-shrink: 0;
  padding: 12px 40px;
  padding-right: 24px;
  margin: 12px 0px;
}
.guides {
  //height: calc(100% - 71px);
  max-height: 500px;
  padding: 12px 40px;
  padding-right: 24px;
  margin: 12px 0px;
  display: grid;
  align-items: start;
  width: 100%;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: 254px;
  grid-row-gap: 24px;
  grid-column-gap: 24px;
}

.none {
  display: grid;
  align-items: center;
  justify-items: center;
  width: 100%;
  margin: 50px 0;
  .noneImage,
  .noneText {
    grid-area: 1 / 1 / 2 / 2;
  }
  .noneText {
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
    color: $black;
  }
}
