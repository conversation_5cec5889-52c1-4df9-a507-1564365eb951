import styles from "../../styles/login.module.scss";
import { Input } from "../../components/Input";
import Button from "../../components/Button";
import { useEffect, useState } from "react";
import jwt_decode from "jwt-decode";
import { validateAll } from "indicative/validator";
import { useDispatch } from "react-redux";
import $ from "jquery";
import { Link, useNavigate } from "react-router-dom";
import { ApiPost } from "../api/api";

const rules = {
  password: "required|min:6",
  confirmPassword: "required",
};

const messages = {
  "password.required": "Please enter a new password",
  "password.min": "Password must be at least 6 characters",
  "confirmPassword.required": "Please confirm new password",
};

const ResetPassword = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [token, setToken] = useState("" as any);

  const [tokenError, setTokenError] = useState(false);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search) as any;
    let tokenStore;
    for (const param of params) {
      tokenStore = param[0];
    }
    setToken(tokenStore);
    try {
      if (tokenStore) {
        const decoded: any = jwt_decode(tokenStore);
      }
    } catch (e) {
      setTokenError(true);
    }
  }, []);

  const [loading, setLoading] = useState(false);

  const [passwordData, setPasswordData] = useState({
    password: "",
    confirmPassword: "",
    errors: {
      password: "",
      confirmPassword: "",
    },
  });

  /**********  Handle data change    ********/

  const handleChange = (prop: string, event: any) => {
    setPasswordData({
      ...passwordData,
      [prop]: event.target.value,
      errors: {
        ...passwordData.errors,
        [prop]: "",
      },
    });
  };

  const clear = (prop: string) => {
    setPasswordData({
      ...passwordData,
      [prop]: "",
      errors: {
        ...passwordData.errors,
        [prop]: "",
      },
    });
  };

  const resetPassword = () => {
    const data = {
      password: passwordData.password,
      confirmPassword: passwordData.confirmPassword,
    };

    validateAll(data, rules, messages)
      .then((response) => {
        if (data.password !== data.confirmPassword) {
          setPasswordData({
            ...passwordData,
            errors: {
              password: "",
              confirmPassword: "Passwords do not match",
            },
          });
        } else {
          setLoading(true);
          ApiPost("/users/resetpassword", {
            resetToken: token,
            password: data.password,
            confirmPassword: data.confirmPassword,
          })
            .then((response) => {
              navigate("/login");
              dispatch({
                type: "set",
                resetMessage: response.data.message,
                closeResetMessage: false,
              });
            })
            .catch((error) => {
              setLoading(false);
              dispatch({
                type: "notify",
                payload: {
                  error: true,
                  message:
                    error.response.data.message ||
                    "Something went wrong, please try again",
                },
              });
            });
        }
      })
      .catch((errors) => {
        let formattedErrors = {} as any;
        errors.forEach((error: any) => {
          formattedErrors[error.field] = error.message;
        });
        setPasswordData({
          ...passwordData,
          errors: formattedErrors,
        });

        if (!data.password) {
          $("#new-password").trigger("focus");
        } else if (!data.confirmPassword) {
          $("#confirm-new-password").trigger("focus");
        }
      });
  };

  return (
    <div className={styles.container}>
      <div className={styles.main}>
        <Link to="/login">
          <img src="/Logo.png" className={styles.logo} />
        </Link>
        <div className={styles.formContainer}>
          <div className={styles.form}>
            <h2>Reset Password</h2>
            <div style={{ maxWidth: 350, width: "100%" }}>
              <Input
                label="New Password"
                placeholder="Enter a new password"
                password
                id="new-password"
                value={passwordData.password}
                error={passwordData.errors.password}
                clear={() => {
                  clear("password");
                }}
                onChange={(e: any) => {
                  handleChange("password", e);
                }}
                onKeyDown={resetPassword}
                disabled={loading}
              />
              <Input
                label="Confirm New Password"
                placeholder="Confirm your new password"
                password
                id="confirm-new-password"
                value={passwordData.confirmPassword}
                error={passwordData.errors.confirmPassword}
                clear={() => {
                  clear("confirmPassword");
                }}
                onChange={(e: any) => {
                  handleChange("confirmPassword", e);
                }}
                onKeyDown={resetPassword}
                disabled={loading}
              />
            </div>
            <Button
              style={{ marginTop: 30 }}
              color="primary"
              loading={loading}
              onClick={resetPassword}
            >
              Reset Password
            </Button>
          </div>
        </div>
      </div>
      <img src="/reset_password_graphic.svg" className={styles.graphic} />
    </div>
  );
};

export default ResetPassword;
