@use "../../styles/theme.scss" as *;

.container {
  position: relative;
  width: 24px;
  height: 24px;
  padding: 3.75px;
}
.box {
  border: 2px solid #9b9b9b;
  border-radius: 2px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.1s ease;
  width: 100%;
  height: 100%;
  color: white;
  &.disabled {
    cursor: auto;
    &.indeterminate {
      &:hover {
        background: $faded-orange;
        border-color: $faded-orange;
        .tick {
          opacity: 1;
        }
      }
    }
    &:hover {
      background: none;
      border-color: #9b9b9b;
      .tick {
        opacity: 0;
      }
    }
  }
  &.indeterminate {
    background: $faded-orange;
    border-color: $faded-orange;
    .tick {
      opacity: 1;
    }
  }
  &:hover {
    background: $faded-orange;
    border-color: $faded-orange;
    .tick {
      opacity: 1;
    }
  }
  svg {
    width: 100%;
  }
}
.checked {
  background: $orange;
  border-color: $orange;
  &:hover {
    border-color: $orange;
    background: $orange;
  }
}
.checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.tick {
  opacity: 0;
  transition: all 0.1s ease;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  &.tickChecked {
    opacity: 1;
  }
}
