export const ordersFields = [
  {
    label: "Order Number",
    labelStr: "Order Number",
    key: "orderNumber",
  },
  {
    label: "Date & Time",
    labelStr: "Date & Time",
    key: "dateAndTime",
  },
  {
    label: "Subscriber",
    labelStr: "Subscriber",
    key: "subscriberName",
  },
  {
    label: "SIM Type",
    labelStr: "SIM Type",
    key: "simType",
  },
  {
    label: "Product",
    labelStr: "Product",
    key: "offerName",
  },
  {
    label: "IMEI",
    labelStr: "IMEI",
    key: "imei",
  },
  {
    label: "ICCID",
    labelStr: "ICCID",
    key: "iccid",
  },
  {
    label: "Status",
    labelStr: "Status",
    key: "status",
  },
];

const createOrder = () => {
  const iccid = ["12345678912345", null][Math.floor(Math.random() * 2)];
  return {
    orderNumber: "123456",
    time: new Date(),
    subscriber: "<PERSON>" + " " + "<PERSON>",
    simType: ["eSIM", "SIM"][Math.floor(Math.random() * 2)],
    product: "Airespring Throttle Data",
    imei: "123456789123456789",
    iccid: iccid,
    status:
      iccid === null
        ? "ICCID Required"
        : ["BAN Change", "Ready"][Math.floor(Math.random() * 2)],
  };
};

export const getOrders = () => {
  let orders = [] as any;
  Array.from({ length: 100 }).forEach((i: any) => {
    orders.push(createOrder());
  });
  return orders;
};
