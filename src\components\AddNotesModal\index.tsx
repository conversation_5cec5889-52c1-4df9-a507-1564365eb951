import styles from "./add-notes.module.scss";
import Modal from "../Modal";
import { useState } from "react";
import TextArea from "../TextArea";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import { validateAll } from "indicative/validator";
import { ApiPostAuth } from "../../pages/api/api";
import { useSelector, useDispatch } from "react-redux";

const fields = ["note"];
const rules = getRules(fields);
const messages = getMessages(fields);

const AddNotesModal = ({ show, setShow, ticketId, repopulate }: any) => {
  const dispatch = useDispatch();
  const reset = () => {
    setData(createStateObject(fields));
    setLoading(false);
  };

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(createStateObject(fields));

  const { userInfo } = useSelector((state: any) => state);

  const createNote = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPostAuth("/tickets/add/note", {
          ticketId: ticketId,
          agentId: 153004319588,
          text: `${userInfo.firstName} ${userInfo.lastName} || ${data.note}`,
          makePrivate: false,
        })
          .then((response) => {
            repopulate();
            setShow(false);
            setTimeout(reset, 300);
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: "Your note was added successfully!",
              },
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  return (
    <Modal
      saveButton={<>Add Note</>}
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={createNote}
      close={() => {
        setShow(false);
        reset();
      }}
      onClose={() => {
        setShow(false);
        reset();
      }}
      loading={loading}
    >
      <div className={`${styles.main} `}>
        <h3>Add Note </h3>
        <TextArea
          disabled={loading}
          value={data.note}
          label={labels.note}
          placeholder={placeholders.note}
          onChange={(e: any) => {
            handleInputChange("note", e, data, setData);
          }}
          clear={() => {
            clearInput("note", setData);
          }}
          error={data.errors.note}
          white
        />
      </div>
    </Modal>
  );
};

export default AddNotesModal;
