import { useEffect, useState } from "react";
import styles from "./check-otp.module.scss";
import Button from "../Button";
import OtpInput from "../OtpInput";
import { ApiPost } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";

const CheckOtp = ({ loginInfo }: any) => {
  const [otp, setOtp] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const codeLength = 5;

  const [loading, setLoading] = useState(false);

  const handleSubmit = () => {
    setLoading(true);
    ApiPost("/users/login", {
      ...loginInfo,
      entered2faCode: otp,
    })
      .then((response) => {
        localStorage.setItem("token", response.data.auth);
        const { message, auth, active, ...userInfo } = response.data;
        if (!userInfo.brandLogo) {
          userInfo.brandLogo = "";
        }
        dispatch({
          type: "set",
          isLoggedIn: true,
          userInfo: userInfo,
        });
        localStorage.setItem("crmUserInfo", JSON.stringify(userInfo));
        if (response.data.roleName === "mvne") {
          dispatch({
            type: "set",
            resetMessage: response.data.message,
            closeResetMessage: false,
          });
          navigate("/select-project");
        } else {
          dispatch({
            type: "set",
            loginMessage: response.data.message,
            closeLoginMessage: false,
          });
          navigate(`/${userInfo.mvnoId}/subscriber-management`);
        }
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  const [timeToReset, setTimeToReset] = useState(30);

  const [timer, setTimer] = useState(null as any);

  const setTimeToResetCounter = () => {
    const clearTimer = () => {
      clearInterval(timer);
    };
    clearTimer();
    setTimer(
      setInterval(() => {
        setTimeToReset((prev) => {
          if (prev > 0) {
            return prev - 1;
          } else {
            clearTimer();
            return 0;
          }
        });
      }, 1000)
    );
  };

  useEffect(setTimeToResetCounter, []);

  const resendOtp = () => {
    setLoading(true);
    ApiPost("/users/sendotp", loginInfo)
      .then((response) => {
        setOtp("");
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        setTimeToReset(30);
        setTimeToResetCounter();
        setLoading(false);
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: "Something went wrong, please try again",
          },
        });
        setLoading(false);
      });
  };

  return (
    <div className={styles.form}>
      <h2>Login</h2>
      <p className={styles.tag}>
        We’ve sent a code to your email. Please enter it below.
      </p>
      <OtpInput
        handleSuccess={handleSubmit}
        codeLength={codeLength}
        value={otp}
        setValue={setOtp}
      />
      <div className={styles.notReceived}>
        Code not received?{" "}
        {timeToReset === 0 ? (
          <button
            disabled={loading}
            onClick={resendOtp}
            className={styles.resend}
          >
            Re-send code
          </button>
        ) : (
          <div className={styles.resetTime}>
            Re-send code in <div style={{ width: 30 }}>{timeToReset}</div>{" "}
            seconds
          </div>
        )}
      </div>
      <Button
        style={{ marginTop: 34 }}
        loading={loading}
        disabled={otp.length !== codeLength}
        color="primary"
        onClick={handleSubmit}
      >
        Log in
      </Button>
    </div>
  );
};

export default CheckOtp;
