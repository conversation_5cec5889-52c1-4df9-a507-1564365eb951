@use "./theme.scss" as *;

.main {
  width: 100%;
  background: #f1f1f1;
  padding: 50px 40px;
}

.logs {
  overflow-x: auto;

  td,
  th {
    white-space: nowrap;
  }
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 55px;

  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }

  h3 {
    width: 100%;
    text-align: center;
  }
}

.skeletonContainer {
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;

  .breadcrumbs {
    a {
      color: $primary;
      text-decoration: none;
      &:hover {
        color: $orange;
      }
    }

    span {
      font-weight: 700;
    }
  }

  .actionButtons {
    display: flex;
    align-items: center;
    gap: 16px;
  }
}

.container {
  border-radius: 24px;
  background: #fff;
  margin-top: 16px;
  padding: 24px;

  .detailTop {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .detailDates {
    display: flex;
    gap: 24px;
    color: #6b6b6b;
    font-size: 14px;
  }

  h1,
  h2 {
    font-size: 18px;
    font-weight: 700px;
    margin-top: 16px;
  }
}

.detailItemCardsContainer {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

.detailItemCard {
  background: #f6f6f6;
  width: 200px;
  padding: 16px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;

  span {
    font-size: 12px;
  }

  h3 {
    font-size: 18px;
    font-weight: 600;
  }
}

.activityLog {
  table {
    margin-top: 16px;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 6px;
    font-size: 14px;
  }

  thead {
    tr {
      height: 40px;
      vertical-align: middle;
    }

    th {
      border-bottom: 1px solid #0000141f;

      &:first-child {
        padding-left: 16px;
      }
    }
  }

  th {
    text-align: left;
  }

  tbody {
    tr {
      height: 60px;
      vertical-align: middle;
      border-radius: 8px;

      &:nth-child(2n + 1) {
        background: #f5f5f5;
      }
    }

    td {
      &:first-child {
        padding-left: 16px;
        border-radius: 8px 0 0 8px;
      }

      &:last-child {
        border-radius: 0 8px 8px 0;
      }
    }
  }

  .logTransitionCell {
    span {
      font-size: 12px;
    }
  }

  .logValueTransitionContainer {
    display: flex;
    gap: 16px;
    align-items: center;

    p {
      font-size: 14px;
    }
  }
}

.paginationContainer {
  display: flex;
  justify-content: end;
  width: 100%;
  border-top: 1px solid #0000141f;
  margin-top: 4px;
  padding-top: 24px;
}

.loadingError {
  background: #fff;
  width: 100%;
  padding: 50px;
  border-radius: 24px;
  display: flex;
  align-items: center;
}
