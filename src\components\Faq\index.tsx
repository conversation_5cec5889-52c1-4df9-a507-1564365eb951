import { CaretDown } from "../svgs";
import styles from "./faq.module.scss";
import { Accordion, AccordionDetails, AccordionSummary } from "@mui/material";

const Faq = ({ item }: any) => {
  return (
    <Accordion
      style={{
        borderRadius: "8px",
        backgroundColor: "#F2F2F2",
        width: "100%",
        padding: "12px 24px",
        marginBottom: 12,
      }}
    >
      <AccordionSummary
        expandIcon={<CaretDown />}
        aria-controls="panel1a-content"
        id="panel1a-header"
        style={{ padding: 0 }}
      >
        <div className={styles.question}>{item.question}</div>
      </AccordionSummary>
      <AccordionDetails style={{ padding: "0 0 12px 0" }}>
        {typeof item.answer === "string" ? (
          <div
            className={styles.answer}
            dangerouslySetInnerHTML={{ __html: item.answer }}
          />
        ) : (
          <div className={styles.answer}>{item.answer}</div>
        )}
      </AccordionDetails>
    </Accordion>
  );
};

export default Faq;
