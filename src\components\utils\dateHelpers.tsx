import moment from "moment";

export const zeroPad = (value: number) => {
  return value.toString().padStart(2, "0");
};

export const isADate = (date: any) => {
  return moment(date, moment.ISO_8601, true).isValid();
};

// Get number of days in month
export const getMonthDays = (month: number, year: number) => {
  const months30 = [4, 6, 9, 11];
  const leapYear = year % 4 === 0;
  return month === 2
    ? leapYear
      ? 29
      : 28
    : months30.includes(month)
      ? 30
      : 31;
};

// 1 = monday, 7 = sun
export const getMonthFirstDay = (month: number, year: number) => {
  let sundayBase = new Date(`${year}/${zeroPad(month)}/01`).getDay();
  if (sundayBase === 0) {
    return 7;
  } else {
    return sundayBase;
  }
};

// 1 = monday, 7 = sun
export const getMonthLastDay = (month: number, year: number) => {
  let sundayBase = new Date(
    `${year}/${zeroPad(getMonthDays(month, year))}/01`,
  ).getDay();
  if (sundayBase === 0) {
    return 7;
  } else {
    return sundayBase;
  }
};

// Get previous month padding
export const getPreviousDate = (day: number, month: number, year: number) => {
  const dateYear = month === 1 ? year - 1 : year;
  const dateMonth = month === 1 ? 12 : month - 1;
  return new Date(`${dateYear}/${zeroPad(dateMonth)}/${zeroPad(day)}`);
};

// Get next month padding
export const getNextDate = (day: number, month: number, year: number) => {
  const dateYear = month === 12 ? year + 1 : year;
  const dateMonth = month === 12 ? 1 : month + 1;
  return new Date(`${dateYear}/${zeroPad(dateMonth)}/${zeroPad(day)}`);
};

// Return month array with beginning and end of prev and next months to fill out to 42 days
export const getMonthArray = (
  mainMonth: any,
  numberOfDays: number,
  firstDay: number,
  month: number,
  year: number,
) => {
  const lastMonth =
    month === 1 ? getMonthDays(12, year - 1) : getMonthDays(month - 1, year);

  const prevPadLength = firstDay - 1;
  const prevMonthPad = Array.from({ length: prevPadLength }, (v, i) => ({
    type: "pad",
    value: lastMonth - (prevPadLength - (i + 1)),
    date: getPreviousDate(lastMonth - (prevPadLength - (i + 1)), month, year),
  }));

  const totalMonthPlusPrev = prevPadLength + numberOfDays;
  const nextPadLength = 42 - totalMonthPlusPrev;

  const nextMonthPad = Array.from({ length: nextPadLength }, (v, i) => ({
    type: "pad",
    value: i + 1,
    date: getNextDate(i + 1, month, year),
  }));

  const totalArray = [...prevMonthPad, ...mainMonth, ...nextMonthPad];

  return totalArray;
};

// Format date to dd/mm/yyyy
export const formatDate = (date: Date) => {
  const day = zeroPad(date.getDate());
  const month = zeroPad(date.getMonth() + 1);
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

// Is date today's date
export const isDateNow = (date: any) => {
  const now = new Date();
  const testDate = typeof date === "string" ? new Date(date) : date;
  return (
    testDate.getDate() === now.getDate() &&
    now.getMonth() === testDate.getMonth() &&
    testDate.getFullYear() === now.getFullYear()
  );
};

// Is date in past
export const dateInPast = (date: any) => {
  const now = new Date();
  if (date.getFullYear() < now.getFullYear()) {
    return true;
  } else if (date.getFullYear() === now.getFullYear()) {
    if (date.getMonth() < now.getMonth()) {
      return true;
    } else if (date.getMonth() === now.getMonth()) {
      if (date.getDate() < now.getDate()) {
        return true;
      } else if (date.getDate() === now.getDate()) {
        return false;
      } else {
        return false;
      }
    } else {
      return false;
    }
  } else {
    return false;
  }
};

export function isAtLeastNextDay(referenceDate: Date, dateToCheck: Date) {
  const ref = moment(referenceDate).startOf("day");
  const check = moment(dateToCheck).startOf("day");

  return check.diff(ref, "days") >= 1;
}

/**
 * format to YYYY-MM-DD
 */
export const formatDateForApi = (date: Date | null) => {
  if (!date) return null;

  const year = date.getFullYear();
  const month = zeroPad(date.getMonth() + 1);
  const day = zeroPad(date.getDate());
  return `${year}-${month}-${day}`;
};
