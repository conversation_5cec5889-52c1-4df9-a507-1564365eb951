import { useEffect, useState } from "react";
import { Input } from "../Input";
import Modal from "../Modal";
import SelectInput from "../SelectInput";
import styles from "./edit-fee-modal.module.scss";
import { ApiPut } from "../../pages/api/api";
import { useDispatch } from "react-redux";

const EditFeeModal = ({ show, setShow, fee, refreshData }: any) => {
  const [feeType, setFeeType] = useState<any>();
  const [feeName, setFeeName] = useState<any>();
  const [feeAmount, setFeeAmount] = useState<any>();
  const [feeStatus, setFeeStatus] = useState<boolean>();

  const [editing, setEditing] = useState(false);

  const dispatch = useDispatch();

  const editFee = () => {
    setEditing(true);
    ApiPut(`/fees/${fee.id}`, {
      name: feeName,
      amount: feeAmount,
      type: feeType,
      status: feeStatus,
    }).then(() => {
      setEditing(false);
      setShow(false);
      dispatch({
        type: "notify",
        payload: {
          error: false,
          heading: "Fee edited successfully",
        },
      });
      refreshData();
    }).catch;
  };

  useEffect(() => {
    if (fee && show) {
      setFeeType(fee.type);
      setFeeName(fee.name);
      setFeeAmount(fee.amount);
      setFeeStatus(fee.status);
    }
  }, [fee, show]);

  return (
    <Modal
      saveButton={<>Edit fee</>}
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={editFee}
      loading={editing}
      close={() => {
        setShow(false);
      }}
      fullsize
      title="Edit Fee"
    >
      <div className={`${styles.main} normal-select-input`}>
        <SelectInput
          placeholder="Fee Type"
          selected={feeType}
          options={["Regulatory", "Activation"]}
          onChange={(value: any) => {
            setFeeType(value);
          }}
        />
        <div style={{ height: 12 }}></div>
        <SelectInput
          placeholder="Fee Status"
          selected={feeStatus ? "Active" : "Inactive"}
          options={["Active", "Inactive"]}
          onChange={(value: any) => {
            setFeeStatus(value === "Active" ? true : false);
          }}
        />
        <div style={{ height: 12 }}></div>
        <Input
          label="Fee Name"
          value={feeName}
          onChange={(e: any) => setFeeName(e.target.value)}
        />
        <Input
          label="Fee Amount"
          number
          value={feeAmount}
          onChange={(e: any) => setFeeAmount(e.target.value)}
        />
      </div>
    </Modal>
  );
};

export default EditFeeModal;
