import styles from "./priority.module.scss";

const Priority = ({ priority, small }: any) => {
  return (
    <div className={`${styles.main} ${small && styles.small}`}>
      {priority === "Urgent" ? (
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="8" cy="8" r="8" fill="#EA3D5C" />
        </svg>
      ) : priority === "High" ? (
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="8" cy="8" r="8" fill="#FBCA90" />
          <path
            d="M13.6569 13.6571C14.7757 12.5383 15.5376 11.1128 15.8463 9.56095C16.155 8.00911 15.9965 6.40057 15.391 4.93876C14.7855 3.47696 13.7602 2.22753 12.4446 1.34847C11.129 0.469424 9.58225 0.00023187 8 0.00023188C6.41775 0.000231889 4.87103 0.469424 3.55544 1.34847C2.23984 2.22753 1.21446 3.47696 0.608964 4.93876C0.0034628 6.40057 -0.154964 8.00911 0.153718 9.56095C0.4624 11.1128 1.22433 12.5383 2.34315 13.6571L8 8.00023L13.6569 13.6571Z"
            fill="#F2A446"
          />
        </svg>
      ) : priority === "Medium" ? (
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="8" cy="8" r="8" fill="#F8EC7E" />
          <path
            d="M16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 1.60186e-07 8 0C5.87827 -1.60186e-07 3.84344 0.842854 2.34315 2.34315C0.842855 3.84344 3.20373e-07 5.87827 0 8L8 8L16 8Z"
            fill="#EED922"
          />
        </svg>
      ) : (
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="8" cy="8" r="8" fill="#CBCAF9" />
          <path
            d="M13.6568 2.34315C12.914 1.60028 12.0321 1.011 11.0615 0.608964C10.0909 0.206926 9.05056 1.13953e-07 7.99999 1.36914e-07C6.94941 1.59875e-07 5.90912 0.206926 4.93852 0.608964C3.96792 1.011 3.086 1.60028 2.34313 2.34315L7.99999 8L13.6568 2.34315Z"
            fill="#6361DC"
          />
        </svg>
      )}
      {priority}
    </div>
  );
};

export default Priority;
