import { useState } from "react";
import Attachment from "../Attachment";
import Button from "../Button";
import ViewAttachmentsModal from "../ViewAttachmentsModal";
import { Plus, Upload } from "../svgs";
import styles from "./attachments-tile.module.scss";
import { useRef } from "react";

const AttachmentsTile = ({ sidebar }: any) => {
  const attachment = {
    name: "Filename.pdf",
    date: new Date(),
    size: 328704,
  };

  const list = Array.from({ length: 10 }).map(() => attachment);

  const fileInput = useRef(null);

  const handleFileUpload = () => {
    (fileInput.current as any).click();
  };

  const [showAll, setShowAll] = useState(false);

  return (
    <>
      <ViewAttachmentsModal show={showAll} setShow={setShowAll} data={list} />
      <div className={styles.main}>
        <div className={styles.title}>
          <div className={styles.leftContainer}>
            <h4>Attachments</h4>
            <p>
              {list.length} file{list.length !== 1 ? "s" : ""}
            </p>
            {sidebar && !!list.length && (
              <Button
                style={{
                  padding: 0,
                  height: 24,
                  fontSize: 14,
                  marginLeft: 16,
                }}
                color="quaternary"
                onClick={() => {
                  setShowAll(true);
                }}
              >
                See all
              </Button>
            )}
          </div>
          <Button
            style={{
              padding: 0,
              height: 24,
              fontSize: 14,
            }}
            color="quaternary"
            onClick={handleFileUpload}
          >
            <Upload />
            Upload file
          </Button>
          <input
            type="file"
            multiple
            ref={fileInput}
            style={{ display: "none" }}
          />
        </div>
        <div
          className={`${styles.attachmentsContainer} ${
            sidebar && styles.sidebar
          }`}
        >
          {list.length > 0 ? (
            sidebar ? (
              list.slice(0, 4).map((item) => <Attachment data={item} />)
            ) : (
              list.map((item) => <Attachment data={item} />)
            )
          ) : (
            <div className={styles.noneFound}>
              <img src="/none_found.svg" />
              <h3>No attachments</h3>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default AttachmentsTile;
