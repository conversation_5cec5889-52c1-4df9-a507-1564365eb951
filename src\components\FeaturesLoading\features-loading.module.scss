@use "../../styles/theme.scss" as *;

.name {
  height: 21px;
  width: 50px;
  background-color: $skeleton;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  margin-bottom: 5px;
}

.container {
  margin-bottom: 16px;
  &:last-of-type {
    margin-bottom: 0px;
  }
}

.flex {
  display: flex;
  flex-wrap: wrap;
}

.data {
  height: 29px;
  background-color: $skeleton;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
  margin: 0 8px 8px 0;
}
