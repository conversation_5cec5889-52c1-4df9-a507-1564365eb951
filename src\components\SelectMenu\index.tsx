import styles from "./select.module.scss";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { useRef } from "react";

const Select = ({ options, disabled, label, align = "center" }: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  return (
    <div className={`${styles.box} select`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${disabled && styles.disabled}`}
        onClick={(e: any) => {
          e.stopPropagation();
          if (menuProps.state === "closing") {
            toggleMenu(false);
          } else {
            toggleMenu(true);
          }
        }}
      >
        {label}
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        align={align}
        viewScroll="initial"
        onItemClick={(e: any) => (e.stopPropagation = true)}
      >
        {options.map((item: any) => (
          <MenuItem className={styles.menuItem} key={item.label}>
            <div
              onClick={(e) => {
                item.onClick();
                e.stopPropagation();
              }}
            >
              {item.label}
            </div>
          </MenuItem>
        ))}
      </ControlledMenu>
    </div>
  );
};

export default Select;
