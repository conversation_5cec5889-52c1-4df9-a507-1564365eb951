@use "../../styles/theme.scss" as *;

.panel {
  background-color: #fff;
  border-radius: 24px;
  width: 100%;
  padding: 24px;
}

.tableContainer {
  padding: 20px 0px;
  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0px 0px;
    white-space: nowrap;
    tbody {
      tr {
        height: 48px;
        background: #f2f2f2;
        &:nth-child(odd) {
          background: #fff;
        }
        td {
          font-size: 14px;
          font-weight: 400;
          line-height: 21px;
          padding: 0 16px;
          svg {
            vertical-align: middle;
          }
        }
        td:first-child {
          border-radius: 8px 0 0 8px;
          padding-left: 12px;
        }
        td:last-child {
          border-radius: 0 8px 8px 0;
          padding-right: 12px;
        }
      }
      &:before {
        content: "@";
        display: block;
        line-height: 0px;
        text-indent: -99999px;
      }
    }
    thead {
      tr {
        th:first-child {
          padding-left: 12px;
        }
      }
    }
    th {
      font-size: 14px;
      font-weight: 600;
      line-height: 21px;
      text-align: start;
      border-bottom: 1px solid #f2f2f2;
      padding: 0 16px;
      padding-bottom: 30px;
    }
  }
}

.cardInfoContainer {
  display: flex;
  align-items: center;
  svg,
  img {
    margin-right: 8px;
  }
}
