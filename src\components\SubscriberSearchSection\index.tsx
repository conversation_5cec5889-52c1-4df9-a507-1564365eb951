import { useState, useEffect } from "react";
import SearchBar from "../SearchBar";
import styles from "./search-section.module.scss";
import UserMenu from "../UserMenu";
import SearchBySelect from "../SearchBySelect";
import { ApiGet } from "../../pages/api/api";
import { useParams } from "react-router-dom";
import { submitSearch } from "../utils/searchAndFilter";
import { Input } from "../Input";
import {
  clearInput,
  createStateObject,
  handleInputChange,
} from "../utils/InputHandlers";
import Button from "../Button";
import { MagnifyingGlass } from "../svgs";
import qs from "qs";
import { useSelector } from "react-redux";
import SubscriberStatus from "../SubscriberStatus";
import SelectInput from "../SelectInput";

type fieldsType =
  | "subscriberName"
  | "email"
  | "mdn"
  | "iccid"
  | "imei"
  | "ban"
  | "subscriberNumberStatus";

const fields: fieldsType[] = [
  "subscriberName",
  "email",
  "mdn",
  "iccid",
  "imei",
  "ban",
  "subscriberNumberStatus",
];

const labels = {
  subscriberName: "Name",
  email: "Email Address",
  mdn: "MDN",
  iccid: "ICCID",
  imei: "IMEI",
  ban: "BAN",
  subscriberNumberStatus: "Status",
};

const statusOptions = [
  {
    value: "Active",
    label: <SubscriberStatus status="Active" />,
  },
  {
    value: "Suspended",
    label: <SubscriberStatus status="Suspended" />,
  },
  {
    value: "Cancelled",
    label: <SubscriberStatus status="Cancelled" />,
  },
  {
    value: "Rejected",
    label: <SubscriberStatus status="Rejected" />,
  },
  {
    value: "Pending",
    label: <SubscriberStatus status="Pending" />,
  },
  {
    value: "Portout",
    label: <SubscriberStatus status="Portout" />,
  },
  {
    value: "Reserved",
    label: <SubscriberStatus status="Reserved" />,
  },
];

const SubscriberSearchSection = ({
  currentPage,
  setCurrentPage,
  setLoading,
  setSubscribers,
  setMaxPage,
  selectedChannels,
  itemsPerPage,
  channelOptions,
  setSelectedChannelIds,
}: any) => {
  const { mvnoId, channelId } = useParams();

  const { userInfo } = useSelector((state: any) => state);

  const [data, setData] = useState(createStateObject(fields));
  const [masterData, setMasterData] = useState(createStateObject(fields));

  const [totalResults, setTotalResults] = useState(0);

  const loadSubs = () => {
    if (channelOptions.length === 0 && userInfo?.roleName === "Agent") return;
    setLoading(true);

    const controller = new AbortController();

    let reqUrl = `/accounts/${mvnoId}?pageSize=${itemsPerPage}&currentPage=${currentPage - 1}`;
    if (channelId) {
      reqUrl += `&channelId=${channelId}`;
    } else if (selectedChannels.length) {
      reqUrl += selectedChannels
        .map((channel: any) => `&channelId=${channel}`)
        .join("");
    } else if (userInfo?.roleName === "Agent") {
      reqUrl +=
        "&" +
        qs.stringify(
          {
            channelId: channelOptions.map((opt: any) => opt.key),
          },
          { arrayFormat: "repeat" },
        );
    }

    fields.forEach((prop: fieldsType) => {
      if (data[prop] !== "") {
        reqUrl += `&${prop}=${data[prop]}`;
      }
    });

    ApiGet(reqUrl, controller.signal)
      .then((response) => {
        setLoading(false);
        setSubscribers(response.data.content);
        setMaxPage(response.data.totalPages);
        setTotalResults(response.data.totalElements);
        if (currentPage > response.data.totalPages) {
          setCurrentPage(1);
        }
      })
      .catch((error) => {
        setLoading(false);
      });

    return () => {
      controller.abort();
    };
  };

  const loadingTriggers =
    userInfo?.roleName === "Agent"
      ? [
          currentPage,
          itemsPerPage,
          masterData,
          selectedChannels,
          channelOptions,
        ]
      : [currentPage, itemsPerPage, masterData, selectedChannels];

  useEffect(loadSubs, loadingTriggers);

  return (
    <>
      <div className={styles.main}>
        <div className={styles.heading}>Search</div>
        <div className={styles.inputs}>
          {fields.map((prop: fieldsType) => {
            if (prop === "subscriberNumberStatus") {
              return (
                <SelectInput
                  options={statusOptions}
                  selected={data.subscriberNumberStatus}
                  placeholder="Status"
                  onChange={(newStatus: any) => {
                    setData({
                      ...data,
                      subscriberNumberStatus: newStatus,
                    });
                  }}
                />
              );
            } else {
              return (
                <Input
                  key={`search-input-${prop}`}
                  label={labels[prop]}
                  value={data[prop]}
                  onChange={(e: any) =>
                    handleInputChange(prop, e, data, setData)
                  }
                  clear={() => {
                    clearInput(prop, setData);
                  }}
                  white
                />
              );
            }
          })}
        </div>
      </div>
      <div className={styles.controls}>
        <div className={styles.resultsCount}>({totalResults}) Results</div>
        <Button
          color="white"
          style={{ minWidth: "initial", marginRight: 16 }}
          onClick={() => {
            setData(createStateObject(fields));
            setMasterData(createStateObject(fields));
            setSelectedChannelIds([]);
          }}
        >
          Reset
        </Button>
        <Button
          style={{ minWidth: "initial" }}
          onClick={() => {
            setMasterData(data);
          }}
        >
          <MagnifyingGlass />
          Search
        </Button>
      </div>
    </>
  );
};

export default SubscriberSearchSection;
