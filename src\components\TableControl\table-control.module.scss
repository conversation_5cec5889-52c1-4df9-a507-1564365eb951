@use "../../styles/theme.scss" as *;

.pagination {
  margin-top: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ticketsPerPage {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  padding-left: 7px;
  margin-right: auto;
  position: relative;
  z-index: 1;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
}

.ticketsPerPageButton {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1000px;
  width: 33px;
  height: 33px;
  background: #fde5d4;
  position: relative;
  z-index: 300;
  cursor: pointer;
}

.ticketsShownMenu {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1000px;
  width: 32px;
  height: 32px;
  background: #e0dcdc;
  position: absolute;
  z-index: 200;
  top: 0.5px;
  transition: top 0.3s ease, background 0.15s ease;
  cursor: pointer;
  &:hover {
    background: #fde5d4;
  }
}

.ticketsShownMenu0 {
  top: -153px;
  transition: top 0.4s ease, background 0.15s ease;
}

.ticketsShownMenu1 {
  top: -115px;
  transition: top 0.3s ease, background 0.15s ease;
}

.ticketsShownMenu2 {
  top: -76px;
  transition: top 0.2s ease, background 0.15s ease;
}

.ticketsShownMenu3 {
  top: -38px;
  transition: top 0.1s ease, background 0.15s ease;
}
