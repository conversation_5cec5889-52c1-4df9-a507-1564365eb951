import { faker } from "@faker-js/faker";
import StatusPill from "./StatusPill";
import RoleBadge from "./RoleBadge";
import formatDate from "./utils/formatDate";

export const activityTypes = [
  { key: "LOGIN", label: "logged in" },
  { key: "LOGOUT", label: "logged out" },
  { key: "CHANGEPASSWORD", label: "changed password" },
  { key: "CREATEUSER", label: "created user" },
  { key: "EDITUSER", label: "updated user" },
  { key: "ADDSUB", label: "added a subscriber" },
  { key: "UPDATESUB", label: "updated a subscriber" },
  { key: "UPDATEPRODUCT", label: "updated a product" },
];

//////////////////////////////////////////////////////////////
/******            Subscribers                       ********/

export const serviceTypes = ["Data", "Data & Voice"];
export const productSizes = [
  "1 GB",
  "3 GB",
  "5 GB",
  "10 GB",
  "20 GB",
  "30 GB",
  "40 GB",
  "50 GB",
];
export const carriers = ["BT", "Verizon", "AT&T", "Vodafone", "Sprint"];

const createUser = () => {
  return {
    subscriberName: faker.name.firstName() + " " + faker.name.lastName(),
    subscriberNumber: faker.random.numeric(10),
    fan: faker.random.numeric(8),
    ban: faker.random.numeric(10),
    creationDate: faker.date.past(1),
    activationDate: faker.date.past(1),
    subscriberNumberStatus: faker.datatype.number({ min: 1, max: 4 }),
    dataUsed: faker.datatype.number(5000000),
    serviceType: serviceTypes[faker.datatype.number(serviceTypes.length - 1)],
    product: "APEX 1GB LTE iPhone 2.32/GB Coverage no tether",
    productSize: productSizes[faker.datatype.number(productSizes.length - 1)],
    carrier: carriers[faker.datatype.number(carriers.length - 1)],
    iccid: faker.random.numeric(18),
    imei: faker.random.numeric(15),
    address: `${faker.address.streetAddress()}, ${faker.address.city()}, ${faker.address.zipCode()}`,
    email: faker.internet.email(),
  };
};

const createEmptyUser = () => {
  return {
    subscriberName: faker.name.firstName() + " " + faker.name.lastName(),
    subscriberNumber: "",
    fan: "",
    ban: "",
    creationDate: "",
    activationDate: "",
    subscriberNumberStatus: 5,
    dataUsed: "",
    serviceType: "",
    product: "",
    productSize: "",
    carrier: "",
    iccid: "",
    imei: "",
    address: `${faker.address.streetAddress()}, ${faker.address.city()}, ${faker.address.zipCode()}`,
    email: faker.internet.email(),
  };
};

export const populateUsers = () => {
  let users = [] as any;
  Array.from({ length: 100 }).forEach((x, i) => {
    if (i === 3 || i === 54 || i === 79) {
      users.push(createEmptyUser());
    }
    users.push(createUser());
  });
  return users;
};

///////////////////////////////////////////////////////////////////////////

const createAgent = () => {
  return {
    id: faker.datatype.uuid(),
    firstName: faker.name.firstName(),
    lastName: faker.name.lastName(),
    email: faker.internet.email(),
    role: faker.datatype.number(1),
    status: faker.datatype.number(1),
  };
};

export const populateAgents = () => {
  let users = [] as any;
  Array.from({ length: 30 }).forEach(() => {
    users.push(createAgent());
  });
  return users;
};

export const productFamily = ["Airespring Throttle", "Airespring Unlimited"];
const productNames = [
  ["Airespring Throttle Data", "Airespring Data Only"],
  ["Unlimited Silver", "Unlimited Gold"],
];
export const approaches = ["Hard cut-off (cap)", "Throttle", "Overage"];
export const billings = ["Flat", "First GB", "Additional Charge"];

const createProduct = () => {
  let fam = faker.datatype.number(1);
  return {
    family: productFamily[fam],
    name: productNames[fam][faker.datatype.number(1)],
    size: productSizes[faker.datatype.number(7)],
    talkText: fam === 1 ? "Unlimited" : "-",
    approach: approaches[faker.datatype.number(2)],
    billing: billings[faker.datatype.number(2)],
  };
};

export const populateProducts = () => {
  let products = [] as any;
  Array.from({ length: 100 }).forEach(() => {
    products.push(createProduct());
  });
  return products;
};

const activityType = [
  "LOG_IN",
  "LOG_OUT",
  "CREATE_USER",
  "UPDATE_USER",
  "ADD_SUB",
  "UPDATE_SUB",
  "UPDATE_PRODUCT",
];

const createActivity = () => {
  const type = faker.datatype.number(6);

  const getUserUpdate = (updateType: number) => {
    switch (updateType) {
      case 0:
        return {
          type: "change",
          old: faker.name.firstName(),
          new: faker.name.firstName(),
        };
      case 1:
        return {
          type: "change",
          old: faker.name.lastName(),
          new: faker.name.lastName(),
        };
      case 2:
        return {
          type: "change",
          old: faker.internet.email(),
          new: faker.internet.email(),
        };
      case 3:
        const oldRole = faker.datatype.number(1);
        return {
          type: "change",
          old: <RoleBadge role={oldRole} />,
          new: <RoleBadge role={oldRole === 0 ? 1 : 0} />,
        };
    }
  };

  const getSubUpdate = (updateType: number) => {
    switch (updateType) {
      case 0:
        return {
          display: (
            <>
              <b>changed the SIM number</b> for subscriber{" "}
              {faker.name.fullName()}
            </>
          ),
          extra: {
            type: "change",
            old: faker.datatype.number(9999999999),
            new: faker.datatype.number(9999999999),
          },
        };
      case 1:
        return {
          display: (
            <>
              <b>changed the Next Bill Cycle</b> for subscriber{" "}
              {faker.name.fullName()}
            </>
          ),
          extra: {
            type: "change",
            old: formatDate(faker.date.future(1)),
            new: formatDate(faker.date.future(2)),
          },
        };
      case 2:
        const oldStatus = faker.datatype.number(4);
        return {
          display: (
            <>
              <b>changed the status</b> for subscriber {faker.name.fullName()}
            </>
          ),
          extra: {
            type: "change",
            old: <StatusPill status={oldStatus} />,
            new: <StatusPill status={oldStatus === 0 ? 1 : oldStatus - 1} />,
          },
        };
    }
  };

  const getData = () => {
    switch (type) {
      case 0:
        return {
          display: <b>logged in</b>,
          extra: null,
        };
      case 1:
        return {
          display: <b>logged out</b>,
          extra: null,
        };
      case 2:
        return {
          display: (
            <>
              <b>created user</b> {faker.name.fullName()}
            </>
          ),
          extra: null,
        };
      case 3:
        const updateType = faker.datatype.number(3);
        return {
          display: (
            <>
              <b>updated user</b> for user {faker.name.fullName()}
            </>
          ),
          extra: getUserUpdate(updateType),
        };
      case 4:
        return {
          display: (
            <>
              <b>added subscriber</b> {faker.name.fullName()}
            </>
          ),
          extra: null,
        };
      case 5:
        const subUpdateType = faker.datatype.number(2);
        return getSubUpdate(subUpdateType);
      case 6:
        const oldStatus = faker.datatype.number(1);
        return {
          display: (
            <>
              <b>changed the status</b> for product ID
              {faker.datatype.number(99999999)}
            </>
          ),
          extra: {
            type: "change",
            old: <StatusPill status={oldStatus} />,
            new: <StatusPill status={oldStatus === 0 ? 1 : 0} />,
          },
        };
    }
  };

  return {
    id: faker.datatype.uuid(),
    agent: faker.name.fullName(),
    activity: activityType[type],
    data: getData(),
    date: faker.date.past(1),
    ipAddress: `${faker.random.numeric(3)}.${faker.random.numeric(3)}.0.1`,
  };
};

export const populateActivity = () => {
  let activity = [] as any;
  Array.from({ length: 30 }).forEach(() => {
    activity.push(createActivity());
  });
  return activity;
};
