import { useState, useEffect } from "react";
import { Input } from "../Input";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import {
  clearInput,
  createStateObject,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
  displayErrors,
} from "../utils/InputHandlers";
import styles from "./nickname-modal.module.scss";
import { useDispatch } from "react-redux";
import { validateAll } from "indicative/validator";
import { ApiPatch, ApiPostAuth } from "../../pages/api/api";

const fields = ["nickname"];
const rules = getRules(fields);
const messages = getMessages(fields);

const NicknameModal = ({ show, setShow, repopulate, plan }: any) => {
  const dispatch = useDispatch();

  useEffect(() => {
    if (show && plan) {
      setData({ nickname: plan?.nickname, errors: { nickname: "" } });
    }
  }, [show, plan]);

  const handleChange = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPostAuth(`/accounts/sub-nickname/${plan?.id}/update`, {
          nickname: data.nickname,
        })
          .then((response) => {
            reset();
            repopulate();
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: response.data.message,
              },
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const reset = () => {
    setShow(false);
    setTimeout(() => {
      setData(createStateObject(fields));
      setLoading(false);
    }, 300);
  };

  const [data, setData] = useState(createStateObject(fields));
  const [loading, setLoading] = useState(false);

  return (
    <Modal
      saveButton={
        plan?.nickname ? (
          <>
            <FloppyDisk /> Save Changes
          </>
        ) : (
          "Add Nickname"
        )
      }
      setData={setData}
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      proceed={handleChange}
      setShow={setShow}
      close={() => {
        clearInput("nickname", setData);
        setShow("");
      }}
      loading={loading}
    >
      <div className={styles.main}>
        <h4>{plan?.nickname ? "Edit" : "Add"} Device Nickname</h4>
        <p>
          {plan?.nickname
            ? "Add a nickname to help identify"
            : "Update the nickname for"}{" "}
          the following MDN: <b>{plan?.mdn || plan?.subscriberNumber}</b>.{" "}
          <br />
          This name will be visible in the account and can be updated later.
        </p>
        <div className={styles.input}>
          <Input
            label={labels.nickname}
            placeholder={placeholders.nickname}
            value={data.nickname}
            onChange={(e: any) => {
              handleInputChange("nickname", e, data, setData);
            }}
            clear={() => {
              clearInput("nickname", setData);
            }}
            onKeyDown={handleChange}
            disabled={loading}
            error={data.errors.nickname}
            white
          />
        </div>
      </div>
    </Modal>
  );
};

export default NicknameModal;
