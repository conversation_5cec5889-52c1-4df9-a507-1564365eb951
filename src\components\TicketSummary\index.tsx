import { useDispatch, useSelector } from "react-redux";
import styles from "./ticket-summary.module.scss";
import { ArrowRight, CaretLeft, CaretRight, Plus } from "../svgs";
import { Fade } from "@mui/material";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import Button from "../Button";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import TicketSidebarTile from "../TicketSidebarTile";
import CorrespondenceTile from "../CorrespondenceTile";
import NotesTile from "../NotesTile";
import DIDTile from "../DIDTile";
import AttachmentsTile from "../AttachmentsTile";
import { ApiGet } from "../../pages/api/api";

const TicketSummary = ({
  nextTicket,
  prevTicket,
  handleTicketUpdate,
  assignees,
  GetTicket,
  allTickets,
}: any) => {
  const dispatch = useDispatch();
  const { ticketOpen, ticket: selectedTicket } = useSelector(
    (state: any) => state
  );

  const closeTicket = () => {
    dispatch({
      type: "set",
      ticketOpen: false,
    });
  };

  const [ticket, setTicket] = useState(null as any);

  const loadTicket = () => {
    if (selectedTicket) {
      ApiGet(`/tickets/${selectedTicket.id}`).then((response: any) => {
        setTicket(response.data);
      });
    }
  };

  useEffect(loadTicket, [selectedTicket, allTickets]);

  return (
    <div className={`${styles.main} ${ticketOpen && styles.open}`}>
      <SwitchTransition>
        <CSSTransition
          key={ticket ? ticket.id : "1"}
          addEndListener={(node, done) =>
            node.addEventListener("transitionend", done, false)
          }
          classNames="fade"
        >
          <div
            className={`${styles.mainGrid} ${
              ticket && ticket.type === "Support" && styles.support
            }`}
          >
            {ticket && (
              <>
                <div>
                  <Fade in={ticketOpen}>
                    <div onClick={closeTicket} className={styles.close}>
                      <CaretRight />
                    </div>
                  </Fade>
                  <div className={styles.prevNext}>
                    <div
                      onClick={prevTicket}
                      style={{ marginRight: 16, cursor: "pointer" }}
                    >
                      <CaretLeft />
                    </div>
                    <div onClick={nextTicket} style={{ cursor: "pointer" }}>
                      <CaretRight />
                    </div>
                  </div>
                  {/*<div className={styles.customerInfo}>
                    <div>
                      <div className={styles.name}>{ticket.name}</div>
                      <div className={styles.email}>{ticket.email}</div>
                    </div>
                    <div
                      className={styles.customerSummary}
                      id="customer-summary"
                    >
                      Subscriber Details <ArrowRight />
                    </div>
            </div>*/}

                  {/*<div className={styles.selectionWrapper}>
                    <div
                      className={`${styles.selection} ${
                        selection === "ticket" && styles.activeSelection
                      }`}
                      onClick={() => {
                        setSelection("ticket");
                      }}
                    >
                      <span>Ticket Overview</span>
                      {selection === "ticket" && (
                        <motion.div
                          className={styles.background}
                          layoutId="ticket-summary-underline"
                        />
                      )}
                    </div>
                    <div
                      className={`${styles.selection} ${
                        selection === "correpsondance" && styles.activeSelection
                      }`}
                      onClick={() => {
                        setSelection("correpsondance");
                      }}
                    >
                      <span>Correspondence</span>
                      {selection === "correpsondance" && (
                        <motion.div
                          className={styles.background}
                          layoutId="ticket-summary-underline"
                        />
                      )}
                      </div>
                  </div>*/}
                </div>
                <div className={`${styles.contentScroll} modal-scroll`}>
                  <TicketSidebarTile
                    GetTicket={() => {
                      GetTicket();
                      loadTicket();
                    }}
                    ticket={ticket}
                    handleTicketUpdate={handleTicketUpdate}
                    assignees={assignees}
                  />
                  {/*<AttachmentsTile sidebar />
                  <div style={{ height: 24 }} />*/}

                  <NotesTile
                    sidebar
                    list={ticket?.notes ? ticket.notes : []}
                    repopulate={() => {
                      GetTicket();
                      loadTicket();
                    }}
                    ticketId={ticket?.id}
                  />

                  {/* {ticket.type === "Support" ? (
                    <SwitchTransition>
                      <CSSTransition
                        key={selection}
                        addEndListener={(node, done) =>
                          node.addEventListener("transitionend", done, false)
                        }
                        classNames="fade"
                      >
                        <div>
                          {selection === "ticket" ? (
                            <div>
                              <TicketSidebarTile ticket={ticket} />
                              <NotesTile />
                            </div>
                          ) : (
                            <CorrespondenceTile />
                          )}
                        </div>
                      </CSSTransition>
                    </SwitchTransition>
                  ) : ticket.type === "DID Request" ? (
                    <>
                      <TicketSidebarTile ticket={ticket} />
                      <DIDTile />
                    </>
                  ) : (
                    <>
                      <TicketSidebarTile ticket={ticket} />
                      <div
                        style={{ display: "flex", justifyContent: "flex-end" }}
                      >
                        <Button>Confirm & Forward</Button>
                      </div>
                    </>
                  )} */}
                </div>
              </>
            )}
          </div>
        </CSSTransition>
      </SwitchTransition>
    </div>
  );
};

export default TicketSummary;
