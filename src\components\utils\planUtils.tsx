export const planStatuses = [
  "active",
  "ready-to-activate-temp-subscription",
  "ready-to-activate-temp-portin",
  "rejected",
  "suspended",
  "pending-activation",
  "pending-ban-change",
  "iccid-required",
  "cancelled-resume-available",
  "cancelled-resume-not-available",
  "missing-porting-details",
  "port-out",
  // active portin statuses
  "port-in-in-progress",
  "port-in-completed",
  "port-in-failed",
  "port-in-confirmed",
  "port-in-cancelled",
  "port-in-incomplete",
] as const;

export type PlanStatus = (typeof planStatuses)[number];

export function getStatusesForPlan(plan: any) {
  const statuses: PlanStatus[] = [];

  const subscriberNumberStatus = plan?.subscriberNumberStatus;

  /* Status Derivation Logic */

  // 1. Active
  if (subscriberNumberStatus === "Active") {
    statuses.push("active");
  }

  // 2. Cancelled (Resume not available)
  if (
    subscriberNumberStatus === "Cancelled" &&
    (!plan?.remainingDaysToResume || plan?.remainingDaysToResume <= 0)
  ) {
    statuses.push("cancelled-resume-not-available");
  }

  // 3. Cancelled (Resume available)
  if (
    subscriberNumberStatus === "Cancelled" &&
    plan?.remainingDaysToResume &&
    plan?.remainingDaysToResume > 0
  ) {
    statuses.push("cancelled-resume-available");
  }

  // 4. Rejected
  if (subscriberNumberStatus === "Rejected") {
    statuses.push("rejected");
  }

  // 5. Pending Activation
  if (
    plan?.type === "activeSubscription" &&
    subscriberNumberStatus === "Pending"
  ) {
    statuses.push("pending-activation");
  }

  // 6. Pending BAN Change
  if (subscriberNumberStatus === "Ban change") {
    statuses.push("pending-ban-change");
  }

  // 7. Suspended
  if (subscriberNumberStatus === "Suspended") {
    statuses.push("suspended");
  }

  // 8. Ready to Activate (Temp Subscription)
  if (plan?.type === "tempSubscription" && plan?.iccid) {
    statuses.push("ready-to-activate-temp-subscription");
  }

  // 9. Ready to Activate (Temp Portin)
  if (
    plan?.type === "tempPortingProcess" &&
    plan?.iccid &&
    plan?.status !== "Failed" &&
    // check for billing account number and password in plan, fallback to attDetails for when plan details comes from porting details endpoint
    (plan?.billingAccountNumber ||
      plan?.attDetails?.oldService?.billingAccountNumber) &&
    (plan?.billingAccountPassword ||
      plan?.attDetails?.oldService?.billingAccountPassword)
  ) {
    statuses.push("ready-to-activate-temp-portin");
  }

  // 10. ICCID Required
  if (
    !plan?.iccid &&
    (plan?.type === "tempSubscription" || plan?.type === "tempPortingProcess")
  ) {
    statuses.push("iccid-required");
  }

  // 11. Missing Porting Details
  if (
    plan?.type === "tempPortingProcess" &&
    // check for billing account number and password in plan, fallback to attDetails for those when plan details comes from porting details endpoint
    (!(
      plan?.billingAccountNumber ||
      plan?.attDetails?.oldService?.billingAccountNumber
    ) ||
      !(
        plan?.billingAccountPassword ||
        plan?.attDetails?.oldService?.billingAccountPassword
      ))
  ) {
    statuses.push("missing-porting-details");
  }

  //12. Port Out
  if (
    subscriberNumberStatus === "Portout" ||
    subscriberNumberStatus === "PortOut"
  ) {
    statuses.push("port-out");
  }

  // Active Portin Statuses.
  // We check for activePortingStatus first, then fallback to status for when plan details comes from porting details endpoint
  //13. Port-in In Progress
  if (
    plan?.type === "activePortingProcess" &&
    (plan?.activePortingStatus === "In Progress" ||
      plan?.status === "In Progress")
  ) {
    statuses.push("port-in-in-progress");
  }

  //14. Port-in Completed
  if (
    plan?.type === "activePortingProcess" &&
    (plan?.activePortingStatus === "Completed" || plan?.status === "Completed")
  ) {
    statuses.push("port-in-completed");
  }

  //15. Port-in Failed
  if (
    plan?.type === "activePortingProcess" &&
    (plan?.activePortingStatus === "Failed" || plan?.status === "Failed")
  ) {
    statuses.push("port-in-failed");
  }

  //16. Port-in Confirmed
  if (
    plan?.type === "activePortingProcess" &&
    (plan?.activePortingStatus === "Confirmed" || plan?.status === "Confirmed")
  ) {
    statuses.push("port-in-confirmed");
  }

  //17. Port-in Cancelled
  if (
    plan?.type === "activePortingProcess" &&
    (plan?.activePortingStatus === "Cancelled" || plan?.status === "Cancelled")
  ) {
    statuses.push("port-in-cancelled");
  }

  //18. Port-in Incomplete
  if (
    plan?.type === "activePortingProcess" &&
    (plan?.activePortingStatus === "InComplete" ||
      plan?.status === "InComplete")
  ) {
    statuses.push("port-in-incomplete");
  }

  return statuses;
}
