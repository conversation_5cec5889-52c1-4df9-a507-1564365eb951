import styles from "./rate-plan.module.scss";

const RatePlan = ({
  activePlan,
  setActivePlan,
  plan,
  noTether,
  update,
}: any) => {
  return (
    <div
      className={`${styles.main} ${
        update
          ? activePlan.OfferId === plan.OfferId && styles.active
          : activePlan.soc === plan.soc &&
            activePlan.offerId === plan.offerId &&
            styles.active
      }`}
      onClick={() => {
        setActivePlan(plan);
      }}
    >
      <div className={styles.name}>
        {update ? plan.Name : plan.offerName} {noTether ? "no tether" : ""}
      </div>
      <div className={styles.description}>
        {update
          ? `${plan.DeviceType}, ${plan.ProductFamily}, ${plan.Size}, ${plan.ProductApproach}, ${plan.TalkAndText}`
          : `${plan.deviceType}, ${plan.productFamily}, ${plan.teaserType}, ${plan.size}, ${plan.serviceType}`}
      </div>
    </div>
  );
};

export default RatePlan;
