import { useNavigate, useParams } from "react-router-dom";
import Menu from "../../components/Menu";
import { LogOut, Pencil, SendPassword, User } from "../../components/svgs";
import { logOut } from "../../components/utils/logOut";
import styles from "../../styles/user-profile.module.scss";
import { useDispatch, useSelector } from "react-redux";
import RoleBadge from "../../components/RoleBadge";
import Button from "../../components/Button";
import ChangePasswordModal from "../../components/ChangePasswordModal";
import { useEffect, useState } from "react";
import { ApiGet, ApiPostAuth } from "../api/api";
import EditUserModal from "../../components/EditUserModal";

const UserProfile = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { userInfo } = useSelector((state: any) => state);

  const { mvnoId } = useParams();

  const [changingPassword, setChangingPassword] = useState(false);
  const [editingUser, setEditingUser] = useState(false);

  const repopulateUserInfo = () => {
    ApiPostAuth("/users/view", {
      userMid: userInfo.mid,
      mvnoId: mvnoId,
    })
      .then((response) => {
        let { userId, ...userDetails } = response.data;
        userDetails.mid = userId;
        userDetails.brandLogo = userInfo.brandLogo;
        dispatch({
          type: "set",
          userInfo: userDetails,
        });
        localStorage.setItem("crmUserInfo", JSON.stringify(userDetails));
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const repopulateUserInfoMvne = (request: any) => {
    let { userId, roleId, enable2fa, ...userDetails } = request;
    userDetails.mid = userId;
    userDetails.brandLogo = userInfo.brandLogo;
    userDetails.roleName =
      roleId === 3 ? "mvne" : roleId === 1 ? "Agent" : "Admin";
    userDetails.is2faEnabled = enable2fa;
    dispatch({
      type: "set",
      userInfo: userDetails,
    });
    localStorage.setItem("crmUserInfo", JSON.stringify(userDetails));
  };

  return (
    <div className={styles.main}>
      <div className={styles.topBar}>
        <h2>My Profile</h2>
        <Menu
          data={{
            label: userInfo.firstName,
            items: [
              {
                label: "Profile",
                icon: <User />,
                link: `/${mvnoId}/user-profile`,
              },
              {
                label: "Logout",
                icon: <LogOut />,
                link: "/login",
                onClick: () => {
                  logOut(dispatch, navigate);
                },
              },
            ],
          }}
        />
      </div>
      <ChangePasswordModal
        show={changingPassword}
        setShow={setChangingPassword}
        user={userInfo}
        id={userInfo.mid}
      />
      <EditUserModal
        show={editingUser}
        setShow={setEditingUser}
        user={userInfo}
        id={userInfo.mid}
        resetActiveUser={null}
        repopulateUsers={
          userInfo.roleName === "mvne"
            ? repopulateUserInfoMvne
            : repopulateUserInfo
        }
        self
        mvne={userInfo.roleName === "mvne"}
      />
      <div className={styles.mainTile}>
        <div className={styles.grid}>
          <img className={styles.illustration} src="/user_profile.png" />
          <div className={styles.userDetails}>
            <RoleBadge role={userInfo.roleName} />
            <div className={styles.nameContainer}>
              <div className={styles.name}>
                {userInfo.firstName} {userInfo.lastName}
              </div>
              <button
                onClick={() => {
                  setEditingUser(true);
                }}
                className={styles.editButton}
              >
                <Pencil />
              </button>
            </div>
            <div className={styles.email}>{userInfo.email}</div>
            <Button
              onClick={() => {
                setChangingPassword(true);
              }}
            >
              <SendPassword />
              Change Password
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
