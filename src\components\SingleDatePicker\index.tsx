import { useState, useEffect, useCallback } from "react";
import { Calendar, CaretDown, CaretUp, ChevronDown } from "../svgs";
import styles from "./single-date-picker.module.scss";
import {
  getMonthDays,
  getMonthFirstDay,
  zeroPad,
  getMonthArray,
  formatDate,
  isDateNow,
  dateInPast,
  isAtLeastNextDay,
} from "../utils/dateHelpers";
import MonthYearSelect from "../MonthYearSelect";
import Button from "../Button";
import { ControlledMenu, MenuItem, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { useRef } from "react";
import { formatDate as formatSlashDate } from "../utils/dateHelpers";
import MonthPicker from "../MonthPicker";
import { Collapse, Fade } from "@mui/material";
import moment from "moment";

const now = new Date();

type SingleDatePickerProps = {
  minDate?: Date;
  [key: string]: any;
};

const SingleDatePicker = ({
  date,
  setDate,
  minDate,
  label = "Effective date",
  future = false,
  error,
  large,
  style,
  disabled,
}: SingleDatePickerProps) => {
  const daysLetters = ["M", "T", "W", "T", "F", "S", "S"];

  const [month, setMonth] = useState(now.getMonth() + 1);
  const [year, setYear] = useState(now.getFullYear());
  const [displayMonths, setDisplayMonths] = useState([] as any);

  function isAllowedDate(date: Date) {
    if (future) {
      if (minDate) {
        // ensure that if minDate is in the past(and future = true), it doesn't override returning only future dates
        return dateInPast(moment(minDate).toDate())
          ? !dateInPast(date)
          : isAtLeastNextDay(minDate, date);
      } else {
        return !dateInPast(date);
      }
    }

    if (minDate) {
      return isAtLeastNextDay(minDate, date);
    }

    return true;
  }

  useEffect(() => {
    const days = getMonthDays(month, year);
    const first = getMonthFirstDay(month, year);
    const newMonthArray = getMonthArray(
      Array.from({ length: days }, (v, i) => ({
        type: "main",
        value: i + 1,
        date: new Date(`${year}/${zeroPad(month)}/${zeroPad(i + 1)}`),
      })),
      days,
      first,
      month,
      year,
    );
    setDisplayMonths(newMonthArray);
  }, [month, year]);

  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const reset = () => {
    toggleMenu(false);
  };

  return (
    <div
      className={`${styles.box} ${
        large && styles.large
      } single-date-select important-date-style menu-select`}
    >
      <div
        ref={ref}
        className={`${styles.menuButton} ${error && styles.error} ${
          disabled && styles.disabled
        } ${date && styles.hasValue}`}
        style={style}
        onClick={(e: any) => {
          e.stopPropagation();
          if (!disabled) {
            toggleMenu(true);
          }
        }}
      >
        {date && (
          <div className={`${styles.label} ${error && styles.error}`}>
            {label}
          </div>
        )}
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          {error && (
            <Fade in={error ? true : false}>
              <img
                src="/input_error.svg"
                onMouseDown={(e) => {
                  e.preventDefault();
                }}
                style={{ right: 45 }}
              />
            </Fade>
          )}
          <Calendar color="#F37121" />
        </div>
        {date ? formatSlashDate(date) : label}
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={reset}
        align="start"
        onItemClick={(e: any) => (e.stopPropagation = true)}
      >
        <div className={styles.main}>
          <div className={styles.mainDatePicker}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                padding: "12px 26px 0 26px",
              }}
            >
              <MonthPicker
                activeMonth={month}
                setActiveMonth={setMonth}
                activeYear={year}
                setActiveYear={setYear}
                future={future}
              />
              <input
                style={{
                  position: "absolute",
                  opacity: 0,
                  zIndex: -1,
                }}
                id="close-month-menu"
              />
              <div className={styles.prevNext}>
                <div
                  className={styles.prev}
                  onClick={() => {
                    if (month === 1) {
                      setMonth(12);
                      setYear(year - 1);
                    } else {
                      setMonth(month - 1);
                    }
                  }}
                >
                  <CaretUp />
                </div>
                <div
                  className={styles.next}
                  onClick={() => {
                    if (month === 12) {
                      setMonth(1);
                      setYear(year + 1);
                    } else {
                      setMonth(month + 1);
                    }
                  }}
                >
                  <CaretDown />
                </div>
              </div>
            </div>
            <div className={styles.calendar}>
              <div className={styles.days}>
                {daysLetters.map((letter) => (
                  <div className={styles.letter}>{letter}</div>
                ))}
              </div>
              <div className={styles.datesGrid}>
                {displayMonths.map((day: any) => (
                  <div
                    className={`${styles.cellContainer} ${
                      !isAllowedDate(day.date) && styles.disabled
                    }`}
                    key={`${day.type}-date-${day.date.getTime()}`}
                  >
                    <div
                      className={`${styles.day} ${
                        isDateNow(day.date) && styles.now
                      } ${day.type === "pad" && styles.pad} ${
                        date
                          ? date.getTime() === day.date.getTime()
                            ? styles.active
                            : ""
                          : ""
                      } ${!isAllowedDate(day.date) && styles.disabled}`}
                      onClick={() => {
                        if (isAllowedDate(day.date)) {
                          setDate(day.date);
                          toggleMenu(false);
                        }
                      }}
                    >
                      {day.value}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </ControlledMenu>
      <Collapse in={error ? true : false}>
        <p className={styles.errorText} id={`error`}>
          {error || <br />}
        </p>
      </Collapse>
    </div>
  );
};

export default SingleDatePicker;
