import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import styles from "./project-tile.module.scss";

const ProjectTile = ({ name, mvnoId, logo }: any) => {
  const dispatch = useDispatch();

  const { userInfo } = useSelector((state: any) => state);

  return (
    <Link
      to={`/${mvnoId}/subscriber-management`}
      onClick={() => {
        let userInfoStore = { ...userInfo };
        userInfoStore.brandLogo = logo;
        userInfoStore.mvnoName = name;
        localStorage.setItem("crmUserInfo", JSON.stringify(userInfoStore));

        dispatch({
          type: "set",
          userInfo: userInfoStore,
        });
      }}
      style={{ textDecoration: "none" }}
    >
      <div
        className={`${styles.main} ${
          (name.includes("EZ Mobile") || name.includes("S-Mobile")) &&
          styles.dark
        }`}
      >
        <div className={styles.content}>
          <img alt={name} src={logo} className={styles.img} title={name} />
          <div className={styles.name}>{name}</div>
        </div>
      </div>
    </Link>
  );
};

export default ProjectTile;
