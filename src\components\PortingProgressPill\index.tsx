import { Info } from "../svgs";
import styles from "./porting-progress-pill.module.scss";

export const statuses = [
  "In Progress",
  "Completed",
  "Failed",
  "Confirmed",
  "Cancelled",
  "InComplete",
];

const PortingProgressPill = ({
  status,
  port,
  setActivePort,
  setCurrentModal,
}: any) => {
  return (
    <div
      className={`${styles.main} ${
        styles[
          `status-${statuses.indexOf(status) === -1 ? "none" : statuses.indexOf(status)}`
        ]
      }`}
    >
      {status === "InComplete" ? "Incomplete" : status}
      {status === "Failed" && (
        <div
          style={{ cursor: "pointer" }}
          onClick={() => {
            setActivePort(port);
            setCurrentModal("view-port-fail");
          }}
        >
          <Info />
        </div>
      )}
    </div>
  );
};

export default PortingProgressPill;
