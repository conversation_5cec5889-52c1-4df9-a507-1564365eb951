@use "../../styles/theme.scss" as *;

.main {
  width: 100%;
  padding: 16px 24px;
  border-radius: 16px;
  background-color: #f7f6f6;
  margin-bottom: 12px;
  border: 2px solid #f7f6f6;
  transition: border-color 0.2s ease;
  cursor: pointer;
  &:hover {
    border-color: $light-orange;
  }
  &.active {
    border-color: $orange;
    cursor: auto;
    &:hover {
      border-color: $orange;
    }
  }
  .name {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 12px;
  }
  .description {
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
  }
}
