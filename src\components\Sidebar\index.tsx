import styles from "./sidebar.module.scss";
import {
  Dashboard,
  User,
  Users,
  Sliders,
  Ticketing,
  ArrowLeft,
  CaretRight,
  ClockTimer,
  Reports,
  Lifebuoy,
  Box,
  Storefront,
  GiftBox,
  Globe,
  NewTab,
} from "../svgs";
import { useDispatch, useSelector } from "react-redux";
import { Collapse, Fade } from "@mui/material";
import { Link, useLocation, useParams } from "react-router-dom";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import { motion } from "framer-motion";

const Sidebar = () => {
  const {
    sidebarOpen: open,
    userInfo,
    ticketOpen,
  } = useSelector((state: any) => state);
  const dispatch = useDispatch();
  const location = useLocation();

  const { project, mvnoId } = useParams();

  const toggleSidebar = () => {
    localStorage.setItem("sidebarOpen", !open ? "true" : "false");
    dispatch({
      type: "set",
      sidebarOpen: !open,
      ticketOpen: !open ? false : ticketOpen,
    });
  };

  const Page = ({ Icon, title, url, newTab = false }: any) => (
    <Link
      to={url}
      style={{ textDecoration: "none", marginBottom: 10 }}
      target={newTab ? "_blank" : "_self"}
    >
      <div
        className={`${styles.page} ${
          location.pathname.includes(url) && styles.pageActive
        }`}
      >
        <Icon />
        <span className={styles.title}>
          {title}
          {newTab && (
            <div className={styles.newTab}>
              <NewTab />
            </div>
          )}
        </span>
        {location.pathname.includes(url) && (
          <motion.div className={styles.background} />
        )}
      </div>
    </Link>
  );

  return (
    <div className={`${styles.main} ${open ? styles.open : styles.closed}`}>
      <div
        style={{
          display: "flex",
          width: "100%",
          alignItems: "center",
        }}
      >
        {/*<img src="/hero_h_logo.png" className={styles.heroLogo} />*/}
        {userInfo.roleName === "mvne" && (
          <Collapse in={open} orientation="horizontal">
            <Link to="/select-project">
              <div className={styles.back}>
                <ArrowLeft />
              </div>
            </Link>
          </Collapse>
        )}
        <img
          src={
            userInfo
              ? userInfo.brandLogo
                ? userInfo.brandLogo
                : "/AireSpring-Logo.png"
              : "/AireSpring-Logo.png"
          }
          className={styles.airLogo}
          style={{
            marginLeft: userInfo.roleName === "mvne" && open ? 40 : 0,
            marginRight: userInfo.roleName === "mvne" && open ? 12 : 0,
          }}
        />
      </div>
      {!userInfo?.mvnoName?.includes("Preferred LLC") && (
        <Collapse in={open} orientation="horizontal">
          <div className={styles.mvnoName}>{userInfo?.mvnoName}</div>
        </Collapse>
      )}
      <div className={styles.divider} />
      {/*<SwitchTransition>
        <CSSTransition
          key={!open ? "small" : "large"}
          addEndListener={(node, done) =>
            node.addEventListener("transitionend", done, false)
          }
          classNames="fade"
        >
          <div className={styles.logoWrapper}>
            {!open ? (
              <div
                className={`${styles.smallLogoWrapper} ${
                  userInfo.roleName === "mvne" && styles.mvne
                }`}
              >
                <img src="/placeholder.png" className={styles.projectLogo} />
                {userInfo.roleName === "mvne" && (
                  <Link className={styles.backLink} to="/select-project">
                    <div className={styles.back}>
                      <ArrowLeft />
                    </div>
                  </Link>
                )}
              </div>
            ) : (
              <div>
                {userInfo.roleName === "mvne" && (
                  <Link to="/select-project">
                    <div className={styles.back}>
                      <ArrowLeft />
                    </div>
                  </Link>
                )}
                <img src="/placeholder.png" className={styles.projectLogo} />
              </div>
            )}
          </div>
        </CSSTransition>
                </SwitchTransition>*/}
      <div className={styles.pages}>
        <Page Icon={Ticketing} title="Tickets" url={`/${mvnoId}/tickets`} />
        <Page
          Icon={Storefront}
          title="Channel Management"
          url={`/${mvnoId}/channel-management`}
        />
        <Page
          Icon={User}
          title="Subscriber Management"
          url={`/${mvnoId}/subscriber-management`}
        />
        <Page Icon={Box} title="Orders" url={`/${mvnoId}/orders`} />
        <Page Icon={GiftBox} title="Promotions" url={`/${mvnoId}/promotions`} />

        {userInfo.roleName !== "Agent" && (
          <>
            <Page
              Icon={Dashboard}
              title="Product Catalogue"
              url={`/${mvnoId}/product-catalogue`}
            />
            <Page
              Icon={Globe}
              title="Travel eSIM"
              url="https://airespring-esim.mobiliseconnect.com"
              newTab
            />
            <Page
              Icon={Sliders}
              title="User Management"
              url={`/${mvnoId}/user-management`}
            />
            <Page
              Icon={ClockTimer}
              title="Activity Logs"
              url={`/${mvnoId}/activity-logs`}
            />
          </>
        )}
        <Page Icon={Reports} title="Reports" url={`/${mvnoId}/reports`} />
        <Page Icon={Lifebuoy} title="Support" url={`/${mvnoId}/support`} />
        <div
          className={`${styles.page} ${styles.expand} ${
            open && styles.collapseMenu
          }`}
          onClick={toggleSidebar}
          style={{ marginTop: "auto" }}
        >
          <CaretRight />
          {open && <span style={{ marginLeft: 16 }}>Collapse Menu</span>}
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
