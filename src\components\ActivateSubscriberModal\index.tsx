import { useEffect, useState } from "react";
import Modal from "../Modal";
import RatePlan from "../RatePlan";
import styles from "./activate-subscriber-modal.module.scss";
import { Input } from "../Input";
import {
  clearInput,
  createStateObject,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import { validateAll } from "indicative/validator";
import { displayErrors } from "../utils/InputHandlers";
import { CheckCircle, Pencil, XCircle } from "../svgs";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import { Collapse } from "@mui/material";
import AddSubscriberSuccessModal from "../AddSubscriberSuccessModal";
import AddSubscriberErrorModal from "../AddSubscriberErrorModal";
import { useParams } from "react-router-dom";

const fields = ["imei"];
const rules = getRules(fields);
const messages = getMessages(fields);

const iccidFields = ["iccid"];
const iccidRules = getRules(iccidFields);
const iccidMessages = getMessages(iccidFields);

type ActivateSubscriberModalProps = {
  complete: (
    plan: any,
    imei: string,
    iccid: string,
    savingForLater: boolean,
    setActivateLoading: any,
  ) => void;
  show?: any;
  setShow?: any;
  addingNew?: any;
  mid?: any;
  repopulate?: any;
  porting?: any;
  portingUpdate?: any;
  portingCancel?: any;
  initialData?: any;

  /**  Only show retail plans with valid prices */
  onlyValidRetailPlans?: boolean;

  /**  Don't make any API calls, just call `complete` */
  proceedWithoutSubmitting?: boolean;
};

type Sections = "enter-imei" | "select-plan" | "summary";

const ActivateSubscriberModal = ({
  show,
  setShow,
  complete,
  addingNew = false,
  mid = 0,
  repopulate = null,
  porting,
  portingUpdate = false,
  portingCancel = null,
  initialData = null,
  onlyValidRetailPlans = false,
  proceedWithoutSubmitting = false,
}: ActivateSubscriberModalProps) => {
  const dispatch = useDispatch();

  const { mvnoId } = useParams();

  const [activePlan, setActivePlan] = useState({
    offerId: -1,
    offerName: "",
  } as any);

  const [activeSection, setActiveSection] = useState<Sections>("enter-imei");

  const [data, setData] = useState(createStateObject(fields));
  const [iccid, setIccid] = useState(createStateObject(iccidFields));

  const [loading, setLoading] = useState(false);

  const [numberIneligible, setNumberIneligible] = useState(false);
  const [numberVerified, setNumberVerified] = useState(false);
  const [ineligibleReason, setIneligibleReason] = useState("");

  const [currentActivationType, setCurrentActivationType] = useState("normal");

  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    if (initialData && show) {
      setData({
        imei: initialData.imei,
        errors: {
          imei: "",
        },
      });

      setIccid({
        iccid: initialData.iccid,
        errors: {
          iccid: "",
        },
      });

      setActivePlan({
        deviceType: initialData.product?.deviceType,
        offerId: initialData.product?.offerId,
        offerName: initialData.product?.product,
        productFamily: "",
        size: initialData.product?.productSize,
        soc: initialData.product?.soc,
        serviceType: initialData.product?.serviceType,
        retailName: initialData.product?.retailName,
        retailPrice: initialData.product?.retailPrice,
      });

      if (portingUpdate) {
        setActiveSection("summary");
      }
    }
  }, [initialData, show, portingUpdate]);

  useEffect(() => {
    setLoading(false);
  }, [show]);

  const [plans, setPlans] = useState([]);

  const reset = () => {
    setShow(false);
    setTimeout(() => {
      setData(createStateObject(fields));
      setIccid(createStateObject(iccidFields));
      setPlans([]);
      setActiveSection("enter-imei");
      setActivePlan({
        offerId: -1,
        offerName: "",
      } as any);
      setNumberIneligible(false);
      setNumberVerified(false);
      setIneligibleReason("");
    }, 300);
  };

  useEffect(() => {
    setNumberIneligible(false);
    setNumberVerified(false);
    setIneligibleReason("");
  }, [data.imei]);

  const handleCheckImei = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        setNumberIneligible(false);
        setNumberVerified(false);
        setIneligibleReason("");
        ApiPostAuth("/products/availableproducts", {
          imei: data.imei,
          mvnoId: parseInt(mvnoId!),
        })
          .then((response: any) => {
            setLoading(false);
            if (response.data.length) {
              setPlans(
                onlyValidRetailPlans
                  ? filterInvalidRetailPlans(response.data)
                  : response.data,
              );
              setNumberVerified(true);
            } else {
              setNumberIneligible(true);
            }
          })
          .catch((error) => {
            setLoading(false);
            setNumberIneligible(true);
            setIneligibleReason(error.response.data.message);
          });

        /*if (activePlan === -1) {
          setActiveSection("select-plan");
        } else {
          setActiveSection("summary");
        }*/
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const handleCheckIccid = (e: any, savingForLater = false) => {
    validateAll(iccid, iccidRules, iccidMessages)
      .then((response) => {
        setCurrentActivationType(
          savingForLater ? "saving-for-later" : "normal",
        );
        if (addingNew) {
          handleActivation(savingForLater);
        } else {
          setLoading(true);
          complete(
            activePlan,
            data.imei,
            iccid.iccid,
            savingForLater,
            setLoading,
          );
        }
      })
      .catch((errors) => {
        displayErrors(errors, setIccid);
      });
  };

  const handleActivation = (savingForLater = false) => {
    setLoading(true);
    ApiPostAuth("/accounts/attactivation/start", {
      imei: data.imei,
      iccid: iccid.iccid,
      carrier: "AT&T",
      product: {
        deviceType: activePlan.deviceType,
        productFamily: activePlan.productFamily,
        size: activePlan.size,
        serviceType: activePlan.serviceType,
        offerId: activePlan.offerId,
        offerName: activePlan.offerName,
        soc: activePlan.soc,
      },
      mid: mid,
    })
      .then((response) => {
        if (savingForLater) {
          setShow("");
          reset();
          dispatch({
            type: "notify",
            payload: {
              error: false,
              message: response.data.message,
            },
          });
          if (repopulate !== null) {
            repopulate();
          }
        } else {
          completeActivation(response);
        }
      })
      .catch((error) => {
        setLoading(false);
        setError(true);
        setErrorMessage(error.response.data.message);
      });
  };

  // Complete activation
  const completeActivation = (prevResponse: any) => {
    ApiPostAuth("/accounts/attactivation", {
      accountId: prevResponse.data.accountId,
      tempSubscriptionId: prevResponse.data.tempSubscriptionId,
    })
      .then((response) => {
        setLoading(false);
        setSuccess(true);
        if (repopulate !== null) {
          repopulate();
        }
      })
      .catch((error) => {
        setLoading(false);
        setError(true);
        setErrorMessage(error.response.data.message);
      });
  };

  const handleAddPlan = () => {
    setActiveSection("summary");
  };

  const [tether, setTether] = useState(true);

  const handleShowPlans = () => {
    setActiveSection("select-plan");
  };

  const getProceedAction = () => {
    if (activeSection === "enter-imei") {
      return numberVerified ? handleShowPlans : handleCheckImei;
    } else if (activeSection === "summary") {
      if (proceedWithoutSubmitting) {
        return () => {
          // validate iccid
          validateAll(iccid, iccidRules, iccidMessages)
            .then(() => {
              setShow(false);
              reset();
              complete(activePlan, data.imei, iccid.iccid, false, setLoading);
            })
            .catch((errors) => {
              displayErrors(errors, setIccid);
            });
        };
      }

      return handleCheckIccid;
    } else {
      return handleAddPlan;
    }
  };

  const getSecondaryButtonText = () => {
    if (activeSection === "summary") {
      if (proceedWithoutSubmitting) {
        return null;
      }

      if (portingUpdate) {
        return null;
      }

      return "Save for later";
    } else {
      return null;
    }
  };

  const getSaveButtonText = () => {
    if (proceedWithoutSubmitting) {
      return "Next";
    }

    if (activeSection === "select-plan") {
      return "Add plan";
    } else if (activeSection === "summary") {
      if (porting) {
        return portingUpdate
          ? "Re-submit Port-in Request"
          : "Send Port-in Request";
      }
      return "Activate Subscriber";
    } else {
      return numberVerified ? "Next" : "Verify";
    }
  };

  return (
    <Modal
      scroll
      saveButton={getSaveButtonText()}
      secondary={getSecondaryButtonText()}
      onSecondary={(e: any) => {
        handleCheckIccid(e, true);
      }}
      cancelButton="Cancel"
      show={show}
      proceed={getProceedAction()}
      image="/add_user_graphic.svg"
      close={() => {
        if (porting) {
          if (portingCancel) {
            portingCancel();
          }
        } else {
          reset();
        }
      }}
      fullSize={activeSection === "select-plan"}
      title={activeSection === "select-plan" ? "Activate Subscriber" : null}
      subtitle={
        activeSection === "select-plan" ? (
          <SelectPlanSubtitle tether={tether} setTether={setTether} />
        ) : null
      }
      clearContainer={!addingNew}
      loading={loading && currentActivationType === "normal"}
      secondaryLoading={loading && currentActivationType === "saving-for-later"}
      onCancel={() => {
        if (porting) {
          if (portingCancel) {
            portingCancel();
          }
        } else {
          reset();
        }
      }}
    >
      <AddSubscriberSuccessModal
        show={success}
        setShow={setSuccess}
        handleFinish={reset}
        type="activate"
        handleToSubscriberPage={reset}
      />
      <AddSubscriberErrorModal
        show={error}
        setShow={setError}
        handleFinish={reset}
        error={errorMessage}
      />
      {activeSection === "enter-imei" ? (
        <div className={styles.imeiMain}>
          <h3>Activate Subscriber</h3>
          <h4>Enter device IMEI</h4>
          <Input
            label={labels.imei}
            placeholder={placeholders.imei}
            value={data.imei}
            onChange={(e: any) => {
              handleInputChange("imei", e, data, setData);
            }}
            error={data.errors.imei}
            clear={() => {
              clearInput("imei", setData);
            }}
            disabled={loading}
            onKeyDown={handleCheckImei}
          />
          <Collapse style={{ width: "100%" }} in={numberVerified}>
            <div className={styles.eligible}>
              <CheckCircle />
              <div>IMEI is eligible</div>
            </div>
          </Collapse>
          <Collapse style={{ width: "100%" }} in={numberIneligible}>
            <div className={styles.notEligible}>
              <XCircle />
              <div>
                <div className={styles.topText}>IMEI not eligible</div>
                <div className={styles.bottomText}>{ineligibleReason}</div>
              </div>
            </div>
          </Collapse>
        </div>
      ) : activeSection === "select-plan" ? (
        <div className={styles.plansMain}>
          <SwitchTransition>
            <CSSTransition
              key={tether ? "tether" : "no-tether"}
              addEndListener={(node, done) =>
                node.addEventListener("transitionend", done, false)
              }
              classNames="fade"
            >
              {tether ? (
                <div className={styles.plans}>
                  {plans.filter(
                    (item: any) => item.teaserType === "Custom Overage",
                  ).length > 0 ? (
                    plans
                      .filter(
                        (item: any) => item.teaserType === "Custom Overage",
                      )
                      .map((plan: any) => (
                        <RatePlan
                          plan={plan}
                          activePlan={activePlan}
                          setActivePlan={setActivePlan}
                        />
                      ))
                  ) : (
                    <div className={styles.noPlans}>
                      <span>
                        There aren't any tether plans available for this IMEI
                      </span>
                      <img src="/none_found.svg" />
                    </div>
                  )}
                </div>
              ) : (
                <div className={styles.plans}>
                  {plans.filter(
                    (item: any) => item.teaserType !== "Custom Overage",
                  ).length > 0 ? (
                    plans
                      .filter(
                        (item: any) => item.teaserType !== "Custom Overage",
                      )
                      .map((plan: any) => (
                        <RatePlan
                          plan={plan}
                          activePlan={activePlan}
                          setActivePlan={setActivePlan}
                          noTether
                        />
                      ))
                  ) : (
                    <div className={styles.noPlans}>
                      <span>
                        There aren't any no tether plans available for this IMEI
                      </span>
                      <img src="/none_found.svg" />
                    </div>
                  )}
                </div>
              )}
            </CSSTransition>
          </SwitchTransition>
        </div>
      ) : activeSection === "summary" ? (
        <div className={styles.imeiMain}>
          <h3 style={{ marginBottom: 32 }}>Activate Subscriber</h3>
          <div className={styles.summaryContainer}>
            <div className={styles.summarySection}>
              <div className={styles.label}>IMEI:</div>
              <div className={styles.data}>{data.imei}</div>
              <div
                onClick={() => {
                  setActiveSection("enter-imei");
                }}
                className={styles.editButton}
              >
                <Pencil />
              </div>
            </div>
            <div className={styles.summarySection}>
              <div className={styles.label}>Plan:</div>
              <div className={styles.data}>{activePlan.offerName}</div>
              <div
                onClick={() => {
                  setActiveSection("select-plan");
                }}
                className={styles.editButton}
              >
                <Pencil />
              </div>
            </div>
            <div className={styles.summarySection}>
              <div className={styles.label}>Carrier:</div>
              <div className={styles.data}>AT&T</div>
            </div>
          </div>
          <Input
            label={labels.iccid}
            placeholder={placeholders.iccid}
            value={iccid.iccid}
            onChange={(e: any) => {
              handleInputChange("iccid", e, iccid, setIccid);
            }}
            error={iccid.errors.iccid}
            clear={() => {
              clearInput("iccid", setIccid);
            }}
            disabled={loading}
          />
        </div>
      ) : (
        ""
      )}
    </Modal>
  );
};

export default ActivateSubscriberModal;

const root = getComputedStyle(document.getElementById("root")!);

const SelectPlanSubtitle = ({ tether, setTether }: any) => (
  <div className={styles.planType}>
    <div>Select a plan</div>
    <div
      className={styles.planSwitch}
      style={{
        backgroundColor: tether
          ? root.getPropertyValue("--faded-orange")
          : "#E0DCDC",
      }}
      onClick={() => {
        setTether((prev: boolean) => !prev);
      }}
    >
      <div className={styles.label} style={{ color: tether ? "#fff" : "#000" }}>
        Tether Plans
      </div>
      <div
        className={styles.label}
        style={{ color: !tether ? "#fff" : "#000" }}
      >
        No Tether Plans
      </div>
      <div
        className={styles.thumb}
        style={{
          right: tether ? "163px" : "0px",
          backgroundColor: tether
            ? root.getPropertyValue("--orange")
            : "#1a1a1a",
        }}
      />
    </div>
  </div>
);

const filterInvalidRetailPlans = (plans: any[]) => {
  return plans.filter((plan: any) => {
    return Boolean(parseFloat(plan.retailPrice || ""));
  });
};
