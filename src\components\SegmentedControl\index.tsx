import React from "react";
import styles from "./segmented-control.module.scss";

interface Option {
  label: string;
  value: string;
}

interface SegmentedControlProps {
  options: Option[];
  selectedValue: string;
  onChange: (value: string) => void;
}

const SegmentedControl: React.FC<SegmentedControlProps> = ({
  options,
  selectedValue,
  onChange,
}) => {
  const handleOptionClick = (value: string) => {
    onChange(value);
  };

  return (
    <div className={styles.segmentedControl}>
      {options.map(({ label, value }) => (
        <button
          key={value}
          className={`${styles.option} ${
            selectedValue === value ? styles.selected : ""
          }`}
          onClick={() => handleOptionClick(value)}
        >
          {label}
        </button>
      ))}
    </div>
  );
};

export default SegmentedControl;
