import { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ApiDelete, ApiGet, ApiPut } from "../../pages/api/api";
import styles from "./manage-family.module.scss";
import Button from "../Button";
import { GearIcon, Plus, TrashIcon, WrenchIcon } from "../svgs";
import Spinner from "../Spinner";
import AddFamilyPlanMembersModal from "../AddFamilyPlanMembersModal/add-family-plan-members-modal";
import { ControlledMenu } from "@szhsin/react-menu";
import Modal from "../Modal";
import { useDispatch } from "react-redux";

type FamilyMember = {
  subscriberName: string;
  subscriptionId: number;
  email: string;
  subscriberNumber: string;
  offerName: string;
  size: string;
  retailPrice: string;
  mid: number;
};

type FamilyPlanData = {
  planId: number;
  adminSubscriptionId: number;
  adminMid: number;
  planName: string;
  discountPercentage: number;
  subscriptions: FamilyMember[];
};

const ManageFamily = ({ planId }: { planId: number }) => {
  const { mvnoId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [familyPlan, setFamilyPlan] = useState<FamilyPlanData | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchFamilyPlanData = () => {
    setLoading(true);
    ApiGet(`/accounts/familyplan/${planId}`)
      .then((response) => {
        setFamilyPlan(response.data);
        setError(null);
      })
      .catch((err) => {
        console.error("Error fetching family plan data:", err);
        setError("Failed to load family plan data. Please try again later.");
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    fetchFamilyPlanData();
  }, [planId]);

  const [showAddFamilyPlanMembersModal, setShowAddFamilyPlanMembersModal] =
    useState(false);

  const WrapperWithAddMemberButton = useCallback(
    ({ children }: any) => {
      return (
        <div className={styles.sectionWrapperWithAddMemberButton}>
          <AddFamilyPlanMembersModal
            show={showAddFamilyPlanMembersModal}
            onClose={() => setShowAddFamilyPlanMembersModal(false)}
            familyPlanId={familyPlan?.planId}
            onComplete={fetchFamilyPlanData}
          />

          <div className={styles.addMemberBtn}>
            <Button
              onClick={() => {
                setShowAddFamilyPlanMembersModal(true);
              }}
            >
              <Plus />
              Add New Family Member
            </Button>
          </div>
          {children}
        </div>
      );
    },
    [showAddFamilyPlanMembersModal],
  );

  if (loading) {
    return (
      <WrapperWithAddMemberButton>
        <div className={styles.loadingContainer}>
          <Spinner />
        </div>
      </WrapperWithAddMemberButton>
    );
  }

  if (error) {
    return (
      <WrapperWithAddMemberButton>
        <div className={styles.errorMessage}>{error}</div>
      </WrapperWithAddMemberButton>
    );
  }

  if (!familyPlan) {
    return (
      <WrapperWithAddMemberButton>
        <div className={styles.errorMessage}>No family plan data found.</div>
      </WrapperWithAddMemberButton>
    );
  }

  return (
    <WrapperWithAddMemberButton>
      <div className={styles.container}>
        <div className={styles.membersGrid}>
          {familyPlan.subscriptions.map((memberSub) => {
            const isAdmin =
              memberSub.subscriptionId === familyPlan.adminSubscriptionId;

            return (
              <div key={memberSub.subscriptionId} className={styles.memberCard}>
                <div className={styles.memberHeader}>
                  <div className={styles.memberHeaderLeft}>
                    <span className={styles.memberName}>
                      {memberSub.subscriberName}
                    </span>
                    <div className={styles.memberEmail}>{memberSub.email}</div>
                  </div>
                  {isAdmin ? (
                    <span className={styles.adminBadge}>Admin</span>
                  ) : (
                    <>
                      <FamilyMemberManageMenu
                        subscription={memberSub}
                        familyPlanId={familyPlan.planId}
                        isAdmin={isAdmin}
                        fetchFamilyPlanData={fetchFamilyPlanData}
                      />
                    </>
                  )}
                </div>
                <div className={styles.subscriptionDetails}>
                  <div className={styles.subscriptionTitle}>
                    Subscription Title
                  </div>
                  <div className={styles.planName}>{memberSub.offerName}</div>
                </div>
                <Button
                  color="secondary"
                  className={styles.accountBtn}
                  onClick={() => {
                    navigate(
                      `/${mvnoId}/subscription-details/${memberSub.mid}/${memberSub.subscriptionId}`,
                    );
                  }}
                >
                  Go to Account
                </Button>
              </div>
            );
          })}
        </div>
      </div>
    </WrapperWithAddMemberButton>
  );
};

export default ManageFamily;

const FamilyMemberManageMenu = ({
  subscription,
  familyPlanId,
  isAdmin,
  fetchFamilyPlanData,
}: {
  subscription: FamilyMember;
  familyPlanId: number;
  isAdmin: boolean;
  fetchFamilyPlanData: () => void;
}) => {
  const dispatch = useDispatch();
  const [showMenu, setShowMenu] = useState(false);
  const anchorRef = useRef<HTMLButtonElement | null>(null);

  const [showRemoveMemberConfirmation, setShowRemoveMemberConfirmation] =
    useState(false);
  const [showSetAsAdminConfirmation, setShowSetAsAdminConfirmation] =
    useState(false);

  const [removeMemberLoading, setRemoveMemberLoading] = useState(false);
  const [setAsAdminLoading, setSetAsAdminLoading] = useState(false);

  const handleRemoveMember = () => {
    setRemoveMemberLoading(true);
    ApiDelete(`/accounts/familyplan/remove-member`, {
      familyPlanId,
      memberSubscriptionId: subscription.subscriptionId,
    })
      .then((response) => {
        setRemoveMemberLoading(false);
        setShowRemoveMemberConfirmation(false);
        fetchFamilyPlanData();
        setShowMenu(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      })
      .catch((error) => {
        setRemoveMemberLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  const handleSetAsAdmin = () => {
    setSetAsAdminLoading(true);
    ApiPut(`/accounts/familyplan/update-admin`, {
      familyPlanId,
      newAdminSubscriptionId: subscription.subscriptionId,
    })
      .then((response) => {
        setSetAsAdminLoading(false);
        setShowSetAsAdminConfirmation(false);
        fetchFamilyPlanData();
        setShowMenu(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      })
      .catch((error) => {
        setSetAsAdminLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <>
      <button
        className={styles.manageBtn}
        ref={anchorRef}
        onClick={() => {
          setShowMenu(!showMenu);
        }}
      >
        <GearIcon />
        <span>Manage</span>
      </button>

      <ControlledMenu
        state={showMenu ? "open" : "closed"}
        anchorRef={anchorRef}
        onClose={() => setShowMenu(false)}
        position="auto"
        align="center"
        direction="bottom"
        overflow="auto"
        className={"manage-family-sub-dropdown"}
      >
        <div className={styles.manageMenuItems}>
          <button
            onClick={() => {
              setShowRemoveMemberConfirmation(true);
              setShowMenu(false);
            }}
          >
            <TrashIcon />
            <span>Remove Member</span>
          </button>
          {!isAdmin && (
            <button
              onClick={() => {
                setShowSetAsAdminConfirmation(true);
                setShowMenu(false);
              }}
            >
              <WrenchIcon />
              <span>Set as Admin</span>
            </button>
          )}
        </div>
      </ControlledMenu>

      <RemoveMemberConfirmationModal
        show={showRemoveMemberConfirmation}
        onClose={() => setShowRemoveMemberConfirmation(false)}
        onConfirm={handleRemoveMember}
        removeMemberLoading={removeMemberLoading}
        subscription={subscription}
      />

      <SetAsAdminConfirmationModal
        show={showSetAsAdminConfirmation}
        onClose={() => setShowSetAsAdminConfirmation(false)}
        onConfirm={handleSetAsAdmin}
        setAsAdminLoading={setAsAdminLoading}
        subscription={subscription}
      />
    </>
  );
};

const RemoveMemberConfirmationModal = ({
  show,
  onClose,
  onConfirm,
  removeMemberLoading,
  subscription,
}: {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  removeMemberLoading: boolean;
  subscription: FamilyMember;
}) => {
  return (
    <Modal
      show={show}
      close={onClose}
      proceed={onConfirm}
      image="/bulk_edit_confirm_graphic.svg"
      saveButton="Yes, remove member"
      cancelButton="No"
      loading={removeMemberLoading}
    >
      <div className={styles.confirmModalContent}>
        <h4 className={styles.confirmMainText}>
          Are you sure you want to remove this member subscription from the
          family plan?
        </h4>
        <div className={styles.info}>
          <p>Subscriber Name: {subscription.subscriberName}</p>
          <p>Email: {subscription.email}</p>
          <p>Product: {subscription.offerName}</p>
        </div>
      </div>
    </Modal>
  );
};

const SetAsAdminConfirmationModal = ({
  show,
  onClose,
  onConfirm,
  setAsAdminLoading,
  subscription,
}: {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
  setAsAdminLoading: boolean;
  subscription: FamilyMember;
}) => {
  return (
    <Modal
      show={show}
      close={onClose}
      proceed={onConfirm}
      image="/bulk_edit_confirm_graphic.svg"
      saveButton="Yes, set as admin"
      cancelButton="No"
      loading={setAsAdminLoading}
    >
      <div className={styles.confirmModalContent}>
        <h4 className={styles.confirmMainText}>
          Are you sure you want to set this member as the admin?
        </h4>
        <div className={styles.info}>
          <p>Subscriber Name: {subscription.subscriberName}</p>
          <p>Email: {subscription.email}</p>
          <p>Product: {subscription.offerName}</p>
        </div>
      </div>
    </Modal>
  );
};
