@use "../../styles/theme.scss" as *;

.box {
  height: 36px;
  width: 100%;
  background-color: $skeleton;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.flex {
  display: flex;
  align-items: center;
}

.pill {
  height: 36px;
  width: 100px;
  background-color: $skeleton;
  border-radius: 50%;
  overflow: hidden;
}

.plansGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  align-items: start;
  gap: 22px;
  margin-top: 16px;
}

.planTile {
  background: white;
  border-radius: 24px;
  padding: 24px;
}

.planProps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(128px, 1fr));
  column-gap: 24px;
  row-gap: 16px;
}
