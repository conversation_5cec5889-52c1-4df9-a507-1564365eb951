import { useState } from "react";
import { Input } from "../Input";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import styles from "./change-bill-cycle-modal.module.scss";
import SingleDatePicker from "../SingleDatePicker";

const ChangeBillCycleModal = ({ show, setShow }: any) => {
  const handleChange = () => {};

  const [date, setDate] = useState(null as any);

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      proceed={handleChange}
      close={() => {
        setShow("");
      }}
    >
      <div className={styles.main}>
        <h4>Change Bill Cycle</h4>
        <SingleDatePicker
          date={date}
          setDate={setDate}
          label="New Cycle Date"
        />
      </div>
    </Modal>
  );
};

export default ChangeBillCycleModal;
