import styles from "./topup.module.scss";

const Topup = ({ activePlan, setActivePlan, plan }: any) => {
  return (
    <div
      className={`${styles.main} ${activePlan === plan.name && styles.active}`}
      onClick={() => {
        setActivePlan(plan.name);
      }}
    >
      <div className={styles.name}>{plan.name}</div>
      <div className={styles.description}>{plan.description}</div>
    </div>
  );
};

export default Topup;
