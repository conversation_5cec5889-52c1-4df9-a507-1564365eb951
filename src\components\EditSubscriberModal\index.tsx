import styles from "./edit-subscriber.module.scss";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import { Input } from "../Input";
import SelectInput from "../SelectInput";
import { useEffect, useState } from "react";
import { validateAll } from "indicative/src/Validator";
import { useDispatch, useSelector } from "react-redux";
import { ApiGet, ApiPatch, ApiPostAuth, ApiPut } from "../../pages/api/api";
import Toggle from "../Toggle";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import { useParams } from "react-router-dom";
import SelectDropdown from "../SelectDropdown";
import { CircularProgress } from "@mui/material";

const fields = ["firstName", "lastName", "email", "contactNumber", "channel"];
const addressFields = [
  "streetNumber",
  "streetDirection",
  "streetName",
  "city",
  "state",
  "zipCode",
];
const rules = getRules(fields);
const messages = getMessages(fields);

const EditSubscriberModal = ({
  show,
  setShow,
  subscriber,
  repopulate,
  fromListingTable,
}: any) => {
  const dispatch = useDispatch();

  const [data, setData] = useState(createStateObject(fields, " "));
  const [addressData, setAddressData] = useState(
    createStateObject(addressFields, " "),
  );
  const [twoFactor, setTwoFactor] = useState(false);

  const { id, mvnoId } = useParams();

  const { userInfo } = useSelector((state: any) => state);

  const [channels, setChannels] = useState([]);

  const [dataLoading, setDataLoading] = useState(false);

  // Populate with current user's data
  useEffect(() => {
    if (subscriber && show) {
      if (fromListingTable) {
        setDataLoading(true);
        ApiGet("/accounts/by-account/" + subscriber.mid)
          .then((response) => {
            const subData = response.data;
            setData({
              ...data,
              firstName: subData.subscriberFirstName,
              lastName: subData.subscriberLastName,
              email: subData.email,
              contactNumber: subData.contactNumber,
              channel: subData.channelId
                ? {
                    label: subData.channelName,
                    value: subData.channelId,
                  }
                : "",
            });
            setTwoFactor(subData.mfa);
            setAddressData({
              ...subData.address,
            });
            setDataLoading(false);
          })
          .catch((error) => {
            setDataLoading(false);
            setShow("");
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      } else {
        setData({
          ...data,
          firstName: subscriber.subscriberFirstName,
          lastName: subscriber.subscriberLastName,
          email: subscriber.email,
          contactNumber: subscriber.contactNumber,
          channel: subscriber.channelId
            ? {
                label: subscriber.channelName,
                value: subscriber.channelId,
              }
            : "",
        });
        setTwoFactor(subscriber.mfa);
        setAddressData({
          ...subscriber.address,
        });
      }
    }
  }, [subscriber, show]);

  // Populate channel selection
  useEffect(() => {
    ApiGet(`/channels/mvno/${mvnoId}`)
      .then((response) => {
        setChannels(
          response.data.map((channel: any) => ({
            value: channel.id,
            label: channel.name,
          })),
        );
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  // Reset modal data when closed
  const reset = () => {
    setTimeout(() => {
      setData(createStateObject(fields, " "));
    }, 300);
    setLoading(false);
    setShow(false);
  };

  // Handles creation of new user
  const editUser = () => {
    const testData = {
      firstName: data.firstName.trim(),
      lastName: data.lastName.trim(),
      email: data.email.trim(),
      contactNumber: data.contactNumber.trim(),
      channel: data.channel.value,
    };

    const handleSuccess = (response: any, onSuccess: any = null) => {
      if (onSuccess) {
        onSuccess();
      } else {
        repopulate();
        reset();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      }
    };

    const handleError = (error: any) => {
      setLoading(false);
      dispatch({
        type: "notify",
        payload: {
          error: true,
          message: error.response.data.message,
        },
      });
    };

    validateAll(testData, rules, messages)
      .then((response) => {
        setLoading(true);
        const request = {
          firstName: testData.firstName,
          lastName: testData.lastName,
          email: testData.email,
          phoneNumber: testData.contactNumber,
        };
        ApiPatch(
          `/accounts/${fromListingTable ? subscriber.mid : id}/edit`,
          request,
        )
          .then((response) => {
            if (
              data.channel.value !== subscriber.channelId &&
              twoFactor !== subscriber.mfa
            ) {
              ApiPut(
                `/channels/${data.channel.value}/subscribers/${fromListingTable ? subscriber.mid : id}`,
              )
                .then(() => {
                  ApiPut("/accounts/2fa", {
                    accountMid: id,
                    enable: twoFactor,
                  })
                    .then(handleSuccess)
                    .catch(handleError);
                })
                .catch(handleError);
            } else if (data.channel.value !== subscriber.channelId) {
              ApiPut(
                `/channels/${data.channel.value}/subscribers/${fromListingTable ? subscriber.mid : id}`,
              )
                .then(handleSuccess)
                .catch(handleError);
            } else if (twoFactor !== subscriber.mfa) {
              ApiPut("/accounts/2fa", {
                accountMid: id,
                enable: twoFactor,
              })
                .then(handleSuccess)
                .catch(handleError);
            } else {
              handleSuccess(response);
            }
          })
          .catch(handleError);
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const [loading, setLoading] = useState(false);

  const handleChannelChange = (selectedOption: any) => {
    setData({
      ...data,
      channel: selectedOption,
      errors: { ...data.errors, channel: "" },
    });
  };

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={editUser}
      close={reset}
      loading={loading}
      fullSize
      title="Edit Subscriber Details"
      scroll
    >
      {dataLoading ? (
        <div className={styles.main} style={{ display: "flex" }}>
          <CircularProgress style={{ color: "#F47D27", margin: "0 auto" }} />
        </div>
      ) : (
        <div className={styles.main}>
          {fields.map((prop) => {
            if (prop === "channel") {
              if (userInfo.roleName !== "Agent") {
                return (
                  <SelectDropdown
                    key="channel-select"
                    value={data.channel}
                    error={data.errors.channel}
                    onChange={handleChannelChange}
                    placeholder="Channel"
                    options={channels}
                    disabled={loading}
                    white
                  />
                );
              }
            } else {
              return (
                <Input
                  key={`edit-user-${prop}`}
                  label={labels[prop]}
                  placeholder={placeholders[prop]}
                  value={data[prop]}
                  onChange={(e: any) => {
                    handleInputChange(prop, e, data, setData);
                  }}
                  error={data.errors[prop]}
                  onKeyDown={editUser}
                  clear={() => {
                    clearInput(prop, setData);
                  }}
                  disabled={loading}
                  white
                />
              );
            }
          })}
          <div
            style={{ marginTop: !self ? 12 : 0, opacity: loading ? 0.3 : 1 }}
            className={styles.twoFactor}
          >
            <div className={styles.twoFaLabel}>Two-Factor Authentication</div>
            <div className={styles.toggleContainer}>
              <div className={styles.onOff}>{twoFactor ? "On" : "Off"}</div>
              <Toggle
                on={twoFactor}
                onChange={() => {
                  setTwoFactor((prev: boolean) => !prev);
                }}
                disabled={loading}
              />
            </div>
          </div>
          {addressFields.map((prop) => (
            <Input
              key={`edit-user-${prop}`}
              label={labels[prop]}
              placeholder={placeholders[prop]}
              value={addressData[prop]}
              onChange={(e: any) => {
                handleInputChange(prop, e, addressData, setAddressData);
              }}
              clear={() => {
                clearInput(prop, setAddressData);
              }}
              disabled
              white
            />
          ))}
        </div>
      )}
    </Modal>
  );
};

export default EditSubscriberModal;
