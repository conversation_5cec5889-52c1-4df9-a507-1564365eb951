import { useState } from "react";
import Modal from "../Modal";
import RatePlan from "../RatePlan";
import styles from "./select-plan-modal.module.scss";
import SearchBar from "../SearchBar";

const SelectPlanModal = ({ show, setShow, setSelectedPlan, plans }: any) => {
  const [activePlan, setActivePlan] = useState({
    OfferId: -1,
    OfferName: "",
  } as any);

  const [searchQuery, setSearchQuery] = useState("");

  const handleAddPlan = () => {
    if (activePlan.OfferId !== -1) {
      setSelectedPlan(activePlan);
      setShow(false);
    }
  };

  const handleSearch = () => {};

  console.log(plans);

  return (
    <Modal
      scroll
      saveButton="Select Subscription"
      cancelButton="Cancel"
      show={show}
      proceed={handleAddPlan}
      image="/search_graphic.svg"
      close={() => {
        setShow(false);
      }}
      fullSize
      title={
        /*<div className={styles.searchBar}>
          <div className={styles.title}>Subscription</div>
          <SearchBar
            placeholder="Search for a subscription"
            query={searchQuery}
            setQuery={setSearchQuery}
            onSubmit={handleSearch}
            grey
            small
          />
        </div>*/
        <div style={{ width: "100%", textAlign: "start" }}>Subscription</div>
      }
      clearContainer
    >
      <div className={styles.plansMain}>
        <div className={styles.plans}>
          {plans.map((plan: any) => (
            <RatePlan
              plan={plan}
              activePlan={activePlan}
              setActivePlan={setActivePlan}
              tether={false}
              update
            />
          ))}
        </div>
      </div>
    </Modal>
  );
};

export default SelectPlanModal;
