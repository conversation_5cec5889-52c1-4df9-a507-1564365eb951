import { useEffect, useRef, useState } from "react";
import styles from "./tooltip.module.scss";
import { createPortal } from "react-dom";

const Tooltip = ({ show, text, white, style = {}, children }: any) => {
  const ref = useRef(null);

  const tooltipRef = useRef(null);

  const [position, setPosition] = useState({ top: 0, left: 0 });

  const [hovering, setHovering] = useState(false);

  const reposition = () => {
    const container = (ref.current as any).getBoundingClientRect();
    const tool = tooltipRef.current as any;
    if (tool) {
      const toolPosition = tool.getBoundingClientRect();
      setPosition({
        top: container.top - toolPosition.height - 15,
        left: container.left - toolPosition.width / 2 + container.width / 2,
      });
    } else {
      setPosition({
        top: container.top - container.height - 15,
        left: container.left,
      });
    }
    (ref.current as any).onmouseenter = () => {
      setHovering(true);
    };
    (ref.current as any).onmouseleave = () => {
      setHovering(false);
    };
  };

  // call your useEffect
  /*useEffect(() => {
    reposition();
    window.addEventListener("resize", reposition);
    const els = document.getElementsByClassName("table-scroll");
    for (let i = 0; i < els.length; i++) {
      els[i].addEventListener("scroll", reposition);
    }
    return () => {
      window.removeEventListener("resize", reposition);
      for (let i = 0; i < els.length; i++) {
        els[i].removeEventListener("scroll", reposition);
      }
    };
  }, []);*/

  return (
    <span
      className={styles.container}
      style={style}
      ref={ref}
      onMouseEnter={reposition}
    >
      {show &&
        createPortal(
          <div
            className={`${styles.tooltip} ${
              white && styles.white
            } tooltip-highlight`}
            style={{
              top: position.top,
              left: position.left,
              opacity: hovering ? 1 : 0,
            }}
            ref={tooltipRef}
          >
            {text}
            <div className={styles.triangle}>
              <svg
                width="18"
                height="15"
                viewBox="0 0 18 15"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M10.7318 13.5C9.96203 14.8333 8.03753 14.8333 7.26773 13.5L1.20555 3C0.435752 1.66667 1.398 5.37701e-07 2.9376 6.72297e-07L15.062 1.73224e-06C16.6016 1.86684e-06 17.5638 1.66667 16.794 3L10.7318 13.5Z"
                  fill="currentColor"
                />
              </svg>
            </div>
          </div>,
          document.getElementById("root")!,
        )}
      {children}
    </span>
  );
};

export default Tooltip;
