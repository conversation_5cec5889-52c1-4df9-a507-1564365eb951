import styles from "./select-input.module.scss";
import {
  ControlledMenu,
  MenuItem,
  useMenuState,
  useClick,
} from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown } from "../svgs";
import { useEffect, useRef, useState } from "react";
import { Collapse } from "@mui/material";
import $ from "jquery";
import { createPortal } from "react-dom";

const SelectInput = ({
  options,
  selected,
  placeholder,
  onChange,
  readonly,
  disabled,
  id = null,
  error,
}: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });
  const anchorProps = useClick(menuProps.state, toggleMenu);

  const [openUpwards, setOpenUpwards] = useState(false);

  const isOptionObject = typeof options[0] === "object";

  const _selected = isOptionObject
    ? options.find((opt: any) => opt.value === selected)?.label
    : selected;

  useEffect(() => {
    if ($(".normal-select-input ul.szh-menu")) {
      let spaceBottom =
        $(window).height()! - $(".select-input").offset()!.top - 56;
      let menuHeight = $(".normal-select-input ul.szh-menu").outerHeight()!;
      if (spaceBottom < menuHeight) {
        setOpenUpwards(true);
      } else {
        setOpenUpwards(false);
      }
    }
  }, [menuProps]);

  return (
    <div
      className={`${styles.box} normal-select-input ${
        openUpwards && "select-upwards"
      }`}
    >
      <div
        ref={ref}
        {...anchorProps}
        className={`${styles.menuButton} ${_selected && styles.selected} ${
          (disabled || readonly) && styles.disabled
        } ${readonly && styles.readonly} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        } ${error && styles.error} select-input`}
        onClick={() => toggleMenu(true)}
      >
        <span style={{ marginRight: 4 }}>{_selected || placeholder}</span>
        {!readonly && <ChevronDown />}
      </div>
      <Collapse in={error ? true : false}>
        <p className={styles.errorText} id={`${id}-error`}>
          {error || <br />}
        </p>
      </Collapse>
      {createPortal(
        <div className="normal-select-input">
          <ControlledMenu
            {...menuProps}
            anchorRef={ref}
            onClose={() => toggleMenu(false)}
            align="start"
            position="auto"
            viewScroll="auto"
            direction={openUpwards ? "top" : "bottom"}
            onItemClick={(e) => (e.keepOpen = true)}
          >
            {options.map((item: any) => {
              const label = isOptionObject ? item.label : item;
              const value = isOptionObject ? item.value : item;

              return (
                <MenuItem
                  className={`${styles.menuItem} ${
                    _selected === value && styles.selected
                  }`}
                  onClick={() => {
                    onChange(value);
                    toggleMenu(false);
                  }}
                  key={value}
                >
                  {label}
                </MenuItem>
              );
            })}
          </ControlledMenu>
        </div>,
        document.getElementById("root")!,
      )}
    </div>
  );
};

export default SelectInput;
