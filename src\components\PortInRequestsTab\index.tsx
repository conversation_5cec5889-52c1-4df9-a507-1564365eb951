import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { useDispatch } from "react-redux";
import { ApiGetSubscriber } from "../../pages/api/api";
import PortingLine from "../PortingLine";
import PortingLoading from "../PortingLoading";
import CancelPortModal from "../CancelPortModal";
import UpdatePortIn from "../UpdatePortIn";
import PortInErrorModal from "../PortInErrorModal";
import styles from "./port-in-requests-tab.module.scss";
import Button from "../Button";

type PortInRequestsTabProps = {
  subscriberInfo: any;
  fetchSubscriberInfo: (after: () => void) => void;
};

const PortInRequestsTab = ({
  subscriberInfo,
  fetchSubscriberInfo,
}: PortInRequestsTabProps) => {
  const { id } = useParams();
  const dispatch = useDispatch();

  const [portins, setPortins] = useState([] as any);
  const [portinsLoading, setPortinsLoading] = useState(true);
  const [portinsError, setPortinsError] = useState(false);
  const [currentModal, setCurrentModal] = useState("");
  const [activePort, setActivePort] = useState(null as any);

  const fetchPortins = () => {
    setPortinsLoading(true);
    setPortinsError(false);
    ApiGetSubscriber(`/accounts/by-account/${id}/portins`, "active")
      .then((response) => {
        setPortinsLoading(false);
        setPortins(response.data.content);
      })
      .catch((error) => {
        setPortinsLoading(false);
        setPortinsError(true);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message:
              error.response?.data?.message ||
              "Failed to load port-in requests",
          },
        });
      });
  };

  const repopulate = async (after = () => {}) => {
    fetchPortins();
    fetchSubscriberInfo(after);
  };

  useEffect(() => {
    fetchPortins();
  }, []);

  return (
    <>
      <CancelPortModal
        show={currentModal === "cancel-port"}
        setShow={setCurrentModal}
        repopulate={repopulate}
        portData={activePort}
      />
      {subscriberInfo && (
        <UpdatePortIn
          show={currentModal === "update-port"}
          setShow={setCurrentModal}
          updating
          accountDataProvided
          msisdn={activePort ? activePort.attDetails?.msisdn : ""}
          zipCode={activePort ? activePort.zipCode : ""}
          portData={activePort}
          repopulate={repopulate}
        />
      )}
      <PortInErrorModal
        show={currentModal === "view-port-fail"}
        setShow={setCurrentModal}
        error={activePort ? activePort.failureReason : ""}
        msisdn={activePort ? activePort.attDetails?.msisdn : ""}
        zipCode={activePort ? activePort.zipCode : ""}
        portingLineView
        handleFinish={() => {
          setCurrentModal("");
        }}
      />

      <div className={`${styles.container} modal-scroll`}>
        <h2 className={styles.title}>Port-in Requests</h2>

        {portinsError && (
          <div className={styles.errorContainer}>
            <p>Failed to load port-in requests. Please try again.</p>
            <Button className={styles.retryButton} onClick={fetchPortins}>
              Retry
            </Button>
          </div>
        )}

        {!portinsError && (
          <div className={styles.portingGrid}>
            {portinsLoading ? (
              Array.from({ length: 3 }).map((_, index) => (
                <PortingLoading key={`loading-${index}`} />
              ))
            ) : portins.length > 0 ? (
              portins.map((port: any) => (
                <PortingLine
                  key={port.id || port.subscriberNumber}
                  port={port}
                  setCurrentModal={setCurrentModal}
                  setActivePort={setActivePort}
                  repopulate={repopulate}
                />
              ))
            ) : (
              <div className={styles.noPortins}>
                <img src="/none_found.svg" alt="No port-in requests found" />
                <h3>No port-in requests found</h3>
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};

export default PortInRequestsTab;
