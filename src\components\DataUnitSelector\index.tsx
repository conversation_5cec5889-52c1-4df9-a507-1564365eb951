import { useRef } from "react";
import styles from "./data-unit-selector.module.scss";
import { ControlledMenu, useMenuState } from "@szhsin/react-menu";
import { ChevronDown } from "../svgs";

const DataUnitSelector = ({ label, unit, setUnit }: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const unitList = ["KB", "MB", "GB"];

  return (
    <div className={styles.box}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${
          menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
        }`}
        onClick={() => toggleMenu(true)}
      >
        {label}
        <ChevronDown />
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => toggleMenu(false)}
        align="start"
        position="auto"
        viewScroll="close"
        onItemClick={(e) => (e.keepOpen = true)}
      >
        <div className={styles.container}>
          <div className={styles.switchContainer}>
            {unitList.map((unitItem: string) => (
              <div
                className={`${styles.unit} ${
                  unit === unitItem && styles.active
                }`}
                onClick={() => {
                  setUnit(unitItem);
                }}
              >
                {unitItem}
              </div>
            ))}
            <div
              className={styles.thumb}
              style={{
                left:
                  unit === "KB"
                    ? 0
                    : unit === "MB"
                    ? 67
                    : unit === "GB"
                    ? 134
                    : 0,
              }}
            />
          </div>
        </div>
      </ControlledMenu>
    </div>
  );
};

export default DataUnitSelector;
