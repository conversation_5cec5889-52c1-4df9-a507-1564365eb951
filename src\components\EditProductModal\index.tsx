import { useState, useEffect } from "react";
import Modal from "../Modal";
import styles from "./edit-product-modal.module.scss";
import { Input } from "../Input";
import {
  clearInput,
  createStateObject,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import { validateAll } from "indicative/validator";
import { displayErrors } from "../utils/InputHandlers";
import { FloppyDisk } from "../svgs";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { ApiPatch, ApiPut } from "../../pages/api/api";
import TextArea from "../TextArea";
import _ from "underscore";

const fields = ["commercialName", "commercialPrice", "commercialDescription"];
const rules = getRules(fields);
const messages = getMessages(fields);

const EditProductModal = ({ show, setShow, product, repopulate }: any) => {
  const dispatch = useDispatch();

  const { mvnoId } = useParams();

  const [data, setData] = useState(createStateObject(fields));

  const [loading, setLoading] = useState(false);

  const reset = () => {
    setShow(false);
    setTimeout(() => {
      setData(createStateObject(fields));
      setLoading(false);
    }, 300);
  };

  useEffect(() => {
    if (product && show) {
      setData({
        ...data,
        commercialName: _.unescape(product.RetailName),
        commercialPrice: product.RetailPrice,
        commercialDescription: _.unescape(product.RetailDescritpion),
      });
    }
  }, [product, show]);

  const saveChanges = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPut("/products/update", {
          name: product.OfferName,
          retailName: data.commercialName,
          retailPrice: data.commercialPrice,
          retailDescription: data.commercialDescription,
        })
          .then((response) => {
            reset();
            repopulate();
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: "Product updated successfully!",
              },
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors: any) => {
        displayErrors(errors, setData);
      });
  };

  return (
    <Modal
      scroll
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      cancelButton="Cancel"
      show={show}
      proceed={saveChanges}
      image="/bulk_edit_confirm_graphic.svg"
      close={reset}
      loading={loading}
      onCancel={reset}
      fullSize
      title="Edit Product"
    >
      <div className={styles.imeiMain}>
        <div className={styles.summaryContainer}>
          <div className={styles.summarySection}>
            <div className={styles.label}>Family</div>
            <div className={styles.data}>{product?.ProductFamily}</div>
          </div>
          <div className={styles.summarySection}>
            <div className={styles.label}>Name</div>
            <div className={styles.data}>{product?.OfferName}</div>
          </div>
        </div>
        {fields.map((prop: string) => {
          if (prop !== "commercialDescription") {
            return (
              <Input
                white
                label={labels[prop]}
                placeholder={placeholders[prop]}
                value={data[prop]}
                onChange={(e: any) => {
                  handleInputChange(prop, e, data, setData);
                }}
                error={data.errors[prop]}
                clear={() => {
                  clearInput(prop, setData);
                }}
                disabled={loading}
                number={prop === "commercialPrice"}
              />
            );
          }
        })}
        <TextArea
          white
          label={labels.commercialDescription}
          placeholder={placeholders.commercialDescription}
          value={data.commercialDescription}
          onChange={(e: any) => {
            handleInputChange("commercialDescription", e, data, setData);
          }}
          error={data.errors.commercialDescription}
          clear={() => {
            clearInput("commercialDescription", setData);
          }}
          disabled={loading}
        />
      </div>
    </Modal>
  );
};

export default EditProductModal;
