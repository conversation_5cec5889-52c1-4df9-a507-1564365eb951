import { closestTo } from "date-fns";
import { churnReportFields, subscriptionReportFields } from "./reportFields";

export const subscriptionFields = [
  "activationDate",
  "carrier",
  "creationDate",
  "subscriberStatus",
  "ban",
  "imei",
  "subscriberNumber",
  "iccid",
  "dataUsed",
];

export const productFields = ["product", "productSize", "serviceType"];

// Encode quotes and commas to prevent column mix up
const encodeCommasAndQuotes = (string: string) => {
  return '"' + string.replaceAll('"', '""') + '"';
};

// Export all subs to csv file
export const exportCsv = (
  fields: any,
  data: any,
  dispatch: any,
  fileName?: string,
) => {
  let csvContent = `data:text/csv;charset=utf-8,${fields.map(
    (fieldItem: any) => fieldItem.labelStr,
  )}\n`;
  console.log({ data });
  if ("subscriptions" in data[0]) {
    data.forEach((sub: any) => {
      if (sub.subscriptions.length > 0) {
        sub.subscriptions.forEach((subscription: any) => {
          fields.forEach((fieldItem: any) => {
            if (subscriptionFields.includes(fieldItem.key)) {
              if (fieldItem.key === "dataUsed") {
                csvContent +=
                  encodeCommasAndQuotes(
                    subscription.totalDataUsageKB.toString(),
                  ) + ",";
              } else {
                if (subscription[fieldItem.key]) {
                  csvContent +=
                    encodeCommasAndQuotes(
                      subscription[fieldItem.key].toString(),
                    ) + ",";
                } else {
                  csvContent +=
                    encodeCommasAndQuotes(subscription[fieldItem.key]) + ",";
                }
              }
            } else if (productFields.includes(fieldItem.key)) {
              csvContent +=
                encodeCommasAndQuotes(
                  subscription.product[fieldItem.key].toString(),
                ) + ",";
            } else {
              csvContent +=
                encodeCommasAndQuotes(sub[fieldItem.key].toString()) + ",";
            }
          });
          csvContent += "\n";
        });
      } else {
        fields.forEach((fieldItem: any) => {
          if (
            subscriptionFields.includes(fieldItem.key) ||
            productFields.includes(fieldItem.key)
          ) {
            csvContent += "" + ",";
          } else {
            csvContent +=
              encodeCommasAndQuotes(sub[fieldItem.key].toString()) + ",";
          }
        });
        csvContent += "\n";
      }
    });
  } else if ("reportPlans" in data[0]) {
    data.forEach((item: any) => {
      if (item.reportPlans.length) {
        item.reportPlans.forEach((rpItem: any) => {
          fields.forEach((fieldItem: any) => {
            if (fieldItem.key === "offerName") {
              csvContent +=
                encodeCommasAndQuotes(rpItem.offerName.toString()) + ",";
            } else if (fieldItem.key === "offerSize") {
              csvContent +=
                encodeCommasAndQuotes(rpItem.offerSize.toString()) + ",";
            } else {
              csvContent +=
                encodeCommasAndQuotes(item[fieldItem.key].toString()) + ",";
            }
          });
        });
      } else if (item.reportPlans.length === 0) {
        fields.forEach((fieldItem: any) => {
          if (fieldItem.key === "offerName" || fieldItem.key === "offerSize") {
            csvContent += "" + ",";
          } else {
            csvContent +=
              encodeCommasAndQuotes(item[fieldItem.key].toString()) + ",";
          }
        });
      }
      csvContent += "\n";
    });
  } else {
    data.forEach((row: any) => {
      fields.forEach((field: any) => {
        csvContent +=
          encodeCommasAndQuotes((row[field.key] || "").toString()) + ",";
      });
      csvContent += "\n";
    });
  }
  const encodedUri = encodeURI(csvContent);
  const link = document.createElement("a");
  link.setAttribute("href", encodedUri);
  link.setAttribute("download", `${fileName || "subscriptions"}.csv`);
  document.body.appendChild(link);

  link.click();

  dispatch({
    type: "notify",
    payload: {
      message: ".CSV file exported.",
      error: false,
    },
  });
};

// Export all subs to csv file
export const exportCsvPlainFields = (
  fields: any,
  data: any,
  dispatch: any,
  filename: string,
  withKeys = false,
) => {
  let csvContent = withKeys
    ? `data:text/csv;charset=utf-8,${fields.map((item: any) => item.key)}\n`
    : `data:text/csv;charset=utf-8,${fields}\n`;
  data.forEach((item: any) => {
    fields.forEach((fieldItem: any) => {
      if (withKeys) {
        csvContent +=
          encodeCommasAndQuotes(item[fieldItem.key].toString() || " ") + ",";
      } else {
        csvContent += encodeCommasAndQuotes(item[fieldItem].toString()) + ",";
      }
    });
    csvContent += "\n";
  });
  const encodedUri = encodeURI(csvContent);
  const link = document.createElement("a");
  link.setAttribute("href", encodedUri);
  link.setAttribute("download", filename + ".csv");
  document.body.appendChild(link);

  link.click();

  dispatch({
    type: "notify",
    payload: {
      message: ".CSV file exported.",
      error: false,
    },
  });
};

// Export all subs to csv file
export const exportCsvNoFields = (
  data: any,
  dispatch: any,
  filename: string,
) => {
  const keys = Object.keys(data[0]);
  let csvContent = `data:text/csv;charset=utf-8,${keys}\n`;
  data.forEach((item: any) => {
    keys.forEach((key: any) => {
      csvContent += encodeCommasAndQuotes(item[key].toString()) + ",";
    });
    csvContent += "\n";
  });
  const encodedUri = encodeURI(csvContent);
  const link = document.createElement("a");
  link.setAttribute("href", encodedUri);
  link.setAttribute("download", filename + ".csv");
  document.body.appendChild(link);

  link.click();

  dispatch({
    type: "notify",
    payload: {
      message: ".CSV file exported.",
      error: false,
    },
  });
};
