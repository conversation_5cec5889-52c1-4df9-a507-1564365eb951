import { v4 as uuid } from "uuid";
import { faker } from "@faker-js/faker";

export const priorities = [
  { name: "Urgent", id: 4 },
  { name: "High", id: 3 },
  { name: "Medium", id: 2 },
  { name: "Low", id: 1 },
];
export const categories = [
  "API issue",
  "Billing Issues",
  "Complaints and Feedback",
  "Connectivity issue",
  "Data Report",
  "Data issue",
  "eSIM",
  "Feature Request",
  "Int’l Service",
  "Invoice",
  "Maintenance and Downtime",
  "Mobile App",
  "Payment",
  "Platform issue",
  "Provisioning issue",
  "SIM Ordering",
  "Service Request",
  "Voice issue",
];

// export const assignees = [
//   "<PERSON>",
//   "<PERSON>",
//   "<PERSON>",
//   "Malwina Roczniak",
// ];
export const ticketStatus = [
  { name: "Open", id: 2 },
  { name: "Pending", id: 3 },
  { name: "Resolved", id: 4 },
  { name: "Closed", id: 5 },
];
export const types = [
  "Support",
  "Support",
  "Support",
  "Support",
  "DID Request",
  "Porting",
];
