import styles from "./notification-skeleton.module.scss";
import Shimmer from "../Shimmer";

const NotificationSkeleton = () => {
  return (
    <div className={styles.main}>
      <div style={{ width: 300 }} className={styles.box}>
        <Shimmer />
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-end",
        }}
      >
        <div style={{ width: 150, marginBottom: 9 }} className={styles.box}>
          <Shimmer />
        </div>
        <div style={{ width: 200 }} className={styles.box}>
          <Shimmer />
        </div>
      </div>
    </div>
  );
};

export default NotificationSkeleton;
