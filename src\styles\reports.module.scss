@use "./theme.scss" as *;

.main {
  padding: 63px 40px;
  display: flex;
  flex-direction: column;
}

.topBar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
  margin-bottom: 24px;
  h2 {
    margin: 0;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
  }
}

.mainTile {
  width: 100%;
  background: #fff;
  padding: 27px 40px;
  border-radius: 24px;
  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 6px;
    white-space: nowrap;
    tbody {
      tr {
        height: 65px;
        background: #f7f6f6;
        td {
          font-size: 14px;
          font-weight: 400;
          line-height: 21px;
          padding: 0 16px;
        }
        td:first-child {
          border-radius: 12px 0 0 12px;
          padding-left: 24px;
        }
        td:last-child {
          border-radius: 0 12px 12px 0;
          padding-right: 24px;
        }
      }
      &:before {
        content: "@";
        display: block;
        line-height: 0px;
        text-indent: -99999px;
      }
    }
    thead {
      tr {
        th:first-child {
          padding-left: 24px;
        }
      }
    }
    th {
      font-size: 14px;
      font-weight: 600;
      line-height: 21px;
      text-align: start;
      border-bottom: 1px solid $disabled;
      padding: 0 16px;
      padding-bottom: 19px;
      vertical-align: top;
    }
  }
}

.tableContainer {
  overflow-x: auto;
  padding-bottom: 5px;
  padding-top: 27px;
  width: 100%;
}

.utilityBar {
  width: 100%;
  display: flex;
  align-items: center;
}

.fadeButtons {
  display: flex;
  align-items: center;
  overflow: hidden;
  transition: all 0.3s ease;
  width: 670px;
  &.hide {
    width: 0px;
  }
}

.button {
  font-size: 14px;
  height: 43px !important;
  padding: 0px 12px;
  min-width: initial;
  height: auto;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #1a1a1a;
  border-radius: 1000px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
  &:hover {
    background-color: rgba(0, 0, 0, 0.075);
  }
  &.active {
    background-color: $light-orange;
    border-color: $light-orange;
  }
  svg {
    margin: 0 10px;
  }
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.office {
  width: 715px;
  margin-top: 193px;
  margin-bottom: 55px;
}

.pagination {
  height: 75px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: auto;
}

.selectionWrapper {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.selection {
  height: 47px;
  border-radius: 1000px;
  color: $black;
  font-size: 14px;
  font-weight: 600;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    color: $dark-orange;
  }
  span {
    position: relative;
    z-index: 6;
  }
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1000px;
  background-color: $light-orange;
  z-index: 5;
  left: 0;
}

.activeSelection {
  cursor: auto;
  &:hover {
    color: $black;
  }
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 55px;
  img,
  h3 {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }
  h3 {
    width: 100%;
    text-align: center;
  }
}

.paymentsInsights {
  background: #fff;
  border-radius: 24px;
  box-shadow: 0px 0px 20px 0px #9e9e9e26;
  margin-bottom: 16px;
  padding: 18px 24px;
  position: relative;
  .paymentsTitleRow {
    width: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .paymentsHeading {
      font-size: 18px;
      font-weight: 700;
      line-height: 27px;
    }
    .rangeButtons {
      display: flex;
      align-items: center;
    }
  }
  .charts {
    display: grid;
    grid-template-columns: 1fr 174px auto;
    grid-column-gap: 60px;
    align-items: center;
    width: 100%;
    transition: opacity 0.2s ease;
    @media (max-width: 1200px) {
      grid-column-gap: 24px;
    }
    .lineChart {
      width: 99%;
    }
    .pending {
      width: 100%;
      background-color: #fce7cf;
      border-radius: 24px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 50px;
      .label {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        margin-bottom: 8px;
      }
      .amount {
        font-size: 26px;
        font-weight: 600;
        line-height: 39px;
      }
    }
  }
  .legendRow {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 24px;
    .indicator {
      width: 9px;
      height: 9px;
      border-radius: 25px;
      margin-right: 4px;
      &.successful {
        background-color: #06c183;
      }
      &.failed {
        background-color: #ea3d5c;
      }
      &.pending {
        background-color: #f2a446;
      }
    }
    .legend,
    .totals {
      display: flex;
      align-items: center;
    }
    .legend {
      .legendItem {
        display: flex;
        align-items: center;
        font-size: 12px;
        line-height: 18px;
        color: #1a1a1a;
        margin-right: 16px;
      }
    }
    .totals {
      font-size: 16px;
      line-height: 24px;
      color: #1a1a1a;
      .totalLabel {
        margin-right: 29px;
      }
      .totalItem {
        display: flex;
        align-items: center;
        margin-right: 29px;
        &:last-child {
          margin-right: 0px;
        }
      }
    }
  }
}
