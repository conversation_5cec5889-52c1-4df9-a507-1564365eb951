import styles from "./mdn-activity-response.module.scss";
import Modal from "../Modal";
// Using ES6 import syntax
import hljs from "highlight.js/lib/core";
import javascript from "highlight.js/lib/languages/javascript";
import { useEffect } from "react";

// Then register the languages you need
hljs.registerLanguage("javascript", javascript);

const MdnActivityResponse = ({ show, setShow, data }: any) => {
  useEffect(() => {
    hljs.highlightAll();
  }, [data, show]);

  return (
    <Modal
      cancelButton="Close Window"
      image="/view-user-Illustration.svg"
      show={show}
      setShow={setShow}
      close={setShow}
      onClose={() => setShow(false)}
    >
      <div className={`${styles.main}`}>
        <h3>Request details</h3>
        {data && data.url && (
          <pre style={{ marginBottom: 12 }}>
            <code>{JSON.stringify({ url: data?.url }, null, 4)}</code>
          </pre>
        )}
        {data && (
          <pre>
            <code>{JSON.stringify(data?.requestDetails, null, 4)}</code>
          </pre>
        )}
        <h3 style={{ marginTop: 50 }}>Response details</h3>
        {data && (
          <pre>
            <code>{JSON.stringify(data?.responseDetails, null, 4)}</code>
          </pre>
        )}
      </div>
    </Modal>
  );
};

export default MdnActivityResponse;
