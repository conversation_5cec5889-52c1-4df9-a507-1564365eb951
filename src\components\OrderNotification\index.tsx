import { Link, useNavigate, useParams } from "react-router-dom";
import NotificationReadIndicator from "../NotificationReadIndicator";
import formatDate from "../utils/formatDate";
import styles from "./order-notification.module.scss";
import { ArrowRight } from "../svgs";
import { useState } from "react";
import { motion } from "framer-motion";
import { useDispatch } from "react-redux";

const OrderNotification = ({ item, repopulate }: any) => {
  const { mvnoId } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [read, setRead] = useState(item.read);

  return (
    <motion.div
      layout
      animate="in"
      variants={{
        in: {
          opacity: 1,
        },
        out: {
          opacity: 0,
        },
      }}
      transition={{
        type: "tween",
        ease: "easeInOut",
        duration: 0.3,
      }}
      key={"motion-notification-" + item.id}
      className={styles.notifyPanel}
    >
      <div className={styles.top}>
        <div className={`${styles.title} ${read && styles.read}`}>
          <NotificationReadIndicator
            read={read}
            setRead={setRead}
            id={item.id}
            repopulate={repopulate}
          />
          <span style={{ marginLeft: 8 }}>
            {item.message} from {item.subscriberName}
          </span>
        </div>
        <div className={styles.date}>
          {item.createdAt && formatDate(item.createdAt)}
        </div>
      </div>
      <div className={styles.bottom} style={{ marginBottom: 12 }}>
        <Link to={`/${mvnoId}/orders/${item.message.replace(/[^0-9]/g, "")}`}>
          <div className={styles.sub}>
            <span style={{ marginRight: 8 }}>Order Details</span> <ArrowRight />
          </div>
        </Link>
      </div>
      <div className={styles.bottom}>
        <Link to={`/${mvnoId}/subscriber/${item.mid}`}>
          <div className={styles.sub}>
            <span style={{ marginRight: 8 }}>Subscriber Details</span>{" "}
            <ArrowRight />
          </div>
        </Link>
      </div>
    </motion.div>
  );
};

export default OrderNotification;
