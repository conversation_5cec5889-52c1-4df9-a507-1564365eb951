import { Link, useParams } from "react-router-dom";
import styles from "./notification-bell.module.scss";
import { Bell<PERSON><PERSON>, Bell } from "../svgs";
import { useDispatch, useSelector } from "react-redux";
import { useEffect } from "react";
import { ApiGetNoAuth } from "../../pages/api/api";

const NotificationBell = () => {
  const { mvnoId } = useParams();
  const dispatch = useDispatch();

  const { throttleNotifications } = useSelector((state: any) => state);

  useEffect(() => {
    if (throttleNotifications === null) {
      ApiGetNoAuth(`/notifications/${mvnoId}`)
        .then((response) => {
          dispatch({
            type: "set",
            throttleNotifications: response.data,
          });
        })
        .catch((error) => {
          console.log(error);
        });
    }
  }, [throttleNotifications]);

  return (
    <Link to={`/${mvnoId}/notifications`} className={styles.notification}>
      {throttleNotifications &&
      throttleNotifications.some((item: any) => !item.read) ? (
        <BellNew />
      ) : (
        <Bell />
      )}
    </Link>
  );
};

export default NotificationBell;
