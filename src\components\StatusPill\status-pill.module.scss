@use "../../styles/theme.scss" as *;

.main {
  padding: 4px 12px;
  font-size: 14px;
  border-radius: 6px;
  width: auto;
  font-weight: 500 !important;
  display: inline-block;
  &.hover:hover {
    box-shadow: 0 0 0 6px #fff;
  }
  &.status-0 {
    background-color: $inactive;
    color: #000;
  }
  &.status-1 {
    background-color: $active;
    color: #fff;
  }
  &.status-2 {
    background-color: $suspended;
    color: #000;
  }
  &.status-3 {
    background-color: $tbs;
    color: #000;
  }
  &.status-4 {
    background-color: $cancelled;
    color: #fff;
  }
  &.status-5 {
    background-color: #e0dcdc;
    color: $black;
  }
  &.status-6 {
    background-color: $rejected;
    color: $black;
  }
  &.status-7 {
    background-color: #f8ec7e;
  }
  &.status-11 {
    background-color: #eed922;
  }
  &.status--1 {
    background-color: none;
    border: 1px solid #666666;
  }
}
