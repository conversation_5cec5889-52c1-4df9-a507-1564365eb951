import { <PERSON>, useNavigate, useParams } from "react-router-dom";
import Menu from "../../components/Menu";
import {
  ArrowBack,
  LogOut,
  Pencil,
  SendPassword,
  User,
} from "../../components/svgs";
import { logOut } from "../../components/utils/logOut";
import styles from "../../styles/user-profile.module.scss";
import { useDispatch, useSelector } from "react-redux";
import RoleBadge from "../../components/RoleBadge";
import Button from "../../components/Button";
import ChangePasswordModal from "../../components/ChangePasswordModal";
import { useEffect, useState } from "react";
import { ApiGet, ApiPostAuth } from "../api/api";
import EditUserModal from "../../components/EditUserModal";
import { AnimatePresence } from "framer-motion";
import Notification from "../../components/Notification";

const UserProfile = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { userInfo, notifications } = useSelector((state: any) => state);

  const { mvnoId } = useParams();

  const [changingPassword, setChangingPassword] = useState(false);
  const [editingUser, setEditingUser] = useState(false);

  const repopulateUserInfo = (request: any) => {
    let { userId, roleId, enable2fa, ...userDetails } = request;
    userDetails.mid = userId;
    userDetails.roleName =
      roleId === 3 ? "mvne" : roleId === 1 ? "Agent" : "Admin";
    userDetails.is2faEnabled = enable2fa;
    dispatch({
      type: "set",
      userInfo: userDetails,
    });
    localStorage.setItem("crmUserInfo", JSON.stringify(userDetails));
  };

  return (
    <div className={styles.mvneMain}>
      <div className="notification-wrapper login-notification-wrapper">
        <AnimatePresence>
          {notifications.map((notification: any) => (
            <Notification
              id={notification.id}
              key={notification.id}
              message={notification.message}
              error={notification.error}
            />
          ))}
        </AnimatePresence>
      </div>
      <div className={styles.topBar}>
        <Link to="/select-project">
          <div className={styles.back}>
            <ArrowBack />
          </div>
        </Link>
        <h2 style={{ marginRight: "auto", marginLeft: 16 }}>My Profile</h2>
        <Menu
          data={{
            label: userInfo.firstName,
            items: [
              {
                label: "Profile",
                icon: <User />,
                link: "/user-profile",
              },
              {
                label: "Logout",
                icon: <LogOut />,
                onClick: () => {
                  logOut(dispatch, navigate);
                },
              },
            ],
          }}
        />
      </div>
      <ChangePasswordModal
        show={changingPassword}
        setShow={setChangingPassword}
        user={userInfo}
        id={userInfo.mid}
      />
      <EditUserModal
        show={editingUser}
        setShow={setEditingUser}
        user={userInfo}
        id={userInfo.mid}
        resetActiveUser={null}
        repopulateUsers={repopulateUserInfo}
        self
        mvne
      />
      <div className={styles.mainTile}>
        <div className={styles.grid}>
          <img className={styles.illustration} src="/user_profile.png" />
          <div className={styles.userDetails}>
            <RoleBadge role={userInfo.roleName} />
            <div className={styles.nameContainer}>
              <div className={styles.name}>
                {userInfo.firstName} {userInfo.lastName}
              </div>
              <button
                onClick={() => {
                  setEditingUser(true);
                }}
                className={styles.editButton}
              >
                <Pencil />
              </button>
            </div>
            <div className={styles.email}>{userInfo.email}</div>
            <Button
              onClick={() => {
                setChangingPassword(true);
              }}
            >
              <SendPassword />
              Change Password
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
