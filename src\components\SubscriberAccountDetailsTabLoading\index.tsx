import Shimmer from "../Shimmer";
import styles from "./subscriber-account-details-tab-loading.module.scss";

const SubscriberAccountDetailsTabLoading = () => {
  return (
    <div>
      {/* Subscriber Name */}
      <div
        className={styles.box}
        style={{ width: "240px", height: "30px", marginBottom: "14px" }}
      >
        <Shimmer />
      </div>
      <div className={styles.flex}>
        {/* Tabs */}
        <div
          className={styles.box}
          style={{ width: "146px", height: "47px", borderRadius: "23.5px" }}
        >
          <Shimmer />
        </div>
        <div
          className={styles.box}
          style={{ width: "110px", height: "21px", marginLeft: "24px" }}
        >
          <Shimmer />
        </div>

        {/* Add Subscription Button */}
        <div
          className={styles.box}
          style={{
            marginLeft: "auto",
            width: "220px",
            height: "50px",
            borderRadius: "25px",
          }}
        >
          <Shimmer />
        </div>
      </div>

      <div className={styles.container} style={{ marginTop: "24px" }}>
        <div className={styles.flex} style={{ alignItems: "start" }}>
          {/* Account details */}
          <div>
            <div
              className={styles.box}
              style={{ width: "240px", height: "30px" }}
            >
              <Shimmer />
            </div>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                columnGap: "150px",
                rowGap: "16px",
                marginTop: "24px",
              }}
            >
              {Array.from({ length: 4 }).map(() => (
                <div>
                  <div
                    className={styles.box}
                    style={{ width: "100px", height: "10px" }}
                  >
                    <Shimmer />
                  </div>
                  <div
                    className={styles.box}
                    style={{ width: "128px", height: "14px", marginTop: "4px" }}
                  >
                    <Shimmer />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Edit Btn */}
          <div
            className={styles.box}
            style={{ width: "225px", height: "44px", marginLeft: "auto" }}
          >
            <Shimmer />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriberAccountDetailsTabLoading;
