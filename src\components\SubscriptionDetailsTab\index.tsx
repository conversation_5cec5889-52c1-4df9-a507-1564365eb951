import PortingLine from "../PortingLine";
import PlanStatus from "../PlanStatus";
import { dummyPortins } from "../utils/dummySubscriptions";
import formatDate, { formatSyncedDate } from "../utils/formatDate";
import styles from "./subscription-details-tab.module.scss";
import PlanManageMenu from "../PlanManageMenu";
import clsx from "clsx";
import { usePlanManageContext } from "../PlanManageContext";
import { CircularProgress } from "@mui/material";
import { Info, Sync } from "../svgs";
import { useState } from "react";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import Button from "../Button";

type SubscriptionDetailsTabProps = {
  subInfo: any;
  planData: any;
  repopulate: () => void;
  isPorting?: boolean;
};

const SubscriptionDetailsTab = ({
  subInfo,
  planData,
  repopulate,
  isPorting,
}: SubscriptionDetailsTabProps) => {
  const dispatch = useDispatch();
  const getDataBalance = (planData: any) => {
    if (
      !Object.hasOwn(planData, "kbBankGB") ||
      !Object.hasOwn(planData, "kbBankMB")
    ) {
      return "-";
    }

    if (planData?.kbBankGB < 1) {
      return `${planData?.kbBankMB} MB`;
    } else {
      return `${planData?.kbBankGB} GB`;
    }
  };
  const { setCurrentModal } = usePlanManageContext();

  const [syncLoading, setSyncLoading] = useState(false);

  const handleLineSync = () => {
    setSyncLoading(true);
    ApiPostAuth(`/accounts/activate/${planData?.id}`)
      .then((response) => {
        setSyncLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response?.data?.message,
          },
        });
        repopulate();
      })
      .catch((error) => {
        setSyncLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error?.response?.data?.message,
          },
        });
      });
  };
  return (
    <div className={styles.mainGrid}>
      <div className={styles.panel}>
        <div className={styles.spread} style={{ marginBottom: 24 }}>
          <div className={styles.statusContainer}>
            <PlanStatus plan={planData} />
            {planData?.type === "activeSubscription" && (
              <div className={styles.lineSync}>
                <button
                  className={styles.syncButton}
                  onClick={handleLineSync}
                  disabled={syncLoading}
                >
                  {syncLoading && (
                    <CircularProgress
                      style={{
                        width: 16,
                        height: 16,
                        color: "#000",
                        gridArea: "1 / 1 / 2 / 2",
                        margin: "0 auto",
                      }}
                    />
                  )}
                  <span
                    style={{
                      visibility: syncLoading ? "hidden" : "visible",
                    }}
                    className={styles.content}
                  >
                    <Sync />
                    Sync Status
                  </span>
                </button>
                <div className={styles.lastSynced}>
                  Last synced: {formatSyncedDate(planData?.lastModifiedDate)}
                </div>
              </div>
            )}
          </div>
          <PlanManageMenu plan={planData} />
        </div>
        {/* Activation Failure Reason (If applicable) */}
        <div className={styles.activationFailureCallout}>
          <div className={styles.activationFailureCalloutTexts}>
            <div className={styles.activationFailureCalloutTitle}>
              <Info />
              <span>Fail Reason: </span>
            </div>
            <div className={styles.activationFailureCalloutDescription}>
              {planData.errorMessage}
            </div>
          </div>
          <div className={styles.activationFailureCalloutBtns}>
            <Button color="white"
              onClick={() => setCurrentModal("resubmit-activation-no-update")}
            >
              Resubmit
            </Button>
            <Button color="white"
              onClick={() => setCurrentModal("resubmit-activation")}
            >
              Update & Resubmit
            </Button>
          </div>
        </div>
        <div>
          <div
            className={styles.dataName}
            style={{ marginBottom: 8, fontSize: 14, lineHeight: "21px" }}
          >
            Subscription Title
          </div>
          <div className={styles.dataValue} style={{ marginBottom: 0 }}>
            {planData?.product?.product}
          </div>
        </div>
        <div className={styles.subGrid}>
          <div>
            <div className={styles.columnTitle}>General Details</div>
            <div>
              <div className={styles.dataName}>Creation Date</div>
              <div className={styles.dataValue}>
                {planData?.creationDate
                  ? formatDate(planData?.creationDate)
                  : "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Activation Date</div>
              <div className={styles.dataValue}>
                {planData?.activationDate
                  ? formatDate(planData?.activationDate)
                  : "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Bill Cycle</div>
              <div className={styles.dataValue}>
                {planData?.nextBillCycleDate
                  ? formatDate(planData?.nextBillCycleDate)
                  : "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Plan Size</div>
              <div className={styles.dataValue}>
                {planData?.product?.productSize || "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Data Balance</div>
              <div className={styles.dataValue}>{getDataBalance(planData)}</div>
            </div>
            <div>
              <div className={styles.dataName}>Service Type</div>
              <div className={styles.dataValue}>
                {planData?.product?.serviceType || "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>MDN</div>
              <div className={styles.dataValue}>
                {planData?.mdn ||
                  planData?.cancelledMdn ||
                  planData?.subscriberNumber ||
                  "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Device Nickname</div>
              <div className={styles.dataValue}>{planData?.nickname}</div>
            </div>
            <div>
              <div className={styles.dataName}>ICCID</div>
              <div className={styles.dataValue}>{planData?.iccid || "-"}</div>
            </div>
            <div>
              <div className={styles.dataName}>IMEI</div>
              <div className={styles.dataValue}>{planData?.imei || "-"}</div>
            </div>
            {isPorting && (
              <div>
                <div className={styles.dataName}>Old Carrier Details</div>
                <div className={styles.dataValue}>
                  PIN:{" "}
                  {planData?.billingAccountPassword ||
                    planData?.attDetails?.oldService?.billingAccountPassword ||
                    "-"}
                  <br />
                  Account No.:{" "}
                  {planData?.billingAccountNumber ||
                    planData?.attDetails?.oldService?.billingAccountNumber ||
                    "-"}
                </div>
              </div>
            )}
            <div>
              <div className={styles.dataName}>BAN</div>
              <div className={styles.dataValue}>{planData?.ban || "-"}</div>
            </div>
          </div>
          <div>
            <div className={styles.columnTitle}>Subscription Details</div>
            <div>
              <div className={styles.dataName}>First Name</div>
              <div className={styles.dataValue}>
                {subInfo?.subscriberFirstName || "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Last Name</div>
              <div className={styles.dataValue}>
                {subInfo?.subscriberLastName || "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Street Address</div>
              <div className={styles.dataValue}>
                {subInfo?.address?.streetNumber}{" "}
                {subInfo?.address?.streetDirection}{" "}
                {subInfo?.address?.streetName}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>City</div>
              <div className={styles.dataValue}>
                {subInfo?.address?.city || "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>State</div>
              <div className={styles.dataValue}>
                {subInfo?.address?.state || "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Zip Code</div>
              <div className={styles.dataValue}>
                {subInfo?.address?.zipCode || "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Email Address</div>
              <div className={styles.dataValue}>{subInfo?.email || "-"}</div>
            </div>
            <div>
              <div className={styles.dataName}>Contact Number</div>
              <div className={styles.dataValue}>
                {subInfo?.contactNumber || "-"}
              </div>
            </div>
          </div>
          <div>
            <div className={styles.columnTitle}>Provisioning Details</div>
            <div>
              <div className={styles.dataName}>First Name</div>
              <div className={styles.dataValue}>
                {subInfo?.subscriberFirstName || "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Last Name</div>
              <div className={styles.dataValue}>
                {subInfo?.subscriberLastName || "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Street Address</div>
              <div className={styles.dataValue}>
                {subInfo?.address?.streetNumber}{" "}
                {subInfo?.address?.streetDirection}{" "}
                {subInfo?.address?.streetName}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>City</div>
              <div className={styles.dataValue}>
                {subInfo?.address?.city || "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>State</div>
              <div className={styles.dataValue}>
                {subInfo?.address?.state || "-"}
              </div>
            </div>
            <div>
              <div className={styles.dataName}>Zip Code</div>
              <div className={styles.dataValue}>
                {subInfo?.address?.zipCode || "-"}
              </div>
            </div>
          </div>
        </div>
      </div>
      {isPorting && (
        <div className={clsx(styles.panel, styles.portingPanel)}>
          <div className={styles.panelTitle}>Port-in Info</div>
          <PortingLine
            port={planData}
            setCurrentModal={setCurrentModal}
            repopulate={repopulate}
          />
        </div>
      )}
    </div>
  );
};

export default SubscriptionDetailsTab;
