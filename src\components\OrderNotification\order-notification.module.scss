@use "../../styles/theme.scss" as *;

.notifyPanel {
  display: flex;
  padding: 19px 32px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
  width: 100%;
  border-radius: 12px;
  background: #f8f7f7;
  margin-bottom: 12px;
  &:last-child {
    margin-bottom: 0px;
  }
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    margin-bottom: 9px;
    .date {
      color: #797979;
      font-family: Poppins;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
    .title {
      color: $black;
      font-family: Poppins;
      font-size: 14px;
      font-weight: 600;
      align-items: center;
      display: flex;
      &.read {
        font-weight: 400;
      }
    }
  }
  .bottom {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    align-self: stretch;
    a {
      text-decoration: none;
    }
    .sub {
      display: flex;
      text-decoration: none;
      cursor: pointer;
      color: $orange;
      font-family: Poppins;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      &:hover {
        color: $dark-orange;
      }
    }
  }
}
