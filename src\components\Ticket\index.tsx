import styles from "./ticket.module.scss";
import Checkbox from "../Checkbox";
import {
  ChevronDownLg,
  ArrowRight,
  Home,
  Spanner,
  Coin,
  Clipboard,
  User,
  ArrowSquareIn,
  PencilSimple,
} from "../svgs";
import { Collapse } from "@mui/material";
import { useEffect, useState } from "react";
import Button from "../Button";
import TicketSelect from "../TicketSelect";
import Priority from "../Priority";
import Category from "../Category";
import Assignee from "../Assignee";
import StatusBadge from "../StatusBadge";
import Initials from "../Initials";
import formatDate from "../utils/formatDate";
import { motion } from "framer-motion";
import { useDispatch, useSelector } from "react-redux";
import TicketType from "../TicketType";
import { Link, useParams } from "react-router-dom";
import moment from "moment";
import { highlightSearch } from "../utils/searchAndFilter";
import DueDateBadge from "../DueDateBadge";
import CreateTicketModal from "../CreateTicketModal";
import CategorySelect from "../CategorySelect";
import { categories } from "../utils/tickets";

const Ticket = ({
  ticket,
  handleTicketUpdate,
  handleSelectTicket,
  selectedTickets,
  disabled,
  assignees,
  queryDisplay,
  GetTicket,
}: any) => {
  const [selected, setSelected] = useState(false);
  const { ticketOpen } = useSelector((state: any) => state);

  const [showBody, setShowBody] = useState(false);
  const [edit, setEdit] = useState(false);

  const dispatch = useDispatch();

  const { mvnoId } = useParams();

  useEffect(() => {
    if (selectedTickets.includes(ticket.id)) {
      setSelected(true);
    } else {
      setSelected(false);
    }
  }, [selectedTickets]);

  const DataBar = () => {
    return (
      <div className={styles.dataBar}>
        {ticket.type !== "DID Request" && ticket.type !== "Porting" && (
          <>
            <TicketSelect
              disabled={disabled}
              label="Priority"
              selected={ticket.priority}
              onChange={(option: string) => {
                handleTicketUpdate("priority", option, ticket);
              }}
              options={[
                {
                  key: 4,
                  label: <Priority priority="Urgent" />,
                  displayLabel: <Priority priority="Urgent" />,
                },
                {
                  key: 3,
                  label: <Priority priority="High" />,
                  displayLabel: <Priority priority="High" />,
                },
                {
                  key: 2,
                  label: <Priority priority="Medium" />,
                  displayLabel: <Priority priority="Medium" />,
                },
                {
                  key: 1,
                  label: <Priority priority="Low" />,
                  displayLabel: <Priority priority="Low" />,
                },
              ]}
            />
          </>
        )}
        <TicketSelect
          assignees={assignees}
          disabled={disabled}
          label="Assignee"
          options={assignees.map((assignee: any) => {
            const getInitials = (name: string) => {
              let names = name.split(" ");
              return names[0][0] + names[names.length - 1][0];
            };
            return {
              key: assignee.id,
              label: <Assignee name={assignee.name} />,
              displayLabel: (
                <Initials>{getInitials(assignee.name).toUpperCase()}</Initials>
              ),
            };
          })}
          selected={ticket.agentId}
          onChange={(option: string) => {
            handleTicketUpdate("assignee", option, ticket);
          }}
        />
        <TicketSelect
          disabled={disabled}
          label="Status"
          options={[
            {
              key: 2,
              label: <StatusBadge status="Open" />,
              displayLabel: <StatusBadge status="Open" />,
            },
            {
              key: 3,
              label: <StatusBadge status="Pending" />,
              displayLabel: <StatusBadge status="Pending" />,
            },
            {
              key: 4,
              label: <StatusBadge status="Resolved" />,
              displayLabel: <StatusBadge status="Resolved" />,
            },
            {
              key: 5,
              label: <StatusBadge status="Closed" />,
              displayLabel: <StatusBadge status="Closed" />,
            },
          ]}
          selected={ticket.status}
          onChange={(option: string) => {
            handleTicketUpdate("status", option, ticket);
          }}
        />
        <DueDateBadge date={new Date(ticket.dueDate)} />
      </div>
    );
  };

  return (
    <>
      <CreateTicketModal
        show={edit}
        setShow={setEdit}
        ticket={ticket}
        mode="edit"
        populate={GetTicket}
      />
      <motion.div
        layout="position"
        animate="in"
        variants={{
          in: {
            opacity: 1,
          },
          out: {
            opacity: 0,
          },
        }}
        transition={{
          type: "tween",
          ease: "easeInOut",
          duration: 0.3,
        }}
        key={"motion-ticket-" + ticket.id}
        className={`${styles.main} ${disabled && styles.disabled} ${
          ticketOpen && styles.ticketOpen
        } container`}
        onClick={(e: any) => {
          if (
            e.target.closest(".szh-menu") ||
            e.target.id === "customer-summary"
          ) {
            return;
          }
          dispatch({
            type: "set",
            ticketOpen: true,
            ticket: ticket,
            sidebarOpen: false,
          });
        }}
      >
        <div className={`${styles.summary} summary`}>
          <div className={styles.summaryWrapper}>
            {/*<div style={{ width: 24 }}>
            <Checkbox
              checked={selected}
              onClick={() => handleSelectTicket(ticket)}
            />
    </div>*/}
            <div className={styles.overview}>
              <div className={styles.top}>
                <CategorySelect
                  disabled={disabled}
                  label="Category"
                  options={categories.map((item: string) => ({
                    key: item,
                    label: item,
                    displayLabel: <TicketType status={item} />,
                  }))}
                  selected={ticket.category}
                  onChange={(option: string) => {
                    handleTicketUpdate("category", option, ticket);
                  }}
                  small
                />
                <div className={styles.id}>#{ticket.id}</div>
                <div className={styles.date}>
                  {moment(ticket.createdDate).format("DD/MM/YYYY")}
                </div>
              </div>
              {ticketOpen && <DataBar />}
              <div className={styles.bottom}>
                <div className={styles.subject}>
                  {highlightSearch(ticket.subject, queryDisplay)}
                </div>
                <div className={styles.email}>{ticket.email}</div>
              </div>
            </div>
          </div>
          {!ticketOpen && <DataBar />}
          <div className={styles.actions}>
            {!ticketOpen && (
              <>
                <Link
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  to={`/${mvnoId}/tickets/${ticket.id}`}
                >
                  <div className={styles.openMain} style={{ marginRight: 12 }}>
                    <ArrowSquareIn />
                  </div>
                </Link>
                <div
                  style={{ marginRight: 20 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setEdit(true);
                  }}
                >
                  <PencilSimple />
                </div>
              </>
            )}
            <div
              onClick={(e: any) => {
                e.stopPropagation();
                setShowBody((prev: boolean) => !prev);
              }}
              className={`${styles.expand} ${showBody && styles.rotate}`}
            >
              <ChevronDownLg />
            </div>
          </div>
        </div>
        <Collapse in={showBody}>
          <div className={styles.body}>
            <div className={styles.left}>
              <h5 className={styles.issueHeading}>Issue:</h5>
              <div className={styles.issueContent}>{ticket.description}</div>
            </div>
            {/*<div className={styles.customerSummary} id="customer-summary">
            Customer Summary <ArrowRight />
        </div>*/}
          </div>
        </Collapse>
      </motion.div>
    </>
  );
};

export default Ticket;
