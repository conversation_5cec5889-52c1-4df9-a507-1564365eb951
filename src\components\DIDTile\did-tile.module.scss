@use "../../styles/theme.scss" as *;

.notes {
  background: $off-white;
  box-shadow: 0px 0px 30px 0px rgba(8, 7, 87, 0.1);
  border-radius: 24px;
  padding: 24px 0;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px 12px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    margin-bottom: 24px;
    h4 {
      font-size: 20px;
      line-height: 30px;
    }
  }
}

.docsContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 16px;
  padding: 0 24px;
  .doc {
    padding: 24px 18px;
    border-radius: 24px;
    border: 1px solid rgba(0, 0, 0, 0.15);
  }
  .docTitle {
    margin-bottom: 12px;
    font-size: 14px;
  }
  .docImage {
    width: 100%;
    border-radius: 24px;
    border: 1px solid rgba(0, 0, 0, 0.15);
  }
}
