import styles from "./role-badge.module.scss";

const RoleBadge = ({ role }: any) => {
  return (
    <div
      className={`${styles.main} ${
        role === 2 || role === "Admin"
          ? styles.admin
          : role === 1 || role === "Agent"
          ? styles.agent
          : role === 3 || role === "mvne"
          ? styles.mvne
          : ""
      }`}
    >
      {role === 2 || role === "Admin"
        ? "Admin"
        : role === 1 || role === "Agent"
        ? "Agent"
        : role === 3 || role === "mvne"
        ? "MVNE"
        : ""}
    </div>
  );
};

export default RoleBadge;
