import { useEffect, useState } from "react";
import Modal from "../Modal";
import { FloppyDisk } from "../svgs";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
} from "../utils/InputHandlers";
import styles from "./change-channel-modal.module.scss";
import { validateAll } from "indicative/validator";
import { ApiGet, ApiPut } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import SelectDropdown from "../SelectDropdown";
import { useParams } from "react-router-dom";

const fields = ["channel"];
const rules = getRules(fields);
const messages = getMessages(fields);

const ChangeChannelModal = ({ show, setShow, subscriber, repopulate }: any) => {
  const dispatch = useDispatch();

  const handleChange = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPut(`/channels/${data.channel.value}/subscribers/${subscriber.mid}`)
          .then((response) => {
            repopulate(() => {
              setLoading(false);
              setData(createStateObject(fields));
              dispatch({
                type: "notify",
                payload: {
                  error: false,
                  message: response.data.message,
                },
              });
              setShow("");
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message:
                  error.response.data.message ||
                  "Something went wrong, please try again.",
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const [data, setData] = useState(createStateObject(fields));

  const { mvnoId, channelId } = useParams();

  const [loading, setLoading] = useState(false);

  const [channels, setChannels] = useState([]);

  // Populate initial channel
  useEffect(() => {
    if (show && subscriber) {
      setData({
        channel: {
          value: channelId,
          label: subscriber.channelName,
        },
        errors: { ...data.errors, channel: "" },
      });
    }
  }, [show, subscriber]);

  // Populate channel selection
  useEffect(() => {
    ApiGet(`/channels/mvno/${mvnoId}`)
      .then((response) => {
        setChannels(
          response.data.map((channel: any) => ({
            value: channel.id.toString(),
            label: channel.name,
          })),
        );
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  const handleChannelChange = (selectedOption: any) => {
    setData({
      ...data,
      channel: selectedOption,
      errors: { ...data.errors, channel: "" },
    });
  };

  return (
    <Modal
      saveButton={
        <>
          <FloppyDisk />
          Save Changes
        </>
      }
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      setShow={setShow}
      proceed={handleChange}
      close={() => {
        clearInput("imei", setData);
        setShow("");
      }}
      loading={loading}
    >
      <div className={styles.main}>
        <h4 style={{ textAlign: "center" }}>
          Change Channel for {subscriber?.subscriberName}
        </h4>
        <SelectDropdown
          key="channel-select"
          value={data.channel}
          error={data.errors.channel}
          onChange={handleChannelChange}
          placeholder="Channel"
          options={channels}
          disabled={loading}
          white
        />
      </div>
    </Modal>
  );
};

export default ChangeChannelModal;
