import styles from "./category.module.scss";
import { Home, Coin, Cube, Spanner, Clipboard, User } from "../svgs";

const Category = ({ category, small }: any) => {
  return (
    <div className={`${styles.main} ${small && styles.small}`}>
      {category === "Invoice" ? (
        <Clipboard />
      ) : category === "Payment" ? (
        <Coin />
      ) : category === "Billing Issues" ? (
        <Home />
      ) : category === "Maintenance and Downtime" ? (
        <Spanner />
      ) : (
        <User />
      )}
      {category}
    </div>
  );
};

export default Category;
