/**
 * Type definitions for the MultiSelect component
 */

/**
 * Represents an option in the MultiSelect dropdown
 */
export interface MultiSelectOption {
  /** Unique identifier for the option */
  key: string | number;
  /** Display text for the option */
  label: string;
}

/**
 * Props interface for the MultiSelect component
 */
export interface MultiSelectProps {
  /** Label text displayed on the dropdown button */
  label: string;
  /** Array of options to display in the dropdown */
  options: MultiSelectOption[];
  /** Whether the options are currently loading */
  optionsLoading?: boolean;
  /** Callback function called when the dropdown is opened */
  onOpen?: () => void;
  /** Array of currently selected option keys */
  selected: (string | number)[];
  /** Callback function to update the selected values */
  setSelected: (selected: (string | number)[]) => void;
  /** Whether to display options in a 3-column grid layout */
  grid?: boolean;
  /** Whether to display options in a 2-column grid layout */
  twoColumnGrid?: boolean;
  /** Whether to hide the "Clear All" button */
  noClear?: boolean;
  /** Whether to show the search functionality */
  search?: boolean;
  /** Placeholder text for the search input */
  searchPlaceholder?: string;
  darkerBg?: boolean;
}

/**
 * Type alias for selected values array
 */
export type MultiSelectValue = (string | number)[];

/**
 * Type for the setSelected callback function
 */
export type MultiSelectSetSelectedCallback = (selected: MultiSelectValue) => void;

/**
 * Type for the onOpen callback function
 */
export type MultiSelectOnOpenCallback = () => void;
