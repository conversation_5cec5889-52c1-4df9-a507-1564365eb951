@use "./theme.scss" as *;

.main {
  width: 100%;
  padding: 32px 100px 50px 100px;
  background: #fdfcfb;
  min-height: calc(100vh - 88px);
}

.projects {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr;
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  padding: 53px 0;
  @media (max-width: 1550px) {
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  }
  @media (max-width: 1300px) {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}
