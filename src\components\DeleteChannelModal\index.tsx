import styles from "./delete-channel-modal.module.scss";
import Modal from "../Modal";
import { Delete } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { ApiDelete } from "../../pages/api/api";

const DeleteChannelModal = ({ show, setShow, channel, repopulate }: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  const deleteUser = () => {
    setLoading(true);
    ApiDelete(`/channels/${channel.id}`, {})
      .then((response) => {
        setLoading(false);
        setShow(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulate();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Modal
      saveButton={
        <>
          <Delete />
          Yes, Delete Channel
        </>
      }
      image="/delete_user_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={deleteUser}
      loading={loading}
    >
      <div className={styles.main}>
        <h3>Are you sure you want to delete this channel?</h3>
        <div className={styles.infoTable}>
          <table>
            <tr>
              <th>Name</th>
              <td>{channel?.name}</td>
            </tr>
            <tr>
              <th>Description</th>
              <td>{channel?.description}</td>
            </tr>
            {/*<tr>
              <th>Email Address</th>
              <td>{channel?.email}</td>
            </tr>
            <tr>
              <th>Phone Number</th>
              <td>{channel?.phoneNumber}</td>
            </tr>*/}
          </table>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteChannelModal;
