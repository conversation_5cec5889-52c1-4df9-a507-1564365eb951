@use "../../styles/theme.scss" as *;

.main {
  position: fixed;
  z-index: 2000;
  height: 100vh;
  width: 600px;
  top: 0px;
  right: -746px;
  background: $off-white;
  transition: right 0.5s ease;
  border-left: 1px solid rgba(0, 0, 0, 0.15);
  padding: 40px 24px;
  &.open {
    right: 0px;
  }
}

.mainGrid {
  display: grid;
  grid-template-rows: 68px 1fr;
  height: 100%;
  &.support {
    grid-template-rows: 280px 1fr;
    .date {
      margin-left: auto;
    }
  }
}

.prevNext {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: flex-end;
  height: 44px;
  margin-bottom: 24px;
}

.close {
  cursor: pointer;
  width: 64px;
  height: 64px;
  background: #f2f2f2;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  z-index: 3000;
  top: 40px;
  left: -32px;
  border-radius: 1000px;
  box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.15);
}
.customerSummary {
  display: flex;
  align-items: center;
  gap: 8px;
  color: $orange;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  height: 24px;
  transition: all 0.2s ease;
  white-space: nowrap;
  cursor: pointer;
  &:hover {
    color: $dark-orange;
  }
}

.customerInfo {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px;
  border-radius: 24px;
  background-color: #f2f2f2;
  margin-bottom: 24px;
  .name {
    font-size: 20px;
    line-height: 30px;
    font-weight: 600;
    margin-bottom: 4px;
  }
  .email {
    font-size: 14px;
    line-height: 21px;
  }
}

.selectionWrapper {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}

.selection {
  height: 47px;
  border-radius: 1000px;
  color: $black;
  font-size: 14px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    color: $dark-orange;
  }
  span {
    position: relative;
    z-index: 6;
  }
}

.contentScroll {
  margin: -24px -18px -24px -24px;
  padding: 24px;
}

.activeSelection {
  text-shadow: 0px 0px 0.5px $black;
  cursor: auto;
  &:hover {
    color: $black;
  }
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1000px;
  background-color: $mid-orange;
  z-index: 5;
  left: 0;
}
