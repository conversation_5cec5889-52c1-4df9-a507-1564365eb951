import Modal from "../Modal";
import { CheckCircle } from "../svgs";
import styles from "./add-subscriber-success-modal.module.scss";

const AddSubscriberSuccessModal = ({
  show,
  setShow,
  handleFinish,
  type,
  handleToSubscriberPage,
}: any) => {
  return (
    <Modal
      show={show}
      close={handleFinish}
      proceed={handleToSubscriberPage}
      image="/green_robot.svg"
      saveButton="Subscriber Profile Screen"
      clearContainer
      cancelButton="Subscriber Management"
    >
      <div className={styles.main}>
        <h3>
          {type === "reg"
            ? "Subscriber successfully created"
            : "Activation Request Sent!"}
        </h3>
        <div className={styles.message}>
          {type === "reg"
            ? "You can now return to Subscriber Management page or see the profile screen of the subscriber you just created."
            : "We have received your request and it is currently in progress. You can track the progress of your request by visiting your subscriber profile screen."}
        </div>
      </div>
    </Modal>
  );
};

export default AddSubscriberSuccessModal;
