import styles from "./remove-filter.module.scss";
import { XCircle } from "../svgs";
import { motion, AnimatePresence } from "framer-motion";
import { statuses } from "../StatusPill";
import { ticketStatus } from "../utils/tickets";
import { convertStatus } from "../OrderStatusBadge";

const RemoveFilter = ({
  type,
  status = null,
  handleRemoveFilter,
  filter = "",
  filterObject,
  setFilterObject,
  grey,
  children,
  group,
  mode = "",
  sim,
  order,
  single,
}: any) => {
  console.log(status);
  return (
    <AnimatePresence>
      <motion.div
        layout
        initial="initial"
        animate="in"
        variants={{
          initial: {
            opacity: 0,
          },
          in: {
            opacity: 1,
          },
          out: {
            opacity: 0,
          },
        }}
        transition={{
          type: "tween",
          ease: "easeInOut",
          duration: 0.3,
        }}
        exit={{ opacity: 0 }}
        className={`${styles.main} ${grey && styles.grey} ${
          sim === "SIM" && styles.sim
        } ${sim === "eSIM" && styles.esim} ${order && styles[status]} ${
          typeof status === "string"
            ? styles.ticketCategory
            : mode === "tickets"
              ? status === 2
                ? styles.open
                : status === 3
                  ? styles.st_pending
                  : status === 4
                    ? styles.resolved
                    : status === 5
                      ? styles.closed
                      : null
              : status === 1
                ? styles.active
                : status === 2
                  ? styles.suspended
                  : status === 3
                    ? styles.tbs
                    : status === 4
                      ? styles.cancelled
                      : status === 5
                        ? styles.noSub
                        : status === 6
                          ? styles.rejected
                          : status === 7
                            ? styles.pending
                            : status === -1
                              ? styles.noStatus
                              : status === 8
                                ? styles.ready
                                : status === 9
                                  ? styles.iccidRequired
                                  : status === 10
                                    ? styles.banChange
                                    : status === 11
                                      ? styles.mdnChange
                                      : null
        }`}
      >
        {order
          ? convertStatus(status, true)
          : mode === "categories"
            ? children
            : status !== null
              ? status === -1
                ? "No Status"
                : mode === ""
                  ? statuses[status]
                  : ticketStatus.find((s: any) => s.id === status)?.name
              : children}

        <div
          className={styles.remove}
          onClick={() => {
            if (single) {
              handleRemoveFilter();
            } else if (group) {
              handleRemoveFilter(type, filterObject, setFilterObject);
            } else {
              handleRemoveFilter(type, filter, filterObject, setFilterObject);
            }
          }}
        >
          <XCircle />
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default RemoveFilter;
