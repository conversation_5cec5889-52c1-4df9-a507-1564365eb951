import { useEffect, useState, useRef } from "react";
import FeatureBadge from "../FeatureBadge";
import {
  ArrowBack,
  CaretDown,
  ChevronDown,
  ClockTimer,
  Delete,
  FailReason,
  MagicWand,
  Pause,
  Pencil,
  Play,
  Sparkle,
  Sync,
  X,
} from "../svgs";
import styles from "./subscriber-plan.module.scss";
import { CircularProgress, Collapse, Fade } from "@mui/material";
import Tooltip from "../Tooltip";
import formatDate, {
  formatDateWithTime,
  formatSyncedDate,
} from "../utils/formatDate";
import { useSelector } from "react-redux";
import Button from "../Button";
import {
  ApiDelete,
  ApiGet,
  ApiGetSubscriber,
  ApiPostAuth,
} from "../../pages/api/api";
import { useNavigate, useParams } from "react-router-dom";
import PlanTileLoading from "../PlanTileLoading";
import FeaturesLoading from "../FeaturesLoading";
import { useDispatch } from "react-redux";
import { tetherList } from "../utils/tetherList";

const SubscriberPlan = ({
  status: initialStatus,
  planData: initialPlanData,
  setSelectedPlan,
  setCurrentModal,
  features,
  porting,
  planToReload,
  setPlanToReload,
  // Allows to force refresh when we need to reload the same plan after an operation (i.e.planToReload value doesn't change)
  reloadKey,
}: any) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { mvnoId } = useParams();

  const [showMore, setShowMore] = useState(false);
  const [showMoreButtons, setShowMoreButtons] = useState(false);

  const [status, setStatus] = useState(initialStatus);

  const { sidebarOpen, activeMdn } = useSelector((state: any) => state);

  const [featureList, setFeatureList] = useState({
    throttle: [],
    boltons: [],
    toggleFeatures: [],
  });

  const { id: subscriberId } = useParams();

  const [planData, setPlanData] = useState(null as any);

  useEffect(() => {
    if (planToReload === initialPlanData.id) {
      setPlanData(null as any);
      loadExtraData();
    }
  }, [planToReload, reloadKey]);

  useEffect(() => {
    if (planData && planData.features) {
      let throttleList = [] as any;
      let boltonsList = [] as any;
      let toggleFeaturesList = [] as any;
      planData.features.forEach((item: any) => {
        if (
          features
            .filter((item: any) => item.classificationName === "throttle")
            .some((throt: any) => throt.code === item.code)
        ) {
          throttleList.push(item);
        } else if (
          features
            .filter((item: any) => item.classificationName === "bolton")
            .some((bolt: any) => bolt.code === item.code)
        ) {
          boltonsList.push(item);
        } else {
          toggleFeaturesList.push(item);
        }
      });
      if (tetherList.tether.includes(planData.product.soc)) {
        toggleFeaturesList.unshift({ name: "Tether" });
      }
      setFeatureList({
        throttle: throttleList,
        boltons: boltonsList,
        toggleFeatures: toggleFeaturesList,
      });
    }
  }, [planData]);

  const mainElement = useRef(null);

  useEffect(() => {
    if (initialPlanData && activeMdn) {
      if (
        (initialPlanData.subscriberNumber === activeMdn ||
          initialPlanData.cancelledMdn === activeMdn) &&
        status !== "ban change"
      ) {
        setShowMore(true);
        (mainElement.current as any)?.scrollIntoView({
          behavior: "smooth",
        });
      }
    }
  }, [planData, activeMdn]);

  const loadExtraData = (resetPlanToReload = false) => {
    ApiGetSubscriber(
      `/accounts/by-account/${subscriberId}/${porting ? "portin" : "sub"}/${
        initialPlanData.id
      }`,
      status === "rta" ? "temp" : "active",
    )
      .then((response) => {
        setPlanData(response.data);

        if (status !== "rta") {
          setStatus(
            response.data.subscriberNumberStatus === "Cancelled" &&
              (response.data.remainingDaysToResume <= 0 ||
                response.data.remainingDaysToResume === null)
              ? "cancelledPast"
              : response.data.subscriberNumberStatus.toLowerCase(),
          );
        }

        if (resetPlanToReload) {
          setPlanToReload(null);
          (mainElement.current as any)?.scrollIntoView({
            behavior: "smooth",
          });
        }
      })
      .catch((error) => {
        setShowMore(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  useEffect(() => {
    if (showMore) {
      setPlanData(null);
      loadExtraData();
    }
  }, [showMore]);

  const tooltipStyle = {
    width: "100%",
    marginBottom: 12,
  };

  const buttonsToShow = {
    active: ["edit", "features", "suspend", "cancel"],
    suspended: ["restore", "cancel"],
    tbs: ["edit", "features", "cancel"],
    cancelled: planData?.remainingDaysToResume > 0 ? ["resume"] : [],
    cancelledPast: [],
    "ban change": [],
    reserved: [],
    portout: [],
  } as any;

  const buttonComponents = {
    edit: (
      <Tooltip style={tooltipStyle} white show={!showMoreButtons} text="Edit">
        <button
          onClick={() => {
            setSelectedPlan(planData);
            setCurrentModal("edit-subscription");
          }}
          className={styles.controlButton}
          disabled={!planData}
        >
          <Pencil />
          <div className={styles.buttonLabel}>Edit</div>
        </button>
      </Tooltip>
    ),
    features: (
      <Tooltip
        style={tooltipStyle}
        white
        show={!showMoreButtons}
        text="Features"
      >
        <button
          onClick={() => {
            setSelectedPlan(planData);
            setCurrentModal("manage-features");
          }}
          className={styles.controlButton}
          disabled={!planData}
        >
          <Sparkle />
          <div className={styles.buttonLabel}>Features</div>
        </button>
      </Tooltip>
    ),
    suspend: (
      <Tooltip
        style={tooltipStyle}
        white
        show={!showMoreButtons}
        text="Suspend"
      >
        <button
          onClick={() => {
            setSelectedPlan(planData);
            setCurrentModal("confirm-suspend");
          }}
          className={styles.controlButton}
          disabled={!planData}
        >
          <Pause />
          <div className={styles.buttonLabel}>Suspend</div>
        </button>
      </Tooltip>
    ),
    cancel: (
      <Tooltip
        white
        show={!showMoreButtons}
        text="Cancel"
        style={{ width: "100%" }}
      >
        <button
          onClick={() => {
            setSelectedPlan(planData);
            setCurrentModal("confirm-cancel");
          }}
          className={styles.controlButton}
          disabled={!planData}
        >
          <X />
          <div className={styles.buttonLabel}>Cancel</div>
        </button>
      </Tooltip>
    ),
    resume: (
      <Tooltip white show={!showMoreButtons} text="Resume" style={tooltipStyle}>
        <button
          onClick={() => {
            setSelectedPlan(planData);
            setCurrentModal("confirm-resume");
          }}
          className={styles.controlButton}
          disabled={!planData}
        >
          <Play />
          <div className={styles.buttonLabel}>Resume</div>
        </button>
      </Tooltip>
    ),
    restore: (
      <Tooltip
        white
        show={!showMoreButtons}
        text="Restore"
        style={{ width: "100%" }}
      >
        <button
          onClick={() => {
            setSelectedPlan(planData);
            setCurrentModal("confirm-restore");
          }}
          className={styles.controlButton}
          disabled={!planData}
        >
          <MagicWand />
          <div className={styles.buttonLabel}>Restore</div>
        </button>
      </Tooltip>
    ),
  } as any;

  const isPortinMissingBilling = () => {
    if (planData) {
      return (
        planData?.attDetails.oldService.billingAccountNumber === null ||
        planData?.attDetails.oldService.billingAccountPassword === null
      );
    } else {
      return (
        initialPlanData.billingAccountNumber === null ||
        initialPlanData.billingAccountPassword === null
      );
    }
  };

  const isPortinMissingIccid = () => {
    if (planData) {
      return planData?.iccid === null;
    } else {
      return initialPlanData.iccid === null;
    }
  };

  const isPortinIncomplete = () => {
    return isPortinMissingBilling() || isPortinMissingIccid();
  };

  const isSubscriptionMissingIccid = () => {
    if (planData) {
      return planData?.iccid === null;
    } else {
      return initialPlanData.iccid === null;
    }
  };

  const getRtaColor = (type: string) => {
    if (porting) {
      if (isPortinIncomplete()) {
        if (type === "background") return "#F8EC7E";
        if (type === "indicator") return "#F8EC7E";
      } else {
        if (type === "background") return "#CBCAF9";
        if (type === "indicator") return "#6361DC";
      }
    } else {
      if (isSubscriptionMissingIccid()) {
        if (type === "background") return "#F8EC7E";
        if (type === "indicator") return "#F8EC7E";
      } else {
        if (type === "background") return "#CBCAF9";
        if (type === "indicator") return "#6361DC";
      }
    }
  };

  const getRtaMessage = () => {
    if (porting) {
      if (isPortinMissingBilling()) {
        if (isPortinMissingIccid()) {
          return "ICCID and Porting Details Required";
        } else {
          return "Missing Porting Details";
        }
      } else {
        if (isPortinMissingIccid()) {
          return "ICCID allocation required";
        } else {
          return "Ready to activate";
        }
      }
    } else {
      if (isSubscriptionMissingIccid()) {
        return "ICCID allocation required";
      } else {
        return "Ready to activate";
      }
    }
  };

  const getRtaLabel = () => {
    if (porting) {
      if (isPortinMissingBilling()) {
        if (isPortinMissingIccid()) {
          return (
            <>
              <div className={styles.singleIccidLabel}>
                Missing Porting Details
                <Fade in={planData && showMore}>
                  <div
                    onClick={() => {
                      setSelectedPlan(planData);
                      setCurrentModal("add-missing-port");
                    }}
                    className={styles.resumeButton}
                  >
                    Add
                  </div>
                </Fade>
              </div>
              <div
                className={styles.singleIccidLabel}
                style={{ marginTop: 12 }}
              >
                ICCID allocation required
                <Fade in={planData && showMore}>
                  <div
                    onClick={() => {
                      setSelectedPlan(planData);
                      setCurrentModal("add-iccid");
                    }}
                    className={styles.resumeButton}
                  >
                    Add ICCID
                  </div>
                </Fade>
              </div>
            </>
          );
        } else {
          return (
            <>
              Missing Porting Details
              <Fade in={planData && showMore}>
                <div
                  onClick={() => {
                    setSelectedPlan(planData);
                    setCurrentModal("add-missing-port");
                  }}
                  className={styles.resumeButton}
                >
                  Add
                </div>
              </Fade>
            </>
          );
        }
      } else {
        if (isPortinMissingIccid()) {
          return (
            <>
              ICCID allocation required
              <Fade in={planData && showMore}>
                <div
                  onClick={() => {
                    setSelectedPlan(planData);
                    setCurrentModal("add-iccid");
                  }}
                  className={styles.resumeButton}
                >
                  Add ICCID
                </div>
              </Fade>
            </>
          );
        } else {
          return (
            <>
              Ready to Activate
              <Fade in={planData && showMore}>
                <div
                  onClick={() => {
                    setSelectedPlan(planData);
                    setCurrentModal("confirm-activate-saved");
                  }}
                  className={styles.resumeButton}
                >
                  Activate
                </div>
              </Fade>
            </>
          );
        }
      }
    } else {
      if (isSubscriptionMissingIccid()) {
        return (
          <>
            ICCID allocation required
            <Fade in={planData && showMore}>
              <div
                onClick={() => {
                  setSelectedPlan(planData);
                  setCurrentModal("add-iccid");
                }}
                className={styles.resumeButton}
              >
                Add ICCID
              </div>
            </Fade>
          </>
        );
      } else {
        return (
          <>
            Ready to Activate
            <Fade in={planData && showMore}>
              <div
                onClick={() => {
                  setSelectedPlan(planData);
                  setCurrentModal("confirm-activate-saved");
                }}
                className={styles.resumeButton}
              >
                Activate
              </div>
            </Fade>
          </>
        );
      }
    }
  };

  const backgroundColors = {
    active: "#E3FEDC",
    rta: getRtaColor("background"),
    iccid: "#FEF9D4",
    suspended: "#FEEBD4",
    tbs: "#FEEBD4",
    cancelled: planData?.remainingDaysToResume <= 0 ? "#B5B5B5" : "#FEE0E5",
    rejected: "#B5B5B5",
    "": "#f2f2f2",
    cancelledPast: "#B5B5B5",
    pending: "#F8EC7E",
    reserved: "#F8EC7E",
    "ban change": "#FEEBD4",
    portout: "#FEEBD4",
  } as any;

  const indicatorColors = {
    active: "#037B53",
    rta: getRtaColor("indicator"),
    iccid: "#EED922",
    suspended: "#F2A446",
    tbs: "#FBCA90",
    cancelled: planData?.remainingDaysToResume <= 0 ? "#1A1A1A" : "#EA3D5C",
    rejected: showMore ? "#B5B5B5" : "#1A1A1A",
    cancelledPast: "#1A1A1A",
    pending: "#F8EC7E",
    reserved: "#F8EC7E",
    "": "#f2f2f2",
    "ban change": "#FBCA90",
    portout: "#FBCA90",
  } as any;

  const tooltipLabels = {
    active: "Active",
    rta: getRtaMessage(),
    iccid: "ICCID allocation required",
    suspended: "Suspended",
    tbs: "To be suspended",
    cancelled: "Cancelled",
    cancelledPast: "Cancelled",
    rejected: "Rejected",
    pending: "Pending Activation",
    reserved: "Reserved",
    "": "No Status",
    "ban change": "Pending BAN change",
    portout: "Porting Out",
  } as any;

  const labels = {
    active: "Active",
    rta: getRtaLabel(),
    iccid: (
      <>
        ICCID allocation required
        <div
          onClick={() => {
            setSelectedPlan(planData);
            setCurrentModal("add-iccid");
          }}
          className={styles.resumeButton}
        >
          Add ICCID
        </div>
      </>
    ),
    suspended: (
      <>
        Subscription Suspended
        <Fade in={planData && showMore}>
          <div
            onClick={() => {
              setSelectedPlan(planData);
              setCurrentModal("confirm-restore");
            }}
            className={styles.resumeButton}
          >
            Restore
          </div>
        </Fade>
      </>
    ),
    tbs: (
      <>
        To be Suspended on DD/MM
        <div className={styles.resumeButton}>Cancel</div>
      </>
    ),
    cancelled:
      planData?.remainingDaysToResume > 0 ? (
        <>
          Cancelled
          <Fade in={planData && showMore}>
            <div
              className={styles.resumeButton}
              onClick={() => {
                setSelectedPlan(planData);
                setCurrentModal("confirm-resume");
              }}
            >
              Resume
            </div>
          </Fade>
        </>
      ) : (
        "Cancelled"
      ),
    cancelledPast: "Cancelled",
    rejected: "Activation Rejected",
    pending: "Pending Activation",
    reserved: "Reserved",
    "": "No Status",
    "ban change": "Pending BAN change",
    portout: "Port Out",
  } as any;

  const [removeLoading, setRemoveLoading] = useState(false);

  const [syncLoading, setSyncLoading] = useState(false);

  const handleLineSync = () => {
    setSyncLoading(true);
    ApiPostAuth(`/accounts/activate/${planData?.id}`)
      .then((response) => {
        setSyncLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response?.data?.message,
          },
        });
        setPlanData(null as any);
        loadExtraData();
      })
      .catch((error) => {
        setSyncLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error?.response?.data?.message,
          },
        });
      });
  };

  return (
    <div
      className={`${styles.main} ${showMore && styles.showMoreMain} ${
        sidebarOpen ? styles.open : styles.closed
      }`}
      ref={mainElement}
    >
      <div
        className={styles.left}
        style={{
          backgroundColor: !showMore ? backgroundColors[status] : "transparent",
        }}
      >
        <div className={styles.flexAlign}>
          <Tooltip
            show
            text={tooltipLabels[status]}
            style={{ marginRight: 12 }}
          >
            <div
              style={{
                backgroundColor: indicatorColors[status],
                border:
                  (status === "pending" ||
                    status === "reserved" ||
                    (status === "rta" &&
                      (!initialPlanData?.iccid ||
                        initialPlanData?.billingAccountNumber === null ||
                        initialPlanData?.billingAccountPassword === null))) &&
                  !showMore
                    ? "1px solid #000"
                    : status === ""
                      ? "1px solid #1a1a1a"
                      : "",
              }}
              className={styles.indicator}
            />
          </Tooltip>
          <div className={styles.planName}>
            {planData ? planData?.product?.product : initialPlanData.product}
          </div>
        </div>
        {(planData?.product?.retailName || initialPlanData?.retailName) && (
          <div className={styles.retailName}>
            {planData
              ? planData?.product?.retailName
              : initialPlanData.retailName}
          </div>
        )}
        <div
          className={`${styles.status} ${showMore && styles[status]} ${
            showMore &&
            status === "rta" &&
            (!initialPlanData?.iccid ||
              initialPlanData?.billingAccountNumber === null ||
              initialPlanData?.billingAccountPassword === null) &&
            styles.iccid
          } ${
            status === "rta" &&
            !initialPlanData?.iccid &&
            initialPlanData?.billingAccountNumber === null &&
            initialPlanData?.billingAccountPassword === null &&
            styles.doubleLabel
          }`}
        >
          {labels[status]}
        </div>
        <Collapse in={showMore}>
          {planData ? (
            <>
              {status === "rta" && (
                <div className={styles.removeSubscription}>
                  {porting ? (
                    <>
                      <Button
                        color="quaternary"
                        style={{
                          textWrap: "nowrap",
                          fontSize: "14px",
                          fontWeight: "600",
                        }}
                        onClick={() => {
                          setSelectedPlan(planData);
                          setCurrentModal("update-temp-port");
                        }}
                        loading={removeLoading}
                      >
                        <Pencil /> Edit Porting Details
                      </Button>
                      <Button
                        color="quaternary"
                        style={{
                          textWrap: "nowrap",
                          fontSize: "14px",
                          fontWeight: "600",
                        }}
                        onClick={() => {
                          setSelectedPlan(planData);
                          setCurrentModal("confirm-delete-saved");
                        }}
                        loading={removeLoading}
                      >
                        <Delete /> Delete
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        color="quaternary"
                        style={{
                          textWrap: "nowrap",
                          fontSize: "14px",
                          fontWeight: "600",
                        }}
                        onClick={() => {
                          setSelectedPlan(planData);
                          setCurrentModal("update-temp-subscription");
                        }}
                      >
                        <Pencil />
                        Edit Details
                      </Button>
                      <Button
                        color="quaternary"
                        style={{
                          textWrap: "nowrap",
                          fontSize: "14px",
                          fontWeight: "600",
                        }}
                        onClick={() => {
                          setSelectedPlan(planData);
                          setCurrentModal("confirm-delete-saved");
                        }}
                        loading={removeLoading}
                      >
                        <Delete /> Delete
                      </Button>
                    </>
                  )}
                </div>
              )}
              {status === "cancelled" &&
                planData?.remainingDaysToResume > 0 && (
                  <div className={styles.restoreDays}>
                    {planData.remainingDaysToResume} day
                    {planData.remainingDaysToResume > 1 && "s"} available to
                    Resume
                  </div>
                )}
              {status !== "rta" && (
                <div className={styles.lineSync}>
                  <div className={styles.lastSynced}>
                    Last synced: {formatSyncedDate(planData?.lastModifiedDate)}
                  </div>
                  <button
                    className={styles.syncButton}
                    onClick={handleLineSync}
                    disabled={syncLoading}
                  >
                    {syncLoading && (
                      <CircularProgress
                        style={{
                          width: 16,
                          height: 16,
                          color: "#000",
                          gridArea: "1 / 1 / 2 / 2",
                          margin: "0 auto",
                        }}
                      />
                    )}
                    <span
                      style={{
                        visibility: syncLoading ? "hidden" : "visible",
                      }}
                      className={styles.content}
                    >
                      <Sync />
                      Sync Status
                    </span>
                  </button>
                </div>
              )}
              {(planData?.errorMessage || status === "rejected") && (
                <div className={styles.failReason}>
                  <div className={styles.failTitle}>
                    <FailReason />
                    Fail Reason:
                  </div>
                  {planData?.errorMessage}
                  {status === "rejected" && (
                    <div className={styles.resubmitButtons}>
                      <button
                        className={styles.whiteButton}
                        style={{ marginRight: 6 }}
                        onClick={() => {
                          setSelectedPlan(planData);
                          setCurrentModal("resubmit-activation-no-update");
                        }}
                      >
                        Resubmit
                      </button>
                      <button
                        className={styles.whiteButton}
                        onClick={() => {
                          setSelectedPlan(planData);
                          setCurrentModal("resubmit-activation");
                        }}
                      >
                        Update & Resubmit
                      </button>
                    </div>
                  )}
                </div>
              )}
              <div style={{ height: 40 }} />
              {featureList.throttle.length !== 0 && (
                <div
                  className={`${styles.featureType} ${
                    (status === "cancelled" ||
                      status === "suspended" ||
                      status === "pending") &&
                    styles.translucent
                  }`}
                >
                  <div className={styles.label}>Throttle</div>
                  <div className={styles.badgeContainer}>
                    {featureList.throttle.map((item: any) => (
                      <FeatureBadge
                        greyscale={
                          (status === "cancelled" &&
                            planData.remainingDaysToResume <= 0) ||
                          status === "rejected"
                        }
                        color="#6361DC"
                        white
                        key={`feature-badge-${item.name}`}
                      >
                        {item.name}
                      </FeatureBadge>
                    ))}
                  </div>
                </div>
              )}
              {featureList.boltons.length !== 0 && (
                <div
                  className={`${styles.featureType} ${
                    (status === "cancelled" ||
                      status === "suspended" ||
                      status === "pending") &&
                    styles.translucent
                  }`}
                >
                  <div className={styles.label}>Bolt-Ons</div>
                  <div className={styles.badgeContainer}>
                    {featureList.boltons.map((item: any) => (
                      <Tooltip
                        show={!!item.effectiveDate}
                        text={`Effective Date: ${formatDate(item.effectiveDate)}`}
                      >
                        <FeatureBadge
                          greyscale={
                            (status === "cancelled" &&
                              planData.remainingDaysToResume <= 0) ||
                            status === "rejected"
                          }
                          color="#F8A0AF"
                          hoverColor={
                            item.effectiveDate ? "#F67E93" : undefined
                          }
                          key={`feature-badge-${item.name}`}
                        >
                          {item.name}
                        </FeatureBadge>
                      </Tooltip>
                    ))}
                  </div>
                </div>
              )}
              {featureList.toggleFeatures.length !== 0 && (
                <div
                  className={`${styles.featureType} ${
                    (status === "cancelled" ||
                      status === "suspended" ||
                      status === "pending") &&
                    styles.translucent
                  }`}
                >
                  <div className={styles.label}>Features</div>
                  <div className={styles.badgeContainer}>
                    {featureList.toggleFeatures.map((item: any) => (
                      <FeatureBadge
                        greyscale={
                          (status === "cancelled" &&
                            planData.remainingDaysToResume <= 0) ||
                          status === "rejected"
                        }
                        color="#CB91F8"
                        key={`feature-badge-${item.name}`}
                      >
                        {item.name}
                      </FeatureBadge>
                    ))}
                  </div>
                </div>
              )}
            </>
          ) : (
            <>
              <div style={{ height: 40 }} />
              <FeaturesLoading />
            </>
          )}
        </Collapse>
      </div>
      <div
        className={`${styles.right} ${
          (status === "cancelled" ||
            status === "suspended" ||
            status === "rejected" ||
            status === "pending") &&
          styles.translucent
        }`}
      >
        <Fade in={!showMore}>
          <div className={styles.mdnPreview}>
            {status === "rta" ? (
              <>
                <div className={styles.planDataLabel}>MDN</div>
                <div className={styles.info}>
                  {porting
                    ? initialPlanData.subscriberNumber
                    : "To be assigned upon activation"}
                </div>
              </>
            ) : (
              <>
                <div>
                  <div className={styles.planDataLabel}>MDN</div>
                  <div className={styles.info}>
                    {initialPlanData.subscriberNumber ||
                      initialPlanData.cancelledMdn ||
                      "-"}
                  </div>
                </div>
                <div>
                  <div className={styles.planDataLabel}>Device Nickname</div>
                  <div className={styles.info}>
                    {initialPlanData?.nickName || "-"}
                  </div>
                </div>
              </>
            )}
          </div>
        </Fade>
        <Collapse in={showMore}>
          {status === "rta" ? (
            <div className={styles.planDataGrid}>
              {planData ? (
                <>
                  <div>
                    <div className={styles.planDataLabel}>MDN</div>
                    <div className={styles.info}>
                      {porting
                        ? planData?.subscriberNumber
                        : "To be assigned upon activation"}
                    </div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>ICCID</div>
                    <div className={styles.info}>
                      {planData?.iccid ? (
                        planData?.iccid
                      ) : (
                        <Button
                          color="quaternary"
                          style={{ height: "auto", padding: 0, fontSize: 14 }}
                          onClick={() => {
                            setSelectedPlan(planData);
                            setCurrentModal("add-iccid");
                          }}
                        >
                          Add
                        </Button>
                      )}
                    </div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>IMEI</div>
                    <div className={styles.info}>{planData?.imei || "-"}</div>
                  </div>
                  {porting && (
                    <div>
                      <div className={styles.planDataLabel}>
                        Old carrier details
                      </div>
                      {planData?.attDetails.oldService.billingAccountNumber &&
                      planData?.attDetails.oldService.billingAccountPassword ? (
                        <div className={styles.info}>
                          PIN:{" "}
                          {
                            planData?.attDetails.oldService
                              .billingAccountPassword
                          }
                          <br />
                          Account No.{" "}
                          {planData?.attDetails.oldService.billingAccountNumber}
                        </div>
                      ) : (
                        <div
                          className={styles.info}
                          style={{ color: "#8C8C8C" }}
                        >
                          to be added
                        </div>
                      )}
                    </div>
                  )}
                </>
              ) : (
                <PlanTileLoading numOfFields={porting ? 4 : 3} />
              )}
            </div>
          ) : (
            <div className={styles.planDataGrid}>
              {planData ? (
                <>
                  <div>
                    <div className={styles.planDataLabel}>MDN</div>
                    <div className={styles.info}>
                      {planData?.mdn || planData?.cancelledMdn || "-"}
                    </div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>Device Nickname</div>
                    <div className={styles.info}>
                      {planData?.nickname || "-"}
                    </div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>Creation Date</div>
                    <div className={styles.info}>
                      {formatDate(planData?.creationDate)}
                    </div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>Activation Date</div>
                    <div className={styles.info}>
                      {formatDate(planData?.activationDate)}
                    </div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>Plan Size</div>
                    <div className={styles.info}>
                      {planData?.product.productSize || "-"}
                    </div>
                  </div>
                  <div>
                    <div>
                      <div className={styles.planDataLabel}>
                        Total Data Usage (GB)
                      </div>
                      <div className={styles.info}>
                        <p>{planData?.totalDataUsageGB.toFixed(2)} GB</p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div>
                      <div className={styles.planDataLabel}>
                        Total Data Usage (MB)
                      </div>
                      <div className={styles.info}>
                        <p>{planData?.totalDataUsageMB?.toFixed(2)} MB</p>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>BAN</div>
                    <div className={styles.info}>{planData?.ban || "-"}</div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>Service Type</div>
                    <div className={styles.info}>
                      {planData?.product.serviceType || "-"}
                    </div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>ICCID</div>
                    <div className={styles.info}>{planData?.iccid || "-"}</div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>IMEI</div>
                    <div className={styles.info}>{planData?.imei}</div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>Bill Cycle</div>
                    <div className={styles.info}>
                      {planData?.nextBillCycleDate
                        ? formatDate(planData?.nextBillCycleDate)
                        : "-"}
                    </div>
                  </div>
                  {planData?.kbBankGB < 1 ? (
                    <div>
                      <div>
                        <div className={styles.planDataLabel}>
                          Data Balance (MB)
                        </div>
                        <div className={styles.info}>
                          <p>{planData?.kbBankMB} MB</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div>
                        <div className={styles.planDataLabel}>
                          Data Balance (GB)
                        </div>
                        <div className={styles.info}>
                          <p>{planData?.kbBankGB} GB</p>
                        </div>
                      </div>
                    </div>
                  )}
                  <div>
                    <div className={styles.planDataLabel}>Hotline</div>
                    <div className={styles.info}>
                      {planData?.hotline || "N/A"}
                    </div>
                  </div>
                  <div>
                    <div className={styles.planDataLabel}>Expiry Date</div>
                    <div className={styles.info}>
                      {planData?.userExpiryDate
                        ? formatDate(planData?.userExpiryDate)
                        : "N/A"}
                    </div>
                  </div>
                </>
              ) : (
                <PlanTileLoading numOfFields={12} />
              )}
            </div>
          )}
        </Collapse>
      </div>
      <div
        className={`${styles.controlPanel} ${showMoreButtons && styles.open} ${
          showMoreButtons && styles.showingMoreButtons
        }`}
      >
        <Tooltip white show text={showMore ? "Hide details" : "Show details"}>
          <button
            onClick={() => {
              /*if (status === "ban change") {
                dispatch({
                  type: "notify",
                  payload: {
                    error: true,
                    message:
                      "This subscription is pending BAN change action, please try again later",
                  },
                });
              } else {*/
              setShowMore((prev: boolean) => {
                if (prev) {
                  setShowMoreButtons(false);
                }
                return !prev;
              });
              //}
            }}
            className={`${styles.controlButton} ${
              showMore && styles.showingMore
            }`}
            style={{ width: 44 }}
          >
            <CaretDown />
          </button>
        </Tooltip>
        <Collapse in={showMore}>
          <div className={styles.collapseContainer}>
            <Tooltip
              white
              show
              text={showMoreButtons ? "Collapse" : "Expand"}
              style={{
                margin: "12px 0px",
                alignSelf: "center",
                width: 44,
              }}
            >
              <button
                onClick={() => {
                  setShowMoreButtons((prev: boolean) => !prev);
                }}
                className={`${styles.controlButton} ${
                  showMoreButtons && styles.showingMore
                }`}
              >
                <ArrowBack />
              </button>
            </Tooltip>
            {(planData?.mdn ||
              planData?.cancelledMdn ||
              planData?.subscriberNumber) && (
              <Tooltip
                white
                show={!showMoreButtons}
                text="MDN Activity Log"
                style={{ width: "100%" }}
              >
                <button
                  onClick={() => {
                    navigate(
                      `/${mvnoId}/mdn-activity/${subscriberId}/${planData?.mdn || planData?.cancelledMdn || planData?.subscriberNumber}`,
                    );
                  }}
                  style={{ marginBottom: 12 }}
                  className={styles.controlButton}
                  disabled={!planData}
                >
                  <ClockTimer />
                  <div
                    className={styles.buttonLabel}
                    style={{ fontSize: 11, lineHeight: "16px" }}
                  >
                    MDN
                    <br />
                    Activity Log
                  </div>
                </button>
              </Tooltip>
            )}
            {status !== "rejected" &&
              status !== "pending" &&
              status !== "" &&
              status !== "rta" &&
              status !== "iccid" &&
              buttonsToShow[status]?.map(
                (buttonKey: string) => buttonComponents[buttonKey],
              )}
          </div>
        </Collapse>
      </div>
    </div>
  );
};

export default SubscriberPlan;
