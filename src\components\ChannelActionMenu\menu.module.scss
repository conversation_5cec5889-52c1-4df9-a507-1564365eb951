@use "../../styles/theme.scss" as *;

.menuButton {
  height: 41px;
  width: 44px;
  background: none;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  cursor: pointer;
  &:hover {
    background-color: #fff;
  }
  svg {
    vertical-align: middle;
  }
}

.box {
  height: 41px;
}

.menuItem {
  padding: 6px 12px;
  display: flex;
  align-items: center;
  height: 36px;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: $black;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  &:last-child {
    margin-bottom: 0px;
  }
  svg {
    margin-right: 12px;
  }
  &:hover {
    color: $orange;
    background-color: #fff;
  }
}
