import styles from "../../styles/user-management.module.scss";
import { Pencil, SendPassword, Delete, AddUser } from "../../components/svgs";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Button from "../../components/Button";
import Pagination from "../../components/Pagination";
import RoleBadge from "../../components/RoleBadge";
import Tooltip from "../../components/Tooltip";
import AddUserModal from "../../components/AddUserModal";
import ChangePasswordModal from "../../components/ChangePasswordModal";
import UserSkeleton from "../../components/UserSkeleton";
import DeleteUserModal from "../../components/DeleteUserModal";
import EditUserModal from "../../components/EditUserModal";
import StatusPill from "../../components/StatusPill";
import { padArrayToLength } from "../../components/utils/padArray";
import { ApiGet, ApiGetNoAuth, ApiPatch, ApiPost } from "../api/api";
import SearchSection from "../../components/SearchSection";
import ViewUserModal from "../../components/ViewUserModal";
import { useNavigate, useParams } from "react-router-dom";
import RadioSelect from "../../components/RadioSelect";
import qs from "qs";
import SearchBar from "../../components/SearchBar";
import { highlightSearch } from "../../components/utils/searchAndFilter";

const UserManagement = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [initialLoading, setInitialLoading] = useState(true);

  const { mvnoId } = useParams();
  const { userInfo } = useSelector((state: any) => state);

  useEffect(() => {
    if (userInfo.roleName === "Agent") {
      navigate(`/${mvnoId}/subscriber-management`);
    }
  }, [userInfo]);

  const repopulateUsers = () => {
    const params = {
      currentPage: currentPage - 1,
      pageSize: usersPerPage,
      search: searchQuery,
    };
    const stringifiedQueryParams = qs.stringify(params, { indices: false });

    setInitialLoading(true);
    ApiGet(`/users/${mvnoId}?${stringifiedQueryParams}`)
      .then((response) => {
        setUsersData(response.data.crmUsers.content);
        setTotalPages(response.data.crmUsers.totalPages);
        setInitialLoading(false);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const [usersData, setUsersData] = useState([] as any);

  const [currentPage, setCurrentPage] = useState(1);
  const usersPerPage = 8;
  const [totalPages, setTotalPages] = useState(0);

  const [searchInputValue, setSearchInputValue] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  /***********   Add User     ***********/

  const [showAddUserModal, setShowAddUserModal] = useState(false);

  /**********    Send Password Reset    **********/

  const [showPasswordRestModal, setShowPasswordResetModal] = useState(false);

  const [activeUser, setActiveUser] = useState(null as any);

  // Handles sending password reset email to user
  const handleSendResetPassword = (user: any) => {
    setActiveUser(user);
    setShowPasswordResetModal(true);
  };

  /********       Delete User         **********/

  const [showDeleteUserModal, setShowDeleteUserModal] = useState(false);

  // Handles deleting user
  const handleDeleteUser = (user: any) => {
    setActiveUser(user);
    setShowDeleteUserModal(true);
  };

  /********       Edit User         **********/

  const [showEditUserModal, setShowEditUserModal] = useState(false);
  const [showViewUserModal, setShowViewUserModal] = useState(false);

  // Handles edit user
  const handleEditUser = (user: any) => {
    setActiveUser(user);
    setShowEditUserModal(true);
  };

  const handleViewUser = (user: any) => {
    setActiveUser(user);
    setShowViewUserModal(true);
  };

  const handleChangeRole = (userDetails: any, newRole: any) => {
    ApiPatch("/users/edit", {
      userId: userDetails.userId,
      firstName: userDetails.firstName,
      lastName: userDetails.lastName,
      email: userDetails.email,
      enable2fa: userDetails.is2faEnabled,
      roleId: newRole === "Agent" ? 1 : 2,
    })
      .then((response) => {
        repopulateUsers();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };
  const handleChangeStatus = (userDetails: any, newStatus: any) => {
    ApiPatch("/users/edit/status", {
      userMid: userDetails.userId,
      statusId: newStatus === "Active" ? 1 : 2,
    })
      .then((response) => {
        repopulateUsers();
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  const handleSearch = () => {
    setSearchQuery(searchInputValue);
    setCurrentPage(1);
  };

  useEffect(repopulateUsers, [currentPage, searchQuery]);

  return (
    <div className={styles.main}>
      <AddUserModal
        show={showAddUserModal}
        setShow={setShowAddUserModal}
        repopulateUsers={repopulateUsers}
      />
      <ChangePasswordModal
        show={showPasswordRestModal}
        setShow={setShowPasswordResetModal}
        user={activeUser}
        id={activeUser ? activeUser.userId : 0}
      />
      <DeleteUserModal
        show={showDeleteUserModal}
        setShow={setShowDeleteUserModal}
        user={activeUser}
        repopulateUsers={repopulateUsers}
      />
      <ViewUserModal
        show={showViewUserModal}
        setShow={setShowViewUserModal}
        user={activeUser}
        handleEditUser={handleEditUser}
      />
      <EditUserModal
        show={showEditUserModal}
        setShow={setShowEditUserModal}
        user={activeUser}
        repopulateUsers={repopulateUsers}
        resetActiveUser={() => {
          setActiveUser(null as any);
          setShowViewUserModal(false);
        }}
        id={activeUser ? activeUser.userId : 0}
        clearContainer={showViewUserModal}
      />
      <SearchBar
        placeholder="Search by First Name, Last Name or Email"
        id="user-search"
        onSubmit={handleSearch}
        query={searchInputValue}
        setQuery={setSearchInputValue}
      />
      <div className={styles.titleBar}>
        <h3>User Management</h3>
        <Button
          style={{ marginLeft: "auto" }}
          onClick={() => {
            setShowAddUserModal(true);
          }}
        >
          <AddUser />
          Add User
        </Button>
      </div>
      <div className={styles.usersPanel}>
        <div className={`${styles.tableContainer} table-scroll`}>
          <table>
            <thead>
              <tr>
                <th>First Name</th>
                <th>Last Name</th>
                <th>Email</th>
                <th>Channels</th>
                <th>Role</th>
                <th>Status</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {!initialLoading ? (
                usersData.length !== 0 ? (
                  padArrayToLength(usersData, usersPerPage, null).map(
                    (singleUser: any) => {
                      if (singleUser === null) {
                        return (
                          <tr
                            style={{
                              visibility: "hidden",
                              pointerEvents: "none",
                            }}
                          ></tr>
                        );
                      } else {
                        return (
                          <tr
                            onClick={() => {
                              handleViewUser(singleUser);
                            }}
                            key={"user-row-" + singleUser.userId}
                            style={{ cursor: "pointer" }}
                          >
                            <td>
                              {highlightSearch(
                                singleUser.firstName,
                                searchQuery,
                              )}
                            </td>
                            <td>
                              {highlightSearch(
                                singleUser.lastName,
                                searchQuery,
                              )}
                            </td>
                            <td>
                              {highlightSearch(singleUser.email, searchQuery)}
                            </td>
                            <td>
                              {singleUser.channels.length > 0
                                ? singleUser.channels.map(
                                    (channel: any, i: number) =>
                                      `${i !== 0 ? ", " : ""}${channel.name}`,
                                  )
                                : "-"}
                            </td>
                            <td>
                              <div
                                style={{
                                  display: "flex",
                                  justifyContent: "flex-start",
                                }}
                              >
                                <RadioSelect
                                  label={
                                    <RoleBadge role={singleUser.roleName} />
                                  }
                                  options={[
                                    {
                                      label: <RoleBadge role="Agent" />,
                                      key: "Agent",
                                    },
                                    {
                                      label: <RoleBadge role="Admin" />,
                                      key: "Admin",
                                    },
                                  ]}
                                  selected={singleUser.roleName}
                                  onChange={(e: any) => {
                                    handleChangeRole(singleUser, e);
                                  }}
                                />
                              </div>
                            </td>
                            <td>
                              <div
                                style={{
                                  display: "flex",
                                  justifyContent: "flex-start",
                                }}
                              >
                                <RadioSelect
                                  label={
                                    <StatusPill status={singleUser.status} />
                                  }
                                  options={[
                                    {
                                      label: <StatusPill status="Active" />,
                                      key: "Active",
                                    },
                                    {
                                      label: <StatusPill status="Inactive" />,
                                      key: "Inactive",
                                    },
                                  ]}
                                  selected={singleUser.status}
                                  onChange={(e: any) => {
                                    handleChangeStatus(singleUser, e);
                                  }}
                                />
                              </div>
                            </td>
                            <td>
                              <div className={styles.actionPanel}>
                                <Tooltip
                                  show
                                  text="Edit User"
                                  style={{ marginRight: 12 }}
                                >
                                  <button
                                    className={styles.actionButton}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleEditUser(singleUser);
                                    }}
                                  >
                                    <Pencil />
                                  </button>
                                </Tooltip>
                                <Tooltip
                                  show
                                  text="Change Password"
                                  style={{ marginRight: 12 }}
                                >
                                  <button
                                    className={styles.actionButton}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleSendResetPassword(singleUser);
                                    }}
                                  >
                                    <SendPassword />
                                  </button>
                                </Tooltip>
                                <Tooltip
                                  show
                                  text="Delete User"
                                  style={
                                    singleUser.userId === userInfo.mid
                                      ? {
                                          pointerEvents: "none",
                                          visibility: "hidden",
                                        }
                                      : {}
                                  }
                                >
                                  <button
                                    className={styles.actionButton}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteUser(singleUser);
                                    }}
                                  >
                                    <Delete />
                                  </button>
                                </Tooltip>
                              </div>
                            </td>
                          </tr>
                        );
                      }
                    },
                  )
                ) : (
                  <tr style={{ background: "none" }}>
                    <td colSpan={100}>
                      <div className={styles.noneFound}>
                        <img src="/none_found.svg" />
                        <h3>
                          We couldn't find anything matching
                          {searchQuery ? <>" {searchQuery}"</> : "."}
                        </h3>
                      </div>
                    </td>
                  </tr>
                )
              ) : (
                Array.from({ length: usersPerPage }, (v, i) => i).map((i) => (
                  <UserSkeleton
                    key={"user-skeleton-" + i}
                    noOfStandard={5}
                    showActionBar
                  />
                ))
              )}
            </tbody>
          </table>
        </div>
        <div className={styles.pagination}>
          <Pagination
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            numberOfPages={totalPages}
          />
        </div>
      </div>
    </div>
  );
};

export default UserManagement;
