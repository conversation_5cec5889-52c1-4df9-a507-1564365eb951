@use "../../styles/theme.scss" as *;

.mainGrid {
  display: flex;
  gap: 18px;
  margin-top: 12px;
}

.panel {
  background-color: #fff;
  width: 100%;
  border-radius: 24px;
  padding: 23px 32px 40px 32px;
  font-size: 14px;
  line-height: 21px;
  .panelTitle {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 12px;
  }
}

.spread {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dataName {
  color: #666666;
  font-size: 12px;
  line-height: 18px;
}

.dataValue {
  margin-bottom: 16px;
}

.subGrid {
  margin-top: 21px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-column-gap: 18px;

  .columnTitle {
    color: #666666;
    margin-bottom: 8px;
  }
}

.statusContainer {
  display: flex;
  align-items: center;
  width: 100%;
  .lineSync {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-left: 8px;
    margin-bottom: auto;
    .lastSynced {
      font-weight: 400;
      font-size: 10px;
      line-height: 100%;
      line-height: 15px;
      margin-top: 4px;
    }
    .syncButton {
      display: grid;
      align-items: center;
      justify-content: center;
      background-color: #f7f6f6;
      border: none;
      outline: none;
      border-radius: 53px;
      height: 30px;
      padding: 0px 8px;
      font-weight: 600;
      font-size: 12px;
      white-space: nowrap;
      cursor: pointer;
      &:hover {
        background-color: #dbdbdb;
      }
      &:disabled {
        cursor: auto;
        &:hover {
          background-color: #e3e3e3;
        }
      }
      .content {
        grid-area: 1 / 1 / 2 / 2;
        display: flex;
        align-items: center;
        svg {
          vertical-align: middle;
          margin-right: 8px;
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}

.activationFailureCallout {
   
}