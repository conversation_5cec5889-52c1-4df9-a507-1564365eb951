import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import Modal from "../Modal";
import styles from "./add-family-plan-members-modal.module.scss";
import { ArrowBack, CaretDown, Info, PencilSimple, Plus, Tick } from "../svgs";
import { Input } from "../Input";
import Button from "../Button";
import { Collapse, IconButton } from "@mui/material";
import { ApiGet, ApiPostAuth } from "../../pages/api/api";
import Spinner from "../Spinner";
import { validateEmail } from "../utils/CardDetailsCheckers";
import { ControlledMenu } from "@szhsin/react-menu";
import Radio from "../Radio";
import ActivateSubscriberModal from "../ActivateSubscriberModal";
import AddSubscriberModal from "../AddSubscriberModal";
import { useDispatch } from "react-redux";
import clsx from "clsx";
import { useNavigate, useParams } from "react-router-dom";

type Steps = "add-susbscriptions" | "review";

type AddFamilyPlanMembersModalProps = {
  show: boolean;

  /**
   * Email of the user account where family plan is created from. Used to prefill the email field for the first member.
   */
  originUserEmail?: string;

  onClose: () => void;
  familyPlanId?: number;
  onComplete?: () => void;
};

const AddFamilyPlanMembersModal = ({
  show,
  originUserEmail,
  onClose,
  familyPlanId,
  onComplete,
}: AddFamilyPlanMembersModalProps) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { mvnoId } = useParams();

  // don't set admin automatically if family plan id is provided as family already has admin
  const defaultMemberSubscription = useMemo(() => {
    return familyPlanId ? [{}] : [{ isAdmin: true }];
  }, [familyPlanId]);

  const [step, setStep] = useState<Steps>("add-susbscriptions");
  const [memberSubscriptions, setMemberSubscriptions] = useState<
    MemberSubscriptionState[]
  >(() => {
    return familyPlanId ? [{}] : [{ isAdmin: true, email: originUserEmail }];
  });

  const updateMemberSubscription = (
    index: number,
    subscriptionData: MemberSubscriptionState,
  ) => {
    setMemberSubscriptions((existingMemberSubscription) => {
      const newSubscriptions = [...existingMemberSubscription];
      newSubscriptions[index] = {
        ...existingMemberSubscription[index],
        ...subscriptionData,
      };
      return newSubscriptions;
    });

    // Clear validation errors for this member subscription when updated
    setValidationErrors((prevErrors) => {
      const newErrors = { ...prevErrors };
      delete newErrors[index];
      return newErrors;
    });
  };

  const addMemberSubscription = () => {
    // Validate all existing subscriptions before allowing new member addition
    const validation = validateAllSubscriptions();
    if (!validation.isValid) {
      // Set validation errors for display
      const errors: { [key: number]: string[] } = {};
      validation.invalidSubscriptions.forEach(({ index, errors: memberErrors }) => {
        errors[index] = memberErrors;
      });
      setValidationErrors(errors);

      // Show notification
      dispatch({
        type: "notify",
        payload: {
          error: true,
          message: "Please complete all current member subscriptions before adding new members.",
        },
      });
      return;
    }

    // Clear validation errors if validation passes
    setValidationErrors({});

    setMemberSubscriptions([...memberSubscriptions, {}]);
    // scroll to bottom
    setTimeout(() => {
      modalContentRef.current?.scrollTo({
        left: 0,
        top: modalContentRef.current?.scrollHeight,
        behavior: "smooth",
      });
    }, 100);
  };

  const removeMemberSubscription = (index: number) => {
    const newSubscriptions = [...memberSubscriptions];

    // if not existing family and subscription being removed is admin, set previous subscription as admin
    if (!familyPlanId && newSubscriptions[index].isAdmin) {
      const prevIndex = index - 1 < 0 ? 0 : index - 1;
      newSubscriptions[prevIndex].isAdmin = true;
    }

    newSubscriptions.splice(index, 1);

    if (newSubscriptions.length === 0) {
      newSubscriptions.push(...defaultMemberSubscription);
    }

    setMemberSubscriptions(newSubscriptions);

    // Clear validation errors for this member subscription when removed
    setValidationErrors((prevErrors) => {
      const newErrors = { ...prevErrors };
      delete newErrors[index];
      return newErrors;
    });
  };

  const getMemberSubscriptionUpdaters = (index: number) => {
    return {
      setEmail: (email: string) => {
        updateMemberSubscription(index, {
          email,
        });
      },
      setExistingSubscription: (
        subscription: MemberSubscriptionState["existingSubscription"],
      ) => {
        updateMemberSubscription(index, {
          existingSubscription: subscription,
          newSubscription: undefined,
          type: "existing",
        });
      },
      setNewSubscription: (
        subscription: MemberSubscriptionState["newSubscription"],
      ) => {
        updateMemberSubscription(index, {
          newSubscription: subscription,
          existingSubscription: undefined,
          type: "new",
        });
      },
      resetSubscription: () => {
        setMemberSubscriptions((m) => {
          // keep admin status
          const isAdmin = m[index].isAdmin;

          return [...m.slice(0, index), { isAdmin }, ...m.slice(index + 1)];
        });

        // Clear validation errors for this member subscription when reset
        setValidationErrors((prevErrors) => {
          const newErrors = { ...prevErrors };
          delete newErrors[index];
          return newErrors;
        });
      },
      // set user email data from 'check email'
      setUserEmailData: (
        userEmailData: MemberSubscriptionState["userEmailData"],
      ) => {
        updateMemberSubscription(index, {
          userEmailData,
        });
      },
      // set whether email has an account. use undefined to unset
      setEmailHasAccount: (emailHasAccount: boolean | undefined) => {
        updateMemberSubscription(index, {
          emailHasAccount: emailHasAccount,
        });
      },
      // set chosen subscription as admin and unset others
      setAdmin: () => {
        setMemberSubscriptions((m) =>
          m.map((subscription, i) => {
            if (i === index) {
              return {
                ...subscription,
                isAdmin: true,
              };
            }
            return {
              ...subscription,
              isAdmin: false,
            };
          }),
        );
      },
    };
  };

  const [createFamilyPlanLoading, setCreateFamilyPlanLoading] = useState(false);

  // Validation logic for subscription completeness
  const validateMemberSubscription = (subscription: MemberSubscriptionState): {
    isValid: boolean;
    errors: string[];
  } => {
    const errors: string[] = [];

    // Check if email is provided and valid
    if (!subscription.email) {
      errors.push("Email must be provided and verified by clicking 'Check email'");
    } else if (!validateEmail((subscription.email || ""))) {
      errors.push("Valid email format is required");
    }

    // Check if email has been checked (only if email is valid)
    if (subscription.email && validateEmail(subscription.email) && subscription.emailHasAccount === undefined) {
      errors.push("Email must be verified by clicking 'Check email'");
    }

    // Check if subscription type is selected and has required data
    // Only validate subscription selection if email has been checked
    if (subscription.emailHasAccount !== undefined) {
      if (!subscription.type) {
        if (subscription.emailHasAccount) {
          errors.push("Please select an existing subscription or create a new one");
        } else {
          errors.push("Please create an account and subscription for this member");
        }
      } else if (subscription.type === "existing") {
        if (!subscription.existingSubscription) {
          errors.push("Please select an existing subscription from the dropdown or create one");
        }
      } else if (subscription.type === "new") {
        if (!subscription.newSubscription) {
          errors.push("Please complete the new subscription creation process");
        } else {
          const newSub = subscription.newSubscription;
          if (!newSub.imei) errors.push("IMEI is required for new subscription");
          if (!newSub.iccid) errors.push("ICCID is required for new subscription");
          if (!newSub.product) errors.push("Product selection is required for new subscription");
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  // Check if all subscriptions are valid
  const validateAllSubscriptions = (): {
    isValid: boolean;
    invalidSubscriptions: { index: number; errors: string[] }[];
  } => {
    const invalidSubscriptions: { index: number; errors: string[] }[] = [];

    memberSubscriptions.forEach((subscription, index) => {
      const validation = validateMemberSubscription(subscription);
      if (!validation.isValid) {
        invalidSubscriptions.push({
          index,
          errors: validation.errors,
        });
      }
    });

    return {
      isValid: invalidSubscriptions.length === 0,
      invalidSubscriptions,
    };
  };

  // State for validation errors
  const [validationErrors, setValidationErrors] = useState<{
    [key: number]: string[];
  }>({});

  const handleCreateFamilyPlan = () => {
    setCreateFamilyPlanLoading(true);
    const requestUrl = familyPlanId
      ? "/accounts/familyplan/add-member"
      : "/accounts/familyplan/create";

    ApiPostAuth(
      requestUrl,
      transformSubscriptionsForAPI(memberSubscriptions, familyPlanId),
    )
      .then((response) => {
        const { adminSubscriptionId, adminMid } = response.data;

        if (onComplete) {
          onComplete();
        } else {
          navigate(
            `/${mvnoId}/subscription-details/${adminMid}/${adminSubscriptionId}?tab=manage-family`,
          );
        }

        onClose();

        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        setCreateFamilyPlanLoading(false);
      })
      .catch((error) => {
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
        setCreateFamilyPlanLoading(false);
      });
  };

  // Used to prevent using the same MDN twice
  const usedMDNS = useMemo(() => {
    return memberSubscriptions
      .map(
        (subscription) => subscription.existingSubscription?.subscriberNumber,
      )
      .filter(Boolean) as string[];
  }, [memberSubscriptions]);

  const getTitle = () => {
    if (step === "add-susbscriptions") {
      return "Add Family Members";
    }
    if (step === "review") {
      return "Review";
    }
    return "";
  };

  const getSubtitle = () => {
    if (step === "add-susbscriptions") {
      return "Enter the emails of the family members. If an MDN doesn’t exist, you’ll need to provide more details to create their account.";
    }
    return "";
  };

  const getSaveButtonText = () => {
    if (step === "add-susbscriptions") {
      return "Continue";
    }

    const addFamilyMembersText =
      memberSubscriptions.length > 1
        ? "Add Family Members"
        : "Add Family Member";

    return familyPlanId ? addFamilyMembersText : "Activate Family Plan";
  };

  const getProceedAction = () => {
    if (step === "add-susbscriptions") {
      return () => {
        // Validate all subscriptions before proceeding to review
        const validation = validateAllSubscriptions();
        if (!validation.isValid) {
          // Set validation errors for display
          const errors: { [key: number]: string[] } = {};
          validation.invalidSubscriptions.forEach(({ index, errors: memberErrors }) => {
            errors[index] = memberErrors;
          });
          setValidationErrors(errors);

          // Show notification
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: "Please complete all member subscriptions before continuing.",
            },
          });
          return;
        }

        // Clear validation errors if validation passes
        setValidationErrors({});
        setStep("review");
      };
    }
    return handleCreateFamilyPlan;
  };

  const renderHeaderBackComponent = useMemo(() => {
    if (step === "review") {
      return <Back onClick={() => setStep("add-susbscriptions")} />;
    }

    return undefined;
  }, [step]);

  const [showCancelConfirmModal, setShowCancelConfirmModal] = useState(false);

  const modalContentRef = useRef<HTMLDivElement>(null);

  return (
    <Modal
      title={getTitle()}
      subtitle={getSubtitle()}
      show={show}
      close={() => setShowCancelConfirmModal(true)}
      image="/bulk_edit_confirm_graphic.svg"
      saveButton={getSaveButtonText()}
      loading={createFamilyPlanLoading}
      proceed={getProceedAction()}
      fullSize
      headerBackComponent={renderHeaderBackComponent}
      contentRef={modalContentRef}
    >
      <CancelConfirmModal
        show={showCancelConfirmModal}
        onClose={() => setShowCancelConfirmModal(false)}
        onConfirm={onClose}
      />
      <div className={styles.main}>
        {/* Add Subscriptions Step */}
        {step === "add-susbscriptions" && (
          <>
            <div className={styles.memberSubscriptions}>
              {memberSubscriptions.map((subscription, index) => (
                <MemberSubscription
                  index={index}
                  key={index}
                  subscription={subscription}
                  updaters={getMemberSubscriptionUpdaters(index)}
                  removeCurrentSubscription={() =>
                    removeMemberSubscription(index)
                  }
                  usedMDNS={usedMDNS}
                  validationErrors={validationErrors[index] || []}
                />
              ))}
            </div>
            <Button
              onClick={addMemberSubscription}
              color="secondary"
              className={styles.addSubscriptionBtn}
            >
              <Plus />
              <span>Add New Member</span>
            </Button>
          </>
        )}

        {/* Review and Submission Step */}
        {step === "review" && (
          <ReviewStep
            subscriptions={memberSubscriptions}
            familyPlanId={familyPlanId}
            setStep={setStep}
          />
        )}
      </div>
    </Modal>
  );
};

export default AddFamilyPlanMembersModal;

type MemberSubscriptionState = {
  type?: "existing" | "new";
  email?: string;
  isAdmin?: boolean;
  emailHasAccount?: boolean;
  userEmailData?: {
    subscriptions: any[];
    mid: number;
  };
  existingSubscription?: {
    subscriberName: string;
    subscriptionId: number;
    subscriberNumber: string;
    email: string;
    offerName: string;
    size: string;
    retailPrice: string;
  };
  newSubscription?: {
    imei: string;
    iccid: string;
    mid?: number;
    product: {
      deviceType: string;
      offerId: string;
      offerName: string;
      productFamily: string;
      size: string;
      soc: string;
      serviceType: string;
      retailName: string;
      retailPrice: string;
    };
  };
};

type MemberSubscriptionProps = {
  subscription: MemberSubscriptionState;
  updaters: {
    // set only email for current subscription
    setEmail: (email: string) => void;
    // set existing subscription data for current subscription
    setExistingSubscription: (
      subscription: MemberSubscriptionState["existingSubscription"],
    ) => void;
    // set new subscription data for current subscription
    setNewSubscription: (
      subscription: MemberSubscriptionState["newSubscription"],
    ) => void;
    // reset subscription data
    resetSubscription: () => void;
    // set user email data from 'check email'
    setUserEmailData: (
      userEmailData: MemberSubscriptionState["userEmailData"],
    ) => void;
    // set chosen subscription as admin and unset others
    setAdmin: () => void;
    setEmailHasAccount: (emailHasAccount: boolean | undefined) => void;
  };
  index: number;
  removeCurrentSubscription: () => void;
  usedMDNS: string[];
  validationErrors: string[];
};

const MemberSubscription = ({
  subscription,
  updaters,
  index,
  removeCurrentSubscription,
  usedMDNS,
  validationErrors,
}: MemberSubscriptionProps) => {
  const dispatch = useDispatch();

  const [emailValue, setEmailValue] = useState(subscription.email || "");
  const [collapseOpen, setCollapseOpen] = useState(true);

  // Boolean to hide some fields and UI elements if subscription has been created or selected for this member
  const isSubscriptionAdded = Boolean(
    subscription.existingSubscription || subscription.newSubscription,
  );

  const userEmailData = subscription.userEmailData;
  const emailSubscriptions = userEmailData?.subscriptions || [];
  const [emailCheckLoading, setEmailCheckLoading] = useState(false);
  const emailChecked = subscription.emailHasAccount !== undefined;
  const isEmailValid = validateEmail(emailValue);
  const emailInputRef = useRef<HTMLInputElement | null>(null);

  const emailCheckBtnContent = useMemo(() => {
    if (emailCheckLoading) {
      return <Spinner size={24} />;
    }
    if (emailChecked) {
      return "Change email";
    }
    return "Check email";
  }, [emailCheckLoading, emailChecked]);

  const handleCheckEmail = async (email: string) => {
    setEmailCheckLoading(true);
    updaters.setEmailHasAccount(undefined);
    updaters.resetSubscription();
    updaters.setEmail(email);

    try {
      const response = await ApiGet(
        `/accounts/subscriptions/active?email=${email}`,
      );
      updaters.setEmailHasAccount(true);
      updaters.setUserEmailData(response.data);
    } catch (err) {
      if ((err as any)?.response?.status === 404) {
        updaters.setEmailHasAccount(false);
      } else {
        console.log("error checking email: ", err);

        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: `Error checking email: ${(err as any)?.message || "Something went wrong. Try again."}`,
          },
        });
      }
    } finally {
      setEmailCheckLoading(false);
    }
  };

  const handleChangeEmail = () => {
    updaters.resetSubscription();

    // hack to wait for input to become enabled before focusing on email input as a disabled input cannot be focused
    // this is a bit of a hit or miss as there's no guarantee that the input will be enabled after 300ms but it seems to work for now
    setTimeout(() => {
      emailInputRef.current?.focus();
    }, 300);
  };

  const [showActivateSubscriberModal, setShowActivateSubscriberModal] =
    useState(false);
  const [showAddSubscriberModal, setShowAddSubscriberModal] = useState(false);

  // Initial data to support editing newly added subscriptions
  const activateSubscriberModalInitialData = useMemo(() => {
    if (!subscription.newSubscription) return null;

    return {
      imei: subscription.newSubscription?.imei || "",
      iccid: subscription.newSubscription?.iccid || "",
      product: subscription.newSubscription?.product || null,
    };
  }, [subscription.newSubscription]);

  return (
    <div className={styles.memberSubscription}>
      <div
        className={styles.memberSubscriptionHeader}
        onClick={() => setCollapseOpen(!collapseOpen)}
        role="button"
        aria-expanded={collapseOpen}
      >
        <div className={styles.memberSubscriptionHeaderTitle}>
          Member {index + 1}
        </div>

        {subscription.isAdmin ? (
          <div className={styles.isAdmin}>
            <Tick />
            <span>Admin</span>
          </div>
        ) : (
          <button
            className={styles.setAdmin}
            onClick={(e) => {
              updaters.setAdmin();
              e.stopPropagation();
            }}
          >
            Set as Admin
          </button>
        )}

        <button
          className={styles.removeMemberSubscriptionBtn}
          onClick={(e) => {
            removeCurrentSubscription();
            // stop propagation to prevent opening/closing the collapse
            e.stopPropagation();
          }}
        >
          Remove
        </button>

        <button
          className={`${styles.memberSubscriptionHeaderCaret} ${collapseOpen && styles.open}`}
          onClick={() => setCollapseOpen(!collapseOpen)}
        >
          <CaretDown />
        </button>
      </div>

      <Collapse in={collapseOpen}>
        <div className={styles.memberSubscriptionBody}>
          {/* Email field... */}
          <div className={styles.memberSubscriptionEmailField}>
            <div className={styles.emailFieldContainer}>
              <div className={styles.inputWrapper}>
                <Input
                  label="Email"
                  placeholder="Enter email"
                  value={emailValue}
                  onChange={(e: any) =>
                    setEmailValue(removeSpaces(e.target.value))
                  }
                  disabled={emailCheckLoading || emailChecked}
                  clear={() => setEmailValue("")}
                  ref={emailInputRef}
                />
              </div>

              {/* Check or change email button */}
              <button
                style={{ marginLeft: 12 }}
                onClick={() => {
                  if (emailChecked) {
                    handleChangeEmail();
                  } else {
                    handleCheckEmail(emailValue);
                  }
                }}
                disabled={!isEmailValid || emailCheckLoading}
                title={!isEmailValid ? "Please enter a valid email" : undefined}
              >
                {emailCheckBtnContent}
              </button>
            </div>

            {/* Email check result */}
            {!isSubscriptionAdded && emailChecked && (
              <div
                className={`${styles.emailCheckResult} ${subscription.emailHasAccount && styles.hasAccount}`}
              >
                <div className={styles.emailCheckResultIcon}>
                  {subscription.emailHasAccount ? <Tick /> : <Info />}
                </div>
                <div className={styles.emailCheckResultText}>
                  {subscription.emailHasAccount
                    ? "This is an existing member"
                    : "This member doesn't exist"}
                </div>
              </div>
            )}

            {/* Create account button if email doesn't exist */}
            {!isSubscriptionAdded &&
              emailChecked &&
              !subscription.emailHasAccount && (
                <Button
                  color="secondary"
                  onClick={() => {
                    setShowAddSubscriberModal(true);
                  }}
                  className={styles.createAccountBtn}
                >
                  <Plus />
                  <span>Create account</span>
                </Button>
              )}

            {/* Select MDN for existing email account */}
            {!isSubscriptionAdded &&
              emailChecked &&
              subscription.emailHasAccount && (
                <SelectExistingMDNField
                  selectedSubscription={subscription}
                  existingSubscriptions={emailSubscriptions}
                  usedMDNS={usedMDNS}
                  setExistingSubscription={(subscription) => {
                    updaters.setExistingSubscription(subscription);
                  }}
                  onCreateNewMDN={() => {
                    setShowActivateSubscriberModal(true);
                  }}
                />
              )}

            {/* Existing MDN Subscription Display */}
            {subscription.existingSubscription && (
              <ExistingMDNSubscriptionDisplay
                subscription={subscription.existingSubscription}
              />
            )}

            {/* New subscription display */}
            {subscription.newSubscription && (
              <NewSubscriptionDisplay
                subscription={subscription.newSubscription}
                onEdit={() => setShowActivateSubscriberModal(true)}
              />
            )}
          </div>
        </div>
      </Collapse>

      {/* Validation Errors Display */}
      {validationErrors.length > 0 && (
        <div className={styles.validationErrors}>
          <div className={styles.validationErrorsHeader}>
            <span>Please complete the following:</span>
          </div>
          <ul className={styles.validationErrorsList}>
            {validationErrors.map((error, errorIndex) => (
              <li key={errorIndex}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Modals */}
      <ActivateSubscriberModal
        show={showActivateSubscriberModal}
        setShow={setShowActivateSubscriberModal}
        initialData={activateSubscriberModalInitialData}
        complete={(plan, imei, iccid, _savingForLater, _setActivateLoading) => {
          if (!userEmailData?.mid) {
            console.error("User mid not found!");
            return;
          }

          updaters.setNewSubscription({
            imei,
            iccid,
            mid: userEmailData.mid,
            product: plan,
          });
        }}
        proceedWithoutSubmitting
        onlyValidRetailPlans
      />
      <AddSubscriberModal
        show={showAddSubscriberModal}
        setShow={setShowAddSubscriberModal}
        createOnly
        forcedEmail={emailValue}
        afterCreate={() => {
          // after creating the user, update the parent component to recognize the new user
          // this ensures that the component is ready to receive the new subscription that's added, as a new subscription should only be added to an existing user
          handleCheckEmail(emailValue);

          dispatch({
            type: "notify",
            payload: {
              error: false,
              message:
                "Account created successfully. Proceed to create subscription.",
            },
          });
          setShowAddSubscriberModal(false);
          setShowActivateSubscriberModal(true);
        }}
      />
    </div>
  );
};

type SelectExistingMDNFieldProps = {
  selectedSubscription: MemberSubscriptionState | undefined;
  existingSubscriptions: MemberSubscriptionState["existingSubscription"][];
  usedMDNS: string[];
  setExistingSubscription: (
    subscription: MemberSubscriptionState["existingSubscription"],
  ) => void;
  onCreateNewMDN: () => void;
};

// Select an existing MDN (and associated active subscription) to add to family plan
const SelectExistingMDNField = ({
  selectedSubscription,
  existingSubscriptions,
  usedMDNS,
  setExistingSubscription,
  onCreateNewMDN,
}: SelectExistingMDNFieldProps) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const anchorRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const removeUsedMDNs = (
    subscriptions: MemberSubscriptionState["existingSubscription"][],
  ) => {
    return subscriptions.filter(
      (subscription) =>
        !subscription?.subscriberNumber ||
        !usedMDNS.includes(subscription?.subscriberNumber),
    );
  };

  // Filter MDNs based on search query
  const filterMDNsBySearch = (
    subscriptions: MemberSubscriptionState["existingSubscription"][],
    query: string,
  ) => {
    if (!query.trim()) {
      return subscriptions;
    }

    return subscriptions.filter((subscription) =>
      subscription?.subscriberNumber
        ?.toLowerCase()
        .includes(query.toLowerCase())
    );
  };

  // Get filtered MDN list (remove used MDNs and apply search filter)
  const getFilteredMDNs = () => {
    const availableMDNs = removeUsedMDNs(existingSubscriptions);
    return filterMDNsBySearch(availableMDNs, searchQuery);
  };

  // Clear search when dropdown closes
  const handleDropdownClose = () => {
    setDropdownOpen(false);
    setSearchQuery("");
  };

  return (
    <div className={styles.selectExistingMDN}>
      <div
        className={`${styles.selectExistingMDNInput} ${dropdownOpen && styles.open}`}
        ref={anchorRef}
        onClick={() => setDropdownOpen(!dropdownOpen)}
        role="button"
      >
        <span className={styles.selectExistingMDNInputLabel}>
          {selectedSubscription?.existingSubscription?.subscriberNumber ||
            "Select an existing MDN"}
        </span>
        <CaretDown />
      </div>
      <ControlledMenu
        state={dropdownOpen ? "open" : "closed"}
        anchorRef={anchorRef}
        onClose={handleDropdownClose}
        position="auto"
        align="center"
        direction="bottom"
        overflow="auto"
        className={"select-mdn-dropdown"}
      >
        <button
          className={styles.createNewMDNBtn}
          onClick={() => {
            onCreateNewMDN();
            handleDropdownClose();
          }}
        >
          <Plus />
          <span>Create new MDN</span>
        </button>

        {/* Search Input */}
        {existingSubscriptions.length > 5 && (
          <div className={styles.mdnSearchContainer}>
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search MDNs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={styles.mdnSearchInput}
            // onClick={(e) => e.stopPropagation()} // Prevent dropdown from closing
            // onFocus={(e) => e.stopPropagation()} // Prevent dropdown from closing
            />
          </div>)}

        {/* Filtered MDN List */}
        {getFilteredMDNs().map((subscription, index) => {
          const handleClick = () => {
            setExistingSubscription(subscription);
            handleDropdownClose();
          };
          return (
            <button
              className={styles.MDNMenuItem}
              onClick={handleClick}
              key={index}
            >
              <Radio
                onClick={handleClick}
                checked={
                  selectedSubscription?.existingSubscription
                    ?.subscriberNumber === subscription?.subscriberNumber
                }
              />
              <span>{subscription?.subscriberNumber}</span>
            </button>
          );
        })}

        {/* No results message */}
        {searchQuery.trim() && getFilteredMDNs().length === 0 && (
          <div className={styles.noMDNResults}>
            No MDNs found matching "{searchQuery}"
          </div>
        )}
      </ControlledMenu>
    </div>
  );
};

type ExistingMDNSubscriptionDisplayProps = {
  subscription: MemberSubscriptionState["existingSubscription"];
};

const ExistingMDNSubscriptionDisplay = ({
  subscription,
}: ExistingMDNSubscriptionDisplayProps) => {
  return (
    <div className={styles.existingSub}>
      <div className={styles.row}>
        <span>Plan: </span>
        <span>{subscription?.offerName}</span>
      </div>
    </div>
  );
};

const NewSubscriptionDisplay = ({
  subscription,
  onEdit,
}: {
  subscription: MemberSubscriptionState["newSubscription"];
  onEdit: () => void;
}) => {
  const EditBtnIcon = useCallback(() => {
    return (
      <IconButton
        onClick={onEdit}
        sx={{
          color: "black",
        }}
      >
        <PencilSimple />
      </IconButton>
    );
  }, []);

  return (
    <div className={styles.newSubFields}>
      <Input
        label="IMEI"
        value={subscription?.imei}
        readonly
        rightComponent={<EditBtnIcon />}
      />
      <Input
        label="Plan"
        value={subscription?.product?.offerName}
        readonly
        rightComponent={<EditBtnIcon />}
      />
      <Input
        label="ICCID"
        value={subscription?.iccid}
        readonly
        rightComponent={<EditBtnIcon />}
      />
    </div>
  );
};

const removeSpaces = (str: string) => {
  return str.replace(/\s/g, "");
};

// type UserEmailData = {
//   mid: number;
//   subscriptions: MemberSubscriptionState["existingSubscription"][];
// };

const Back = ({ onClick }: { onClick: () => void }) => {
  return (
    <div className={styles.backStep} onClick={onClick} role="button">
      <ArrowBack />
      <span>Back</span>
    </div>
  );
};

type ReviewStepProps = {
  subscriptions: MemberSubscriptionState[];
  familyPlanId?: number;
  setStep: (step: Steps) => void;
};

const ReviewStep = ({
  subscriptions,
  familyPlanId,
  setStep,
}: ReviewStepProps) => {
  const dispatch = useDispatch();

  const subscriptionsByEmailForDisplay = useMemo(() => {
    return subscriptions.reduce(
      (acc, subscription) => {
        if (!subscription.email) {
          return acc;
        }

        const subsForEmail = acc[subscription.email] || [];

        acc[subscription.email] = [...subsForEmail, subscription];
        return acc;
      },
      {} as Record<string, MemberSubscriptionState[]>,
    );
  }, [subscriptions]);

  // fetch total, subtotal and discount
  const [priceBreakdownFromApi, setPriceBreakdownFromApi] = useState<{
    discountPercentage: number;
    subtotal: number;
    discountAmount: number;
  }>();
  const [priceBreakdownLoading, setPriceBreakdownLoading] = useState(true);
  const [priceBreakdownError, setPriceBreakdownError] = useState("");

  const fetchPriceBreakdown = () => {
    setPriceBreakdownLoading(true);
    setPriceBreakdownError("");
    ApiPostAuth(
      "/accounts/familyplan/create?simulate=true",
      transformSubscriptionsForAPI(subscriptions, familyPlanId),
    )
      .then((response) => {
        setPriceBreakdownFromApi(response.data);
        setPriceBreakdownLoading(false);
      })
      .catch((error) => {
        setPriceBreakdownLoading(false);
        setPriceBreakdownError(error.response.data.message);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
        console.error(error);
      });
  };

  useEffect(() => {
    fetchPriceBreakdown();
  }, [subscriptions]);

  if (priceBreakdownLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Spinner />
      </div>
    );
  }

  if (priceBreakdownError) {
    return (
      <div className={styles.reviewStepError}>
        <img src="/error_robot.svg" />
        <p className={styles.errorTitle}>Something went wrong!</p>
        <p style={{ marginBottom: 12 }}>
          Please check the details you have entered and try again.
        </p>
        <p className={styles.errorDescription}>{priceBreakdownError}</p>
        <Button
          onClick={() => {
            setStep("add-susbscriptions");
          }}
        >
          Go back
        </Button>
      </div>
    );
  }

  return (
    <div className={styles.reviewStep}>
      <div className={styles.subscriptions}>
        {Object.entries(subscriptionsByEmailForDisplay).map(
          ([email, subscriptions], memberIndex) => {
            const isAdmin = subscriptions.some(
              (subscription) => subscription.isAdmin,
            );
            const total = subscriptions.reduce((acc, subscription) => {
              const price =
                subscription.newSubscription?.product?.retailPrice ||
                subscription.existingSubscription?.retailPrice ||
                "";
              const priceNum = parseFloat(price);

              return acc + priceNum;
            }, 0);

            return (
              <div className={styles.subscription}>
                <div className={styles.subscriptionHeader}>
                  <span className={styles.title}>Member {memberIndex + 1}</span>
                  {isAdmin && (
                    <div className={styles.isAdmin}>
                      <Tick />
                      <span>Admin</span>
                    </div>
                  )}
                </div>

                <div>{email}</div>

                <div className={styles.subscriptionList}>
                  {subscriptions.map((subscription, subIndex) => {
                    const mdn =
                      subscription.existingSubscription?.subscriberNumber ||
                      "Assigned upon Activation";
                    const planName =
                      subscription.existingSubscription?.offerName ||
                      subscription.newSubscription?.product?.offerName ||
                      "N/A";
                    const price =
                      subscription.existingSubscription?.retailPrice ||
                      subscription.newSubscription?.product?.retailPrice ||
                      "N/A";

                    return (
                      <div className={styles.subscriptionListItem}>
                        <div className={styles.mdn}>
                          <span>MDN {subIndex + 1}:</span>
                          <span>{mdn}</span>
                        </div>
                        <div className={styles.plan}>
                          <span>{planName}</span>
                          <span>{price !== "N/A" ? `$${price}` : "N/A"}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>

                <div className={styles.total}>
                  <span>Total Member {memberIndex + 1}</span>
                  <span>
                    {Number.isNaN(total) ? "N/A" : `$${total.toFixed(2)}`}
                  </span>
                </div>
              </div>
            );
          },
        )}
      </div>

      <div className={styles.summary}>
        <div className={styles.item}>
          <span>Subtotal</span>
          <span>${priceBreakdownFromApi?.subtotal.toFixed(2)}</span>
        </div>
        <div className={styles.item}>
          <span>Family Discount</span>
          <span className={styles.discount}>
            -${priceBreakdownFromApi?.discountAmount.toFixed(2)}
          </span>
        </div>
        <div className={clsx(styles.item, styles.total)}>
          <span>Total</span>
          <span>
            $
            {(
              (priceBreakdownFromApi?.subtotal || 0) -
              (priceBreakdownFromApi?.discountAmount || 0)
            ).toFixed(2)}
          </span>
        </div>
      </div>
    </div>
  );
};

// Transform subscriptions array to format compatible with backend API
const transformSubscriptionsForAPI = (
  subscriptions: MemberSubscriptionState[],
  familyPlanId?: number,
) => {
  return {
    familyPlanId,
    memberSubscriptions: subscriptions.map((subscription) => {
      if (subscription.type === "existing") {
        // Handle existing subscription
        return {
          subscriptionId:
            subscription.existingSubscription?.subscriptionId || null,
          isAdmin: subscription.isAdmin || false,
        };
      } else if (subscription.type === "new") {
        // Handle new subscription
        return {
          mid: subscription.newSubscription?.mid || null,
          isAdmin: subscription.isAdmin || false,
          imei: subscription.newSubscription?.imei || "",
          iccid: subscription.newSubscription?.iccid || "",
          product: subscription.newSubscription?.product || null,
        };
      } else {
        // Handle undefined type (fallback)
        return {
          subscriptionId: null,
          mid: null,
          isAdmin: subscription.isAdmin || false,
          imei: "",
          iccid: "",
          product: {
            deviceType: "",
            offerId: "",
            offerName: "",
            productFamily: "",
            size: "",
            soc: "",
            serviceType: "",
            retailName: "",
            retailPrice: "",
          },
        };
      }
    }),
  };
};

type CancelConfirmModalProps = {
  show: boolean;
  onClose: () => void;
  onConfirm: () => void;
};

const CancelConfirmModal = ({
  show,
  onClose,
  onConfirm,
}: CancelConfirmModalProps) => {
  return (
    <Modal
      show={show}
      close={onClose}
      proceed={onConfirm}
      image="/bulk_edit_confirm_graphic.svg"
      saveButton="Yes, cancel"
      cancelButton="No"
    >
      <div className={styles.cancelConfirmContent}>
        <h3 className={styles.text}>
          Are you sure you want to cancel? Any changes you've made will be lost.
        </h3>
      </div>
    </Modal>
  );
};
