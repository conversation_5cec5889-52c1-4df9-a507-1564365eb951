import { Pencil } from "../svgs";
import styles from "./dashboard-rate-plan.module.scss";

const DashboardRatePlan = ({ name, description, mobileShareGroup }: any) => {
  return (
    <div className={styles.main}>
      <div className={styles.content}>
        <div className={styles.name}>{name}</div>
        <div className={styles.description}>{description}</div>
        <div className={styles.shareGroup}>
          <div className={styles.indicator}>Mobile Share Group:</div>
          <div className={styles.shareNumber}>{mobileShareGroup}</div>
        </div>
      </div>
      <div className={styles.editButton}>
        <Pencil />
      </div>
    </div>
  );
};

export default DashboardRatePlan;
