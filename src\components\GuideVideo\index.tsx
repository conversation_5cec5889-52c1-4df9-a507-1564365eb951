import { PlayButton, X } from "../svgs";
import styles from "./guide-video.module.scss";
import { useState } from "react";
import { Fade } from "@mui/material";

const GuideVideo = ({ item }: any) => {
  const [showModal, setShowModal] = useState(false);

  return (
    <div>
      <div
        className={styles.videoCard}
        onClick={() => {
          setShowModal(true);
        }}
      >
        <div className={styles.thumbnail}>
          <video>
            <source src={item.linkToVideo} type="video/mp4" />
          </video>
          <div className={styles.playButton}>
            <PlayButton />
          </div>
        </div>
        <div className={`${styles.title} video-guide`}>{item.title}</div>
      </div>

      <Fade in={showModal} unmountOnExit>
        <div
          className={styles.modalContainer}
          onClick={() => {
            setShowModal(false);
          }}
        >
          <div
            className={styles.modal}
            onClick={(e: any) => {
              e.stopPropagation();
            }}
          >
            <div
              onClick={() => {
                setShowModal(false);
              }}
              className={styles.close}
            >
              <X />
            </div>
            <video
              autoPlay
              width="100%"
              height="auto"
              controls
              style={{ borderRadius: "15px", border: "none" }}
            >
              <source src={item.linkToVideo} type="video/mp4" />
            </video>
          </div>
        </div>
      </Fade>
    </div>
  );
};

export default GuideVideo;
