@use "../../styles/theme.scss" as *;

.menuButton {
  height: 56px;
  width: 100%;
  border-radius: 8px;
  border: 1px solid $grey;
  padding-left: 16px;
  padding-right: 12px;
  transition: border 0.1s ease;
  font-size: 16px;
  color: $black;
  background: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: $placeholder;
  cursor: pointer;
  transition: background 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &.selected {
    color: $black;
  }
  &:hover {
    border: 1px solid $black;
  }
  &.error {
    border: 2px solid $error !important;
  }
  &.disabled {
    border: 1px solid $disabled;
    color: $disabled-text;
    background: none;
    pointer-events: none;
    &.readonly {
      color: $black;
      border: 1px solid #cacaca;
    }
  }
  svg {
    transition: all 0.2s ease;
  }
  &.iconOpen {
    border: 2px solid $orange !important;
    svg {
      transform: rotate(180deg);
    }
  }
}

.menuItem {
  height: 48px;
  transition: all 0.1s ease;
  color: $black;
  padding: 0 10px;
  line-height: 21px;
  font-size: 14px;
  font-weight: 300;
  background: $off-white;
  margin-bottom: 5px;
  &:hover {
    background: $light-orange;
  }
  &.selected {
    pointer-events: none;
    background: $faded-orange;
    font-weight: 500;
  }
}

.errorText {
  margin: 0px 0px 0px 5px;
  font-size: 12px;
  color: $error;
}
