import { useState } from "react";
import Modal from "../Modal";
import styles from "./add-top-up.module.scss";
import { ApiPostAuth } from "../../pages/api/api";
import Topup from "../Topup";
import { Info } from "../svgs";
import { useDispatch } from "react-redux";

const AddTopupModal = ({ show, setShow, topups, plan, repopulate }: any) => {
  const [activePlan, setActivePlan] = useState("");

  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();

  const handleAddTopup = () => {
    if (activePlan !== "") {
      setLoading(true);
      ApiPostAuth("/accounts/applytopup", {
        subscriber: plan.mdn,
        topUpName: activePlan,
      })
        .then((response) => {
          setLoading(false);
          setShow(false);
          setActivePlan("");
          repopulate();
          dispatch({
            type: "notify",
            payload: {
              error: false,
              message: response.data.message,
            },
          });
        })
        .catch((error) => {
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: error.response.data.message,
            },
          });
        });
    }
  };

  return (
    <Modal
      scroll
      saveButton="Apply Top up"
      cancelButton="Cancel"
      show={show}
      proceed={handleAddTopup}
      image="/search_graphic.svg"
      close={() => {
        setShow(false);
      }}
      fullSize
      title={<div style={{ width: "100%", textAlign: "start" }}>Top ups</div>}
      loading={loading}
    >
      <div className={styles.plansMain}>
        <div className={styles.plans}>
          <div className={styles.info}>
            <Info />
            Once successfully applied, the top up will reflect as a data balance
            increase in your subscription
          </div>
          {topups.map((plan: any) => (
            <Topup
              plan={plan}
              activePlan={activePlan}
              setActivePlan={setActivePlan}
            />
          ))}
        </div>
      </div>
    </Modal>
  );
};

export default AddTopupModal;
