{"name": "airespring", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@faker-js/faker": "^8.0.2", "@mui/material": "^5.12.1", "@szhsin/react-menu": "^3.5.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/jquery": "^3.5.16", "@types/node": "^16.18.23", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@types/uuid": "^9.0.1", "@uidotdev/usehooks": "^2.4.1", "@vitejs/plugin-react": "^4.0.4", "axios": "^1.4.0", "chart.js": "^4.4.7", "chartjs-plugin-datalabels": "^2.2.0", "clsx": "^2.1.1", "framer-motion": "^10.12.4", "highlight.js": "^11.10.0", "indicative": "^7.4.4", "jquery": "^3.6.4", "jwt-decode": "^3.1.2", "moment": "^2.29.4", "qs": "^6.14.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-router-dom": "^6.10.0", "react-select": "^5.7.3", "react-transition-group": "^4.4.5", "redux": "^4.2.1", "sanitize-html": "^2.10.0", "sass": "^1.69.2", "typescript": "^4.9.5", "underscore": "^1.13.6", "uuid": "^9.0.0", "vite": "4.4.9", "vite-plugin-svgr": "3.2.0", "vite-tsconfig-paths": "4.2.0", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/qs": "^6.9.18", "@types/sanitize-html": "^2.9.0", "@types/underscore": "^1.11.15", "husky": "^9.0.11", "lint-staged": "^15.2.2", "prettier": "^3.2.5"}, "lint-staged": {"*.{js,css,md,ts,tsx,scss,html}": "prettier --write"}}