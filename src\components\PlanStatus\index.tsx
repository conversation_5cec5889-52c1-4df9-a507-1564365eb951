import styles from "./plan-status.module.scss";
import { getStatusesForPlan } from "../utils/planUtils";
import type { PlanStatus } from "../utils/planUtils";
import { Info } from "../svgs";
import formatDate from "../utils/formatDate";
import Tooltip from "../Tooltip";

const PlanStatus = ({ plan }: { plan: any }) => {
  const statuses = getStatusesForPlan(plan);
  const statusesFields = statuses.map((status) =>
    getStatusFields(status, plan),
  );

  const TooltipWrapper = ({ children, tooltipText }: any) => {
    if (!tooltipText) return children;

    return (
      <Tooltip
        text={`Fail Reason: ${tooltipText}`}
        show
        style={{ display: "inline-block" }}
      >
        {children}
      </Tooltip>
    );
  };

  return (
    <div className={styles.container}>
      {statusesFields.map((statusFields) => (
        <TooltipWrapper
          tooltipText={
            "tooltipText" in statusFields ? statusFields.tooltipText : undefined
          }
        >
          <div
            className={styles.label}
            style={{
              backgroundColor: getStatusColors(statusFields.key).bgColor,
            }}
            key={statusFields.title}
          >
            <div className={styles.titleWrapper}>
              <div
                className={styles.title}
                style={{
                  color: getStatusColors(statusFields.key).titleColor,
                }}
              >
                {statusFields.title}
              </div>
              {"tooltipText" in statusFields && (
                <div className={styles.tooltip}>
                  <Info />
                </div>
              )}
            </div>
            {"subtitle" in statusFields && (
              <div
                className={styles.subtitle}
                style={{
                  color:
                    (getStatusColors(statusFields.key) as any).subtitleColor ||
                    getStatusColors(statusFields.key).titleColor,
                }}
              >
                {statusFields.subtitle}
              </div>
            )}
          </div>
        </TooltipWrapper>
      ))}
    </div>
  );
};

export default PlanStatus;

function getStatusFields(status: PlanStatus, plan: any) {
  const statusFieldsMap = {
    active: {
      key: "active" as const,
      title: "Active",
    },
    "ready-to-activate-temp-subscription": {
      key: "ready-to-activate-temp-subscription" as const,
      title: "Ready to Activate",
    },
    "ready-to-activate-temp-portin": {
      key: "ready-to-activate-temp-portin" as const,
      title: "Ready to Activate",
    },
    rejected: {
      key: "rejected" as const,
      title: "Activation Rejected",
      // todo: get from? the plan fields
      tooltipText: plan?.errorMessage || "N/A",
    },
    suspended: {
      key: "suspended" as const,
      title: "Suspended",
      subtitle: formatDate(plan.suspendedDate),
    },
    "pending-activation": {
      key: "pending-activation" as const,
      title: "Pending Activation",
    },
    "pending-ban-change": {
      key: "pending-ban-change" as const,
      title: "Pending BAN Change",
    },
    "iccid-required": {
      key: "iccid-required" as const,
      title: "ICCID Allocation Required",
    },
    "cancelled-resume-available": {
      key: "cancelled-resume-available" as const,
      title: "Cancelled",
      subtitle: `${plan.remainingDaysToResume} days to resume`,
    },
    "cancelled-resume-not-available": {
      key: "cancelled-resume-not-available" as const,
      title: "Cancelled",
      subtitle: plan.cancelledDate
        ? formatDate(plan.cancelledDate, false, true)
        : `0 days to resume`,
    },
    "missing-porting-details": {
      key: "missing-porting-details" as const,
      title: "Missing Porting Details",
    },
    "port-out": {
      key: "port-out" as const,
      title: "Port Out",
    },
    "port-in-in-progress": {
      key: "port-in-in-progress" as const,
      title: "Port-in In Progress",
    },
    "port-in-completed": {
      key: "port-in-completed" as const,
      title: "Port-in Completed",
    },
    "port-in-failed": {
      key: "port-in-failed" as const,
      title: "Port-in Failed",
      subtitle: plan?.portInRequest?.failureReason || plan?.failureReason,
    },
    "port-in-confirmed": {
      key: "port-in-confirmed" as const,
      title: "Port-in Confirmed",
    },
    "port-in-cancelled": {
      key: "port-in-cancelled" as const,
      title: "Port-in Cancelled",
    },
    "port-in-incomplete": {
      key: "port-in-incomplete" as const,
      title: "Port-in Incomplete",
    },
  };

  return statusFieldsMap[status];
}

function getStatusColors(status: PlanStatus) {
  const statusColorsMap = {
    active: {
      key: "active" as const,
      bgColor: "#C3FECD",
      titleColor: "#024B12",
    },
    "ready-to-activate-temp-subscription": {
      key: "ready-to-activate-temp-subscription" as const,
      bgColor: "#C3FECD",
      titleColor: "#024B12",
    },
    "ready-to-activate-temp-portin": {
      key: "ready-to-activate-temp-portin" as const,
      bgColor: "#C3FECD",
      titleColor: "#024B12",
    },
    rejected: {
      key: "rejected" as const,
      bgColor: "#E8E8E8",
      titleColor: "#000000",
    },
    suspended: {
      key: "suspended" as const,
      bgColor: "#FEE0C3",
      titleColor: "#693502",
      subtitleColor: "#000014",
    },
    "pending-activation": {
      key: "pending-activation" as const,
      bgColor: "#F8EC7E",
      titleColor: "#221F02",
    },
    "pending-ban-change": {
      key: "pending-ban-change" as const,
      bgColor: "#F8EC7E",
      titleColor: "#221F02",
    },
    "iccid-required": {
      key: "iccid-required" as const,
      bgColor: "#FCD4D4",
      titleColor: "#7C2C08",
    },
    "cancelled-resume-available": {
      key: "cancelled-resume-available" as const,
      bgColor: "#FCDFE4",
      titleColor: "#850E24",
      subtitleColor: "#000014",
    },
    "cancelled-resume-not-available": {
      key: "cancelled-resume-not-available" as const,
      bgColor: "#1A1A1A",
      titleColor: "#FFFFFF",
      subtitleColor: "#FFFFFF",
    },
    "missing-porting-details": {
      key: "missing-porting-details" as const,
      bgColor: "#FCD4D4",
      titleColor: "#7C2C08",
    },
    "port-out": {
      key: "port-out" as const,
      bgColor: "#FEE0C3",
      titleColor: "#693502",
    },
    "port-in-in-progress": {
      key: "port-in-in-progress" as const,
      bgColor: "#FFF0CC",
      titleColor: "#664700",
    },
    "port-in-completed": {
      key: "port-in-completed" as const,
      bgColor: "#DCF4E0",
      titleColor: "#1A5625",
    },
    "port-in-failed": {
      key: "port-in-failed" as const,
      bgColor: "#FCD9D9",
      titleColor: "#710909",
    },
    "port-in-confirmed": {
      key: "port-in-confirmed" as const,
      bgColor: "#E8F0FC",
      titleColor: "#0C2C64",
    },
    "port-in-cancelled": {
      key: "port-in-cancelled" as const,
      bgColor: "#FCD9D9",
      titleColor: "#710909",
    },
    "port-in-incomplete": {
      key: "port-in-incomplete" as const,
      bgColor: "#DFE2E7",
      titleColor: "#282C34",
    },
  };

  return statusColorsMap[status];
}
