import { CircularProgress } from "@mui/material";
import styles from "./button.module.scss";

const Button = ({
  style,
  onClick,
  loading,
  disabled,
  children,
  color = "primary",
  id = "",
  className = "",
}: any) => {
  const root = getComputedStyle(document.getElementById("root")!);
  return (
    <button
      id={id}
      disabled={loading || disabled}
      className={`${className} ${styles.button}
        ${styles[color]}`}
      style={style}
      onClick={onClick}
    >
      {loading && (
        <CircularProgress
          style={{
            width: 20,
            height: 20,
            color:
              color === "primary" || color === "search"
                ? "#fff"
                : color === "secondary"
                  ? "#000"
                  : root.getPropertyValue("--orange"),
            gridArea: "1 / 1 / 2 / 2",
            margin: "0 auto",
          }}
        />
      )}
      <span
        style={{
          visibility: loading ? "hidden" : "visible",
        }}
        className={styles.content}
      >
        {children}
      </span>
    </button>
  );
};

export default But<PERSON>;
