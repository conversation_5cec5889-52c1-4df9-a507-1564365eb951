@use "../../styles/theme.scss" as *;

.main {
  margin: auto;
  width: 100%;
  max-width: 430px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 16px;
  justify-content: center;
  align-content: center;
  :nth-child(3),
  :nth-child(4),
  :nth-child(5) {
    grid-column: 1 / 3;
  }
}

.twoFactor {
  grid-column: 1 / 3;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  border-radius: 8px;
  border: 1px solid $grey;
  padding: 0 9px 0 16px;
  margin-top: 12px;
  margin-bottom: 12px;
}

.toggleContainer {
  display: flex;
  align-items: center;
  .onOff {
    margin-right: 8px;
  }
}
