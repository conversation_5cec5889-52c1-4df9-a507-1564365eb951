import styles from "./menu.module.scss";
import {
  ControlledMenu,
  MenuItem,
  useClick,
  useMenuState,
} from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown, Pencil, Swap, X } from "../svgs";
import { useRef } from "react";
import { Link } from "react-router-dom";
import { createPortal } from "react-dom";
import { useSelector } from "react-redux";

const ChannelActionMenu = ({
  setCurrentChannelAction,
  subscriber,
  setActiveUser,
}: any) => {
  const ref = useRef(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });
  const anchorProps = useClick(menuProps.state, toggleMenu);

  const { userInfo } = useSelector((state: any) => state);

  return (
    <div className={`${styles.box} select`}>
      <div
        ref={ref}
        {...anchorProps}
        className={styles.menuButton}
        onClick={(e: any) => {
          e.stopPropagation();
          toggleMenu(true);
        }}
      >
        <Pencil />
      </div>
      {createPortal(
        <div className="select">
          <ControlledMenu
            {...menuProps}
            anchorRef={ref}
            onClose={() => toggleMenu(false)}
            align="start"
            position="auto"
            viewScroll="auto"
            onClick={(e: any) => {
              e.stopPropagation();
            }}
          >
            <MenuItem
              onClick={(e: any) => {
                setActiveUser(subscriber);
                setCurrentChannelAction("edit-subscriber");
              }}
              className={styles.menuItem}
            >
              <Pencil />
              Edit Subscriber
            </MenuItem>
            <MenuItem
              onClick={(e: any) => {
                setActiveUser(subscriber);
                setCurrentChannelAction("remove-from-channel");
              }}
              className={styles.menuItem}
            >
              <X />
              Remove from Channel
            </MenuItem>
            {userInfo.roleName !== "Agent" && (
              <MenuItem
                onClick={(e: any) => {
                  setActiveUser(subscriber);
                  setCurrentChannelAction("change-channel");
                }}
                className={styles.menuItem}
              >
                <Swap />
                Change Channel
              </MenuItem>
            )}
          </ControlledMenu>
        </div>,
        document.getElementById("root")!,
      )}
    </div>
  );
};

export default ChannelActionMenu;
