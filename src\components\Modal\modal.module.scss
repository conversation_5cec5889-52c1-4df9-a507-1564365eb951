@use "../../styles/theme.scss" as *;

.container {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 0 30px;
  background: rgba(0, 0, 0, 0.6);
  &.clear {
    background: none;
  }
}

.modal {
  width: 100%;
  max-width: 1350px;
  height: 90vh;
  max-height: 662px;
  border-radius: 24px;
  background: #fff;
  display: flex;
  justify-content: end;
  grid-template-columns: 1fr 705px;
  overflow: hidden;
}

.imgContainer {
  height: 90vh;
  max-height: 662px;
  max-width: calc(90vh * 0.92);
  width: 100%;
}
.imgUniqueContainer {
  height: 100%;
  width: auto;
}

.illustration {
  height: 100%;
  width: auto;
  box-shadow: 20px 0px 40px rgba(0, 95, 210, 0.1);
}
.imgUnique {
  height: 100%;
  width: 318px;
  object-fit: cover;
  box-shadow: 20px 0px 40px rgba(0, 95, 210, 0.1);
}

.main {
  width: 100%;
  height: 100%;
  min-width: 686px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  padding: 32px 41px 32px 32px;
  position: relative;
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.close {
  margin-left: auto;
}

.close.fullSize {
  position: absolute;
  top: 32px;
  right: 32px;
  svg {
    width: 20px;
    height: 20px;
    margin: 0;
  }
}

.content {
  align-self: center;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  //justify-content: center;
  margin: 24px 0;
  padding: 20px 0;
  &.fullSize {
    margin-top: 0;
  }
  h3 {
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    text-align: center;
  }
}

.buttons {
  display: flex;
  margin-top: auto;
  width: 100%;
  justify-content: flex-end;
}

.title {
  margin: 0 0 24px 0;
  text-align: center;
  width: 100%;
  font-weight: 700;
  font-size: 24px;
  line-height: 36px;
}

.subtitle {
  //margin: 0 0 auto 0;
  text-align: center;
  width: 100%;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  color: #45474f;
}
