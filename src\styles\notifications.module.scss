@use "./theme.scss" as *;

.main {
  width: 100%;
  background: #f1f1f1;
  padding: 50px 40px;
}
.titleBar {
  h3 {
    margin-top: -9px;
    margin-bottom: 18px;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
  }
}

.mainTile {
  display: flex;
  padding: 24px;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 24px;
  background: #fff;
  min-height: calc(100vh - 228px);
  .container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1 0 0;
    align-self: stretch;
  }
}

.none {
  display: grid;
  align-items: center;
  justify-items: center;
  width: 100%;
  height: 100%;
  margin: 50px;
  .noneImage,
  .noneText {
    grid-area: 1 / 1 / 2 / 2;
  }
  .noneText {
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
    color: $black;
  }
}

.loadingError {
  background: #f2f4f7;
  width: 100%;
  padding: 12px;
  border-radius: 24px;
  display: flex;
  align-items: center;
}
