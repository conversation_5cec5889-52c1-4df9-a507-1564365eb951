import Checkbox from "../Checkbox";
import styles from "./feature.module.scss";

const Bolton = ({ feature, activeFeature, setActiveFeature }: any) => {
  return (
    <div className={styles.main}>
      <Checkbox
        checked={activeFeature.some((item: any) => item.code === feature.code)}
        onClick={() => {
          if (activeFeature.some((item: any) => item.code === feature.code)) {
            console.log;
            let removed = activeFeature.filter(
              (i: any) => i.code !== feature.code
            );
            setActiveFeature(removed);
          } else {
            setActiveFeature([...activeFeature, feature]);
          }
        }}
      />
      <div>
        <div className={styles.name}>{feature.name}</div>
        <div className={styles.description}>{feature.description}</div>
      </div>
    </div>
  );
};

export default Bolton;
