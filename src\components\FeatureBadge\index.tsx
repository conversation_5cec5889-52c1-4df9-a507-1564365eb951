import styles from "./feature-badge.module.scss";

const FeatureBadge = ({
  color,
  hoverColor,
  white,
  greyscale,
  children,
}: any) => {
  return (
    <div
      className={`${styles.main} ${greyscale && styles.greyscale}`}
      style={{
        background: color,
        color: white ? "#fff" : "#000",
        ...(hoverColor && { cursor: "pointer" }),
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.background = hoverColor || color;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.background = color;
      }}
    >
      {children}
    </div>
  );
};

export default FeatureBadge;
