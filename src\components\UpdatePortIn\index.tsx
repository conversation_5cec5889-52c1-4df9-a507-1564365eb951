import { SwitchTransition, CSSTransition } from "react-transition-group";
import { Input } from "../Input";
import {
  clearInput,
  createStateObject,
  getMessages,
  getRules,
  labels,
  placeholders,
  handleInputChange,
  displayErrors,
} from "../utils/InputHandlers";
import styles from "./port-in-form.module.scss";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import SelectDropdown from "../SelectDropdown";
import { states } from "../utils/usStates";
import Modal from "../Modal";
import { ArrowBack, CheckCircle, XCircle } from "../svgs";
import { validateAll } from "indicative/validator";
import { ApiPostAuth, ApiPutWithId } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import PortInErrorModal from "../PortInErrorModal";
import PortInSuccessModal from "../PortInSuccessModal";
import { Collapse } from "@mui/material";
import RatePlan from "../RatePlan";

const oldNumberFields = [
  "firstName",
  "lastName",
  "billingAccountNumber",
  "billingAccountPassword",
];
const oldNumberRules = getRules(oldNumberFields);
const oldNumberMessages = getMessages(oldNumberFields);

const oldAddressFields = [
  "streetNumber",
  "streetDirection",
  "streetName",
  "city",
  "state",
  "zipCode",
];
const oldAddressRules = getRules(oldAddressFields);
const oldAddressMessages = getMessages(oldAddressFields);

const iccidFields = ["iccid"];
const iccidRules = getRules(iccidFields);
const iccidMessages = getMessages(iccidFields);

const imeiFields = ["imei"];
const imeiRules = getRules(imeiFields);
const imeiMessages = getMessages(imeiFields);

const UpdatePortIn = ({
  show,
  setShow,
  msisdn,
  zipCode,
  temp,
  repopulate,
  handleFinish,
  accountDataProvided,
  portData = null,
  handleInitCancel = null,
}: any) => {
  const dispatch = useDispatch();

  const [oldNumberData, setOldNumberData] = useState(
    createStateObject(oldNumberFields),
  );

  const [oldAddressData, setOldAddressData] = useState(
    createStateObject(oldAddressFields),
  );

  const [iccidData, setIccidData] = useState(createStateObject(iccidFields));
  const [imeiData, setImeiData] = useState(createStateObject(imeiFields));

  const [iccidError, setIccidError] = useState("");

  const [numberIneligible, setNumberIneligible] = useState(false);
  const [numberVerified, setNumberVerified] = useState(false);
  const [ineligibleReason, setIneligibleReason] = useState("");

  const [plans, setPlans] = useState([] as any);

  const [activePlan, setActivePlan] = useState({
    offerId: -1,
    offerName: "",
  } as any);

  const resubmitPortin = () => {
    setLoading(true);
    ApiPostAuth("/accounts/portin/resubmit", {
      msisdn: msisdn,
      billingAccountNumber: oldNumberData.billingAccountNumber,
      billingAccountPassword: oldNumberData.billingAccountPassword,
      iccid: iccidData?.iccid,
      imei: imeiData?.imei,
      product: activePlan,
      addressDetails: {
        streetNumber: oldAddressData.streetNumber,
        streetDirection:
          oldAddressData.streetDirection === ""
            ? oldAddressData.streetDirection
            : oldAddressData.streetDirection.value,
        streetName: oldAddressData.streetName,
        city: oldAddressData.city,
        state: oldAddressData.state.value,
        zipCode: oldAddressData.zipCode,
      },
    })
      .then((response) => {
        repopulate(() => {
          setLoading(false);
          setShowSuccess(true);
        });
      })
      .catch((error) => {
        setLoading(false);
        setShowError(true);
        setErrorMessage(
          error.response.data.attResponse?.errorDescription ||
            error.response.data.message,
        );
      });
  };

  const resubmitTempPortin = () => {
    setLoading(true);

    ApiPostAuth("/accounts/portin/temp/update", {
      msisdn: msisdn,
      billingAccountNumber: oldNumberData.billingAccountNumber,
      billingAccountPassword: oldNumberData.billingAccountPassword,
      iccid: iccidData?.iccid,
      imei: imeiData?.imei,
      product: activePlan,
      addressDetails: {
        streetNumber: oldAddressData.streetNumber,
        streetDirection:
          oldAddressData.streetDirection === ""
            ? oldAddressData.streetDirection
            : oldAddressData.streetDirection.value,
        streetName: oldAddressData.streetName,
        city: oldAddressData.city,
        state: oldAddressData.state.value,
        zipCode: oldAddressData.zipCode,
      },
    })
      .then((response) => {
        updateImei();
      })
      .catch((error) => {
        setLoading(false);
        setShowError(true);
        setErrorMessage(
          error.response.data.attResponse?.errorDescription ||
            error.response.data.message,
        );
      });
  };

  const updateImei = () => {
    ApiPutWithId(
      "/accounts/portin/imei/update",
      mvnoId,
      {
        subscriberNumber: msisdn,
        imei: imeiData.imei,
        product: activePlan,
      },
      temp ? "temp" : "active",
    )
      .then((response) => {
        repopulate(() => {
          setLoading(false);
          setShowSuccess(true);
        });
      })
      .catch((error) => {
        setLoading(false);
        setShowError(true);
        setErrorMessage(
          error.response.data.attResponse?.errorDescription ||
            error.response.data.message,
        );
      });
  };

  useEffect(() => {
    if (portData !== null && show) {
      setOldNumberData({ ...oldNumberData, ...portData.attDetails.oldService });
      setOldAddressData({
        ...portData?.attDetails?.oldService?.addressDetails,
        streetDirection: portData?.attDetails?.oldService?.addressDetails
          ?.streetDirection
          ? {
              label:
                portData?.attDetails?.oldService?.addressDetails
                  ?.streetDirection,
              value:
                portData?.attDetails?.oldService?.addressDetails
                  ?.streetDirection,
            }
          : "",
        state: portData?.attDetails?.oldService?.addressDetails?.state
          ? {
              label: states.find(
                (state: any) =>
                  state.value ===
                  portData?.attDetails?.oldService?.addressDetails?.state,
              )?.label,
              value: portData?.attDetails?.oldService?.addressDetails?.state,
            }
          : "",
        errors: oldAddressData.errors,
      });
      setIccidData({
        ...iccidData,
        iccid: portData.iccid,
      });
      setImeiData({ ...imeiData, imei: portData.imei });

      setActivePlan({
        deviceType: portData.product.deviceType,
        offerId: portData.product.offerId,
        offerName: portData.product.product,
        productFamily: portData.product.productFamily,
        size: portData.product.productSize,
        soc: portData.product.soc,
        serviceType: portData.product.serviceType,
        retailName: portData.product.retailName,
        retailPrice: portData.product.retailPrice,
      });
    }
  }, [show, portData]);

  const [loading, setLoading] = useState(false);

  const [activeSection, setActiveSection] = useState("old-carrier-details");

  const { mvnoId } = useParams();

  const [showError, setShowError] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const handleCancel = () => {
    setShow(false);
    if (handleInitCancel) {
      handleInitCancel();
    }
    setTimeout(() => {
      reset();
    }, 300);
  };

  const handleOldStateChange = (selectedOption: any) => {
    setOldAddressData({
      ...oldAddressData,
      state: selectedOption,
      errors: { ...oldAddressData.errors, state: "" },
    });
  };

  const handleOldDirectionChange = (selectedOption: any) => {
    if (selectedOption.value === oldAddressData.streetDirection.value) {
      setOldAddressData({
        ...oldAddressData,
        streetDirection: "",
        errors: { ...oldAddressData.errors, streetDirection: "" },
      });
    } else {
      setOldAddressData({
        ...oldAddressData,
        streetDirection: selectedOption,
        errors: { ...oldAddressData.errors, streetDirection: "" },
      });
    }
  };

  const reset = () => {
    setActiveSection("old-carrier-details");
    setOldNumberData(createStateObject(oldNumberFields));
    setOldAddressData(createStateObject(oldAddressFields));
    setIccidData(createStateObject(iccidFields));
    setImeiData(createStateObject(imeiFields));
    setIccidError("");
    setShowError(false);
    setShowSuccess(false);
    setNumberIneligible(false);
    setNumberVerified(false);
    setActivePlan({
      offerId: -1,
      offerName: "",
    });
    setIneligibleReason("");
  };

  const checkOldCarrierDetails = () => {
    validateAll(oldNumberData, oldNumberRules, oldNumberMessages)
      .then(() => {
        setActiveSection("old-carrier-address");
      })
      .catch((errors) => {
        displayErrors(errors, setOldNumberData);
      });
  };

  const checkOldCarrierAddress = () => {
    validateAll(oldAddressData, oldAddressRules, oldAddressMessages)
      .then(() => {
        setActiveSection("update-imei");
      })
      .catch((errors) => {
        displayErrors(errors, setOldAddressData);
      });
  };

  const checkImei = () => {
    validateAll(imeiData, imeiRules, imeiMessages)
      .then((response) => {
        setLoading(true);
        setNumberIneligible(false);
        setNumberVerified(false);
        setIneligibleReason("");
        ApiPostAuth("/products/availableproducts", {
          imei: imeiData.imei,
          mvnoId: parseInt(mvnoId!),
        })
          .then((response: any) => {
            setLoading(false);
            if (response.data.length) {
              setPlans(response.data);
              setNumberVerified(true);
            } else {
              setNumberIneligible(true);
            }
          })
          .catch((error) => {
            setLoading(false);
            setNumberIneligible(true);
            setIneligibleReason(error.response.data.message);
          });
      })
      .catch((errors) => {
        displayErrors(errors, setImeiData);
      });
  };

  const selectPlan = () => {
    if (activePlan.offerId !== -1) {
      setActiveSection("update-iccid");
    }
  };

  const checkIccid = () => {
    validateAll(iccidData, iccidRules, iccidMessages)
      .then(() => {
        /*if (iccidData.iccid !== portData.iccid) {
          setLoading(true);
          ApiPostAuth("/accounts/validate/iccid", {
            iccid: iccidData.iccid,
          })
            .then(() => {
              if (temp) {
                resubmitTempPortin();
              } else {
                resubmitPortin();
              }
            })
            .catch((error) => {
              setLoading(false);
              setIccidError(error.response.data.message);
            });
        } else {
          if (temp) {
            resubmitTempPortin();
          } else {
            resubmitPortin();
          }
        }*/
        if (temp) {
          resubmitTempPortin();
        } else {
          resubmitPortin();
        }
      })
      .catch((errors) => {
        displayErrors(errors, setIccidData);
      });
  };

  const goToPlans = () => {
    setActiveSection("select-plan");
  };

  const handleBack = () => {
    if (activeSection === "old-carrier-address") {
      setActiveSection("old-carrier-details");
    } else if (activeSection === "update-imei") {
      setActiveSection("old-carrier-address");
    } else if (activeSection === "select-plan") {
      setActiveSection("update-imei");
    } else if (activeSection === "update-iccid") {
      setActiveSection("select-plan");
    }
  };

  const [tether, setTether] = useState(true);

  return (
    <Modal
      saveButton="Continue"
      cancelButton="Cancel"
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      proceed={
        activeSection === "old-carrier-details"
          ? checkOldCarrierDetails
          : activeSection === "old-carrier-address"
            ? checkOldCarrierAddress
            : activeSection === "update-imei"
              ? numberVerified
                ? goToPlans
                : checkImei
              : activeSection === "select-plan"
                ? selectPlan
                : checkIccid
      }
      close={() => {
        setShow(false);
        setTimeout(() => {
          reset();
        }, 300);
      }}
      loading={loading}
      fullSize
      title={activeSection === "select-plan" && "Update Plan"}
      subtitle={
        activeSection === "select-plan" ? (
          <SelectPlanSubtitle tether={tether} setTether={setTether} />
        ) : null
      }
      onCancel={() => {
        setShow(false);
        setTimeout(() => {
          reset();
        }, 300);
      }}
    >
      <PortInErrorModal
        show={showError}
        setShow={setShowError}
        error={errorMessage}
        msisdn={msisdn}
        zipCode={zipCode}
        handleFinish={() => {
          setTimeout(() => {
            reset();
          }, 300);
        }}
      />
      <PortInSuccessModal
        show={showSuccess}
        setShow={setShowSuccess}
        handleFinish={handleFinish}
        update
        exisitingSubscriber={accountDataProvided}
        handleCancel={handleCancel}
        newMid=""
      />
      <div className={styles.main} style={{ width: "auto" }}>
        {activeSection !== "old-carrier-details" && (
          <button className={styles.backButton} onClick={handleBack}>
            <ArrowBack />
          </button>
        )}
        {activeSection === "old-carrier-details" ? (
          <div className={styles.section} style={{ maxWidth: 291 }}>
            <h3>Update Port In</h3>
            <p className={styles.info}>Enter Old Carrier Details</p>
            <div className={styles.inputsGrid}>
              {oldNumberFields.map((prop: string) => (
                <Input
                  key={"oldnumber-" + prop}
                  label={labels[prop]}
                  placeholder={placeholders[prop]}
                  value={oldNumberData[prop]}
                  onChange={(e: any) =>
                    handleInputChange(
                      prop,
                      e,
                      oldNumberData,
                      setOldNumberData,
                      true,
                    )
                  }
                  error={oldNumberData.errors[prop]}
                  clear={() => {
                    clearInput(prop, setOldNumberData);
                  }}
                  disabled={loading}
                  white
                />
              ))}
            </div>
          </div>
        ) : activeSection === "old-carrier-address" ? (
          <div className={styles.section}>
            <h3 style={{ marginBottom: 20 }}>
              Edit Old Carrier Address Details
            </h3>
            <div className={styles.oldInputsGrid}>
              {oldAddressFields.map((prop: string) => {
                if (prop === "state") {
                  return (
                    <SelectDropdown
                      value={oldAddressData.state}
                      error={oldAddressData.errors.state}
                      onChange={handleOldStateChange}
                      placeholder="State"
                      options={states}
                      dropDownMaxHeight={200}
                      white
                    />
                  );
                } else if (prop === "streetDirection") {
                  return (
                    <div>
                      <div style={{ fontSize: 10, color: "#525252" }}>
                        *Street direction is an optional field
                      </div>
                      <SelectDropdown
                        value={oldAddressData.streetDirection}
                        error={oldAddressData.errors.streetDirection}
                        onChange={handleOldDirectionChange}
                        placeholder="Street Direction"
                        options={[
                          { label: "N", value: "N" },
                          { label: "E", value: "E" },
                          { label: "S", value: "S" },
                          { label: "W", value: "W" },
                          { label: "NE", value: "NE" },
                          { label: "NW", value: "NW" },
                          { label: "SE", value: "SE" },
                          { label: "SW", value: "SW" },
                        ]}
                        dropDownMaxHeight={200}
                        white
                      />
                    </div>
                  );
                } else {
                  return (
                    <Input
                      key={"account-" + prop}
                      label={labels[prop]}
                      placeholder={placeholders[prop]}
                      value={oldAddressData[prop]}
                      onChange={(e: any) =>
                        handleInputChange(
                          prop,
                          e,
                          oldAddressData,
                          setOldAddressData,
                        )
                      }
                      error={oldAddressData.errors[prop]}
                      clear={() => {
                        clearInput(prop, setOldAddressData);
                      }}
                      disabled={loading}
                      white
                    />
                  );
                }
              })}
            </div>
          </div>
        ) : activeSection === "update-imei" ? (
          <div className={styles.imeiMain}>
            <h3>Update IMEI</h3>
            <h4>Enter device IMEI</h4>
            <Input
              label={labels.imei}
              placeholder={placeholders.imei}
              value={imeiData.imei}
              onChange={(e: any) => {
                handleInputChange("imei", e, imeiData, setImeiData);
              }}
              error={imeiData.errors.imei}
              clear={() => {
                clearInput("imei", setImeiData);
              }}
              disabled={loading}
              onKeyDown={checkImei}
            />
            <Collapse style={{ width: "100%" }} in={numberVerified}>
              <div className={styles.eligible}>
                <CheckCircle />
                <div>IMEI is eligible</div>
              </div>
            </Collapse>
            <Collapse style={{ width: "100%" }} in={numberIneligible}>
              <div className={styles.notEligible}>
                <XCircle />
                <div>
                  <div className={styles.topText}>IMEI not eligible</div>
                  <div className={styles.bottomText}>{ineligibleReason}</div>
                </div>
              </div>
            </Collapse>
          </div>
        ) : activeSection === "select-plan" ? (
          <div className={styles.plansMain}>
            <SwitchTransition>
              <CSSTransition
                key={tether ? "tether" : "no-tether"}
                addEndListener={(node, done) =>
                  node.addEventListener("transitionend", done, false)
                }
                classNames="fade"
              >
                {tether ? (
                  <div className={styles.plans}>
                    {plans.filter(
                      (item: any) => item.teaserType === "Custom Overage",
                    ).length > 0 ? (
                      plans
                        .filter(
                          (item: any) => item.teaserType === "Custom Overage",
                        )
                        .map((plan: any) => (
                          <RatePlan
                            plan={plan}
                            activePlan={activePlan}
                            setActivePlan={setActivePlan}
                          />
                        ))
                    ) : (
                      <div className={styles.noPlans}>
                        <span>
                          There aren't any tether plans available for this IMEI
                        </span>
                        <img src="/none_found.svg" />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className={styles.plans}>
                    {plans.filter(
                      (item: any) => item.teaserType !== "Custom Overage",
                    ).length > 0 ? (
                      plans
                        .filter(
                          (item: any) => item.teaserType !== "Custom Overage",
                        )
                        .map((plan: any) => (
                          <RatePlan
                            plan={plan}
                            activePlan={activePlan}
                            setActivePlan={setActivePlan}
                            noTether
                          />
                        ))
                    ) : (
                      <div className={styles.noPlans}>
                        <span>
                          There aren't any no tether plans available for this
                          IMEI
                        </span>
                        <img src="/none_found.svg" />
                      </div>
                    )}
                  </div>
                )}
              </CSSTransition>
            </SwitchTransition>
          </div>
        ) : activeSection === "update-iccid" ? (
          <div className={styles.section}>
            <h3>Update ICCID</h3>
            <p className={styles.info}>Number should be between 18-22 digits</p>
            <div style={{ maxWidth: 350, margin: "0 auto" }}>
              <Input
                label={labels.iccid}
                placeholder={placeholders.iccid}
                value={iccidData.iccid}
                onChange={(e: any) =>
                  handleInputChange("iccid", e, iccidData, setIccidData)
                }
                error={iccidData.errors.iccid}
                clear={() => {
                  clearInput("iccid", setIccidData);
                }}
                disabled={loading}
                white
              />
              <Collapse in={iccidError !== ""}>
                <div className={styles.notEligible}>
                  <XCircle />
                  <div>
                    <div className={styles.topText}>{iccidError}</div>
                  </div>
                </div>
              </Collapse>
            </div>
          </div>
        ) : (
          ""
        )}
      </div>
    </Modal>
  );
};

export default UpdatePortIn;

const root = getComputedStyle(document.getElementById("root")!);

const SelectPlanSubtitle = ({ tether, setTether }: any) => (
  <div className={styles.planType}>
    <div>Select a plan</div>
    <div
      className={styles.planSwitch}
      style={{
        backgroundColor: tether
          ? root.getPropertyValue("--faded-orange")
          : "#E0DCDC",
      }}
      onClick={() => {
        setTether((prev: boolean) => !prev);
      }}
    >
      <div className={styles.label} style={{ color: tether ? "#fff" : "#000" }}>
        Tether Plans
      </div>
      <div
        className={styles.label}
        style={{ color: !tether ? "#fff" : "#000" }}
      >
        No Tether Plans
      </div>
      <div
        className={styles.thumb}
        style={{
          right: tether ? "163px" : "0px",
          backgroundColor: tether
            ? root.getPropertyValue("--orange")
            : "#1a1a1a",
        }}
      />
    </div>
  </div>
);
