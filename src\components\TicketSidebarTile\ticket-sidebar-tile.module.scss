@use "../../styles/theme.scss" as *;

.ticketSummary {
  background: $off-white;
  box-shadow: 0px 0px 30px 0px rgba(8, 7, 87, 0.1);
  border-radius: 24px;
  margin-bottom: 24px;
  .underlineSection {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    padding: 24px 24px 0 24px;
  }
  .mainSection {
    padding: 24px;
  }
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    .left {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }
  .ticketNo {
    font-size: 12px;
    line-height: 18px;
  }
  .subject {
    margin-bottom: 12px;
    font-size: 20px;
    line-height: 30px;
    font-weight: 600;
  }
  .issueLabel {
    font-size: 12px;
    line-height: 18px;
    margin-bottom: 10px;
  }
  .issueContent {
    font-size: 14px;
    line-height: 21px;
    margin-bottom: 12px;
    white-space: pre-line;
  }
}
.date {
  font-size: 12px;
  line-height: 18px;
  color: #797979;
}

.dataBar {
  display: grid;
  grid-template-columns: auto auto auto auto;
  justify-content: start;
  align-items: center;
  grid-column-gap: 12px;
}
