@use "../../styles/theme.scss" as *;

.main {
  background: $off-white;
  box-shadow: 0px 0px 30px 0px rgba(8, 7, 87, 0.1);
  border-radius: 24px;
  padding: 24px 0;
  &.horizontal {
    display: grid;
    grid-template-columns: 475px 1fr;
    padding: 0 24px;
    .title {
      border-bottom: none;
      border-right: 1px solid rgba(0, 0, 0, 0.12);
      margin-bottom: 0px;
      padding-bottom: 0px;
      padding-left: 0px;
    }
    .customerInfo {
      flex-direction: column;
    }
    .customerSummary {
      margin: 8px 0 0 0;
    }
    .contentContainer {
      padding: 24px 0 24px 24px;
      width: 100%;
    }
  }
  /*.title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px 12px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    margin-bottom: 24px;
    h4 {
      font-size: 20px;
      line-height: 30px;
    }
  }*/
  .contentContainer {
    padding: 0 24px;
  }
}

.customerInfo {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  .name {
    font-size: 20px;
    line-height: 30px;
    font-weight: 600;
    margin-bottom: 4px;
  }
  .email {
    font-size: 14px;
    line-height: 21px;
  }
}
.customerSummary {
  display: flex;
  align-items: center;
  gap: 8px;
  color: $orange;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  height: 24px;
  transition: all 0.2s ease;
  white-space: nowrap;
  cursor: pointer;
  &:hover {
    color: $dark-orange;
  }
}
