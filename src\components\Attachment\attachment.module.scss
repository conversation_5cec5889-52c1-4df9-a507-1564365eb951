@use "../../styles/theme.scss" as *;

.main {
  border-radius: 16px;
  background: #f7f6f6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  .button {
    display: none;
    padding: 10px;
    color: $orange;
  }
  &:hover {
    .button {
      display: flex;
      cursor: pointer;
      &:hover {
        color: $dark-orange;
      }
    }
  }
}

.imageContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  border-radius: 16px;
  border: 1px solid #b5b5b5;
  margin-right: 16px;
  svg {
    vertical-align: middle;
  }
}

.filename {
  font-size: 14px;
  font-weight: 600;
  color: $black;
  margin-bottom: 6px;
}

.date,
.size {
  color: #4d4d4d;
  font-size: 12px;
  font-weight: 400;
}

.date {
  margin-bottom: 2px;
}
