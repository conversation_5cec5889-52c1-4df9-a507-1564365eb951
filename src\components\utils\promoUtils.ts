import moment from "moment";

export function formatType(type: string) {
  if (type === "FEE_DISCOUNT") return "Fee Discount";
  else if (type === "ACCOUNT_DISCOUNT") return "Discount";
}

export function formatPromoAmount(promo: any) {
  if (promo.amountType === "PERCENTAGE") {
    return promo.amount + "%";
  } else {
    return "$" + promo.amount;
  }
}

// if start date is same day, set time to start of current hour.
// This prevents the backend from returning an error that we're trying
// to create a promo with a start date from the past
export function adjustPromoStartDate(date: Date) {
  const inputDate = moment(date);
  const now = moment();

  if (inputDate.isSame(now, "day")) {
    // If the input date is today, return a new date with the current hour and reset minutes/seconds
    return inputDate
      .hour(now.hour())
      .minute(0)
      .second(0)
      .millisecond(0)
      .toDate();
  }

  // Otherwise, return the original date
  return inputDate.toDate();
}
