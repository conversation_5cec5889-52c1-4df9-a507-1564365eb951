export const ArrowBack = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 12H3.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.5 5.25L3.75 12L10.5 18.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ChevronDown = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17 10L12 15L7 10"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ChevronDownLg = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.5 9.5L12 17L4.5 9.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const LogOut = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.3125 8.0625L20.25 12L16.3125 15.9375"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.75 12H20.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.75 20.25H4.5C4.30109 20.25 4.11032 20.171 3.96967 20.0303C3.82902 19.8897 3.75 19.6989 3.75 19.5V4.5C3.75 4.30109 3.82902 4.11032 3.96967 3.96967C4.11032 3.82902 4.30109 3.75 4.5 3.75H9.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const User = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 15C15.3137 15 18 12.3137 18 9C18 5.68629 15.3137 3 12 3C8.68629 3 6 5.68629 6 9C6 12.3137 8.68629 15 12 15Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.90625 20.2501C3.82775 18.6537 5.15328 17.328 6.74958 16.4062C8.34588 15.4845 10.1567 14.9993 12 14.9993C13.8433 14.9993 15.6541 15.4845 17.2504 16.4062C18.8467 17.328 20.1722 18.6537 21.0938 20.2501"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Ticketing = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 5.25V18.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.25001 15.675C2.24906 15.5023 2.3085 15.3346 2.41807 15.2011C2.52763 15.0676 2.68042 14.9765 2.85001 14.9437C3.52456 14.8005 4.12946 14.43 4.56349 13.8941C4.99752 13.3583 5.23435 12.6896 5.23435 12C5.23435 11.3104 4.99752 10.6417 4.56349 10.1059C4.12946 9.57002 3.52456 9.19948 2.85001 9.05625C2.68042 9.02346 2.52763 8.93242 2.41807 8.79889C2.3085 8.66535 2.24906 8.49773 2.25001 8.325V6C2.25001 5.80109 2.32903 5.61032 2.46968 5.46967C2.61033 5.32902 2.8011 5.25 3.00001 5.25H21C21.1989 5.25 21.3897 5.32902 21.5303 5.46967C21.671 5.61032 21.75 5.80109 21.75 6V8.325C21.751 8.49773 21.6915 8.66535 21.582 8.79889C21.4724 8.93242 21.3196 9.02346 21.15 9.05625C20.4755 9.19948 19.8706 9.57002 19.4365 10.1059C19.0025 10.6417 18.7657 11.3104 18.7657 12C18.7657 12.6896 19.0025 13.3583 19.4365 13.8941C19.8706 14.43 20.4755 14.8005 21.15 14.9437C21.3196 14.9765 21.4724 15.0676 21.582 15.2011C21.6915 15.3346 21.751 15.5023 21.75 15.675V18C21.75 18.1989 21.671 18.3897 21.5303 18.5303C21.3897 18.671 21.1989 18.75 21 18.75H3.00001C2.8011 18.75 2.61033 18.671 2.46968 18.5303C2.32903 18.3897 2.25001 18.1989 2.25001 18V15.675Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Users = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.25 15C10.9424 15 13.125 12.8174 13.125 10.125C13.125 7.43261 10.9424 5.25 8.25 5.25C5.55761 5.25 3.375 7.43261 3.375 10.125C3.375 12.8174 5.55761 15 8.25 15Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.5686 5.42813C14.9996 5.31124 15.444 5.25136 15.8905 5.25C17.1834 5.25 18.4234 5.76361 19.3376 6.67785C20.2519 7.59209 20.7655 8.83207 20.7655 10.125C20.7655 11.4179 20.2519 12.6579 19.3376 13.5721C18.4234 14.4864 17.1834 15 15.8905 15"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.5 18.5061C2.2612 17.4229 3.27191 16.5388 4.44676 15.9285C5.6216 15.3181 6.92608 14.9995 8.25 14.9995C9.57392 14.9995 10.8784 15.3181 12.0532 15.9285C13.2281 16.5388 14.2388 17.4229 15 18.5061"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.8906 15C17.2147 14.9992 18.5194 15.3174 19.6944 15.9277C20.8693 16.5381 21.8799 17.4225 22.6406 18.5063"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PaperPlane = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_4617_70716)">
      <path
        d="M6.70555 11.7365C6.76464 11.8994 6.76464 12.078 6.70555 12.2409L3.7993 20.745C3.74734 20.8885 3.74044 21.0445 3.7795 21.1921C3.81857 21.3397 3.90175 21.4718 4.01793 21.5709C4.1341 21.67 4.27774 21.7312 4.42966 21.7464C4.58157 21.7617 4.73451 21.7302 4.86805 21.6562L20.618 12.6478C20.7353 12.5829 20.833 12.4878 20.901 12.3724C20.9691 12.257 21.005 12.1255 21.005 11.9915C21.005 11.8575 20.9691 11.726 20.901 11.6106C20.833 11.4952 20.7353 11.4001 20.618 11.3353L4.86805 2.34933C4.73484 2.27483 4.58207 2.24274 4.43014 2.25735C4.27821 2.27197 4.13435 2.33259 4.01779 2.43112C3.90122 2.52965 3.81749 2.6614 3.77778 2.80878C3.73807 2.95615 3.74427 3.11213 3.79555 3.25589L6.70555 11.7365Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.75 12H6.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_4617_70716">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ArrowSquareIn = () => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_4122_4481)">
      <path
        d="M11.25 19.25L11.2491 13.2509L5.25 13.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.75 20.75L11.25 13.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 17.75H19.5C19.6989 17.75 19.8897 17.671 20.0303 17.5303C20.171 17.3897 20.25 17.1989 20.25 17V5C20.25 4.80109 20.171 4.61032 20.0303 4.46967C19.8897 4.32902 19.6989 4.25 19.5 4.25H7.5C7.30109 4.25 7.11032 4.32902 6.96967 4.46967C6.82902 4.61032 6.75 4.80109 6.75 5V9.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_4122_4481">
        <rect
          width="24"
          height="24"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Sliders = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.875 16.125H3.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.25 16.125H17.625"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.75 18C16.7855 18 17.625 17.1605 17.625 16.125C17.625 15.0895 16.7855 14.25 15.75 14.25C14.7145 14.25 13.875 15.0895 13.875 16.125C13.875 17.1605 14.7145 18 15.75 18Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.875 7.875H3.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.25 7.875H11.625"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.75 9.75C10.7855 9.75 11.625 8.91053 11.625 7.875C11.625 6.83947 10.7855 6 9.75 6C8.71447 6 7.875 6.83947 7.875 7.875C7.875 8.91053 8.71447 9.75 9.75 9.75Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Close = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.75 5.25L5.25 18.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18.75 18.75L5.25 5.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Dashboard = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.5 4.5H4.5V10.5H10.5V4.5Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M19.5 4.5H13.5V10.5H19.5V4.5Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.5 13.5H4.5V19.5H10.5V13.5Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M19.5 13.5H13.5V19.5H19.5V13.5Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CaretRight = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 4.5L16.5 12L9 19.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CaretLeft = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15 19.5L7.5 12L15 4.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ArrowLeft = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 12H3.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.5 5.25L3.75 12L10.5 18.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const MagnifyingGlass = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.875 18.75C15.2242 18.75 18.75 15.2242 18.75 10.875C18.75 6.52576 15.2242 3 10.875 3C6.52576 3 3 6.52576 3 10.875C3 15.2242 6.52576 18.75 10.875 18.75Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.4436 16.4437L20.9999 21"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Tick = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 7.25L9.75 17.75L4.5 12.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Export = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.0625 5.4375L12 1.5L15.9375 5.4375"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 12V1.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.5 9H18.75C18.9489 9 19.1397 9.07902 19.2803 9.21967C19.421 9.36032 19.5 9.55109 19.5 9.75V19.5C19.5 19.6989 19.421 19.8897 19.2803 20.0303C19.1397 20.171 18.9489 20.25 18.75 20.25H5.25C5.05109 20.25 4.86032 20.171 4.71967 20.0303C4.57902 19.8897 4.5 19.6989 4.5 19.5V9.75C4.5 9.55109 4.57902 9.36032 4.71967 9.21967C4.86032 9.07902 5.05109 9 5.25 9H7.5"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Home = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.0062 10.275L12.5062 3.45933C12.368 3.33284 12.1874 3.2627 12 3.2627C11.8126 3.2627 11.632 3.33284 11.4937 3.45933L3.99375 10.275C3.91828 10.3462 3.85783 10.4319 3.81597 10.5269C3.77411 10.6219 3.75168 10.7243 3.75 10.8281V19.5C3.75 19.6989 3.82902 19.8896 3.96967 20.0303C4.11032 20.1709 4.30109 20.25 4.5 20.25H19.5C19.6989 20.25 19.8897 20.1709 20.0303 20.0303C20.171 19.8896 20.25 19.6989 20.25 19.5V10.8281C20.2483 10.7243 20.2259 10.6219 20.184 10.5269C20.1422 10.4319 20.0817 10.3462 20.0062 10.275V10.275Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Spanner = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.5219 6.6563C21.0138 7.80501 21.1343 9.07881 20.8663 10.2994C20.5983 11.5199 19.9554 12.6261 19.0275 13.4631C18.0996 14.3001 16.9331 14.8259 15.6915 14.9671C14.4499 15.1082 13.1952 14.8575 12.1031 14.25V14.25L6.84375 20.3438C6.42106 20.7665 5.84777 21.0039 5.25 21.0039C4.65222 21.0039 4.07894 20.7665 3.65625 20.3438C3.23356 19.9211 2.99609 19.3478 2.99609 18.75C2.99609 18.1523 3.23356 17.579 3.65625 17.1563L9.75 11.8969C9.14258 10.8049 8.89188 9.55017 9.03299 8.30854C9.1741 7.06691 9.69996 5.90049 10.5369 4.97257C11.3739 4.04466 12.4801 3.40171 13.7007 3.13374C14.9212 2.86578 16.195 2.98621 17.3437 3.47817L13.4062 7.4063L13.9406 10.0594L16.5937 10.5938L20.5219 6.6563Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Coin = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 14.25C17.3848 14.25 21.75 12.2353 21.75 9.75C21.75 7.26472 17.3848 5.25 12 5.25C6.61522 5.25 2.25 7.26472 2.25 9.75C2.25 12.2353 6.61522 14.25 12 14.25Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12 14.25V18.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.25 9.75V14.25C2.25 16.5 6 18.75 12 18.75C18 18.75 21.75 16.5 21.75 14.25V9.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18 13.3218V17.8218"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 13.3218V17.8218"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Cube = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21 16.6217V7.378C20.9993 7.24448 20.9634 7.1135 20.8959 6.99829C20.8284 6.88307 20.7317 6.78768 20.6156 6.72175L12.3656 2.08112C12.2545 2.01694 12.1284 1.98315 12 1.98315C11.8716 1.98315 11.7455 2.01694 11.6344 2.08112L3.38437 6.72175C3.26827 6.78768 3.1716 6.88307 3.10411 6.99829C3.03663 7.1135 3.00072 7.24448 3 7.378V16.6217C3.00072 16.7553 3.03663 16.8862 3.10411 17.0015C3.1716 17.1167 3.26827 17.2121 3.38437 17.278L11.6344 21.9186C11.7455 21.9828 11.8716 22.0166 12 22.0166C12.1284 22.0166 12.2545 21.9828 12.3656 21.9186L20.6156 17.278C20.7317 17.2121 20.8284 17.1167 20.8959 17.0015C20.9634 16.8862 20.9993 16.7553 21 16.6217V16.6217Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M20.8973 6.99365L12.0848 11.9999L3.10352 6.99365"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.0844 12L12 22.0125"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ArrowRight = ({
  color = "currentColor",
  width = 24,
  height = 24,
}: any) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.75 12H20.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.5 5.25L20.25 12L13.5 18.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const XCircle = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 9L9 15"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 15L9 9"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const FloppyDisk = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 8.55938V19.5C20.25 19.6989 20.171 19.8897 20.0303 20.0303C19.8897 20.171 19.6989 20.25 19.5 20.25H4.5C4.30109 20.25 4.11032 20.171 3.96967 20.0303C3.82902 19.8897 3.75 19.6989 3.75 19.5V4.5C3.75 4.30109 3.82902 4.11033 3.96967 3.96967C4.11032 3.82902 4.30109 3.75 4.5 3.75H15.4406C15.538 3.74966 15.6345 3.76853 15.7246 3.80553C15.8147 3.84253 15.8966 3.89694 15.9656 3.96563L20.0344 8.03438C20.1031 8.10341 20.1575 8.18532 20.1945 8.27541C20.2315 8.36549 20.2503 8.46199 20.25 8.55938V8.55938Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.5 20.25V14.25C7.5 14.0511 7.57902 13.8603 7.71967 13.7197C7.86032 13.579 8.05109 13.5 8.25 13.5H15.75C15.9489 13.5 16.1397 13.579 16.2803 13.7197C16.421 13.8603 16.5 14.0511 16.5 14.25V20.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.25 6.75H9"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Plus = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_16465_45822)">
      <path
        d="M3.75 12H20.25"
        stroke={color}
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12 3.75V20.25"
        stroke={color}
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_16465_45822">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Pencil = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.69063 20.2499H4.5C4.30109 20.2499 4.11033 20.1709 3.96967 20.0302C3.82902 19.8896 3.75 19.6988 3.75 19.4999V15.3093C3.74966 15.2119 3.76853 15.1154 3.80553 15.0253C3.84253 14.9352 3.89694 14.8533 3.96563 14.7843L15.2156 3.53429C15.2854 3.46343 15.3686 3.40715 15.4603 3.36874C15.5521 3.33033 15.6505 3.31055 15.75 3.31055C15.8495 3.31055 15.9479 3.33033 16.0397 3.36874C16.1314 3.40715 16.2146 3.46343 16.2844 3.53429L20.4656 7.71554C20.5365 7.78533 20.5928 7.86851 20.6312 7.96026C20.6696 8.052 20.6894 8.15046 20.6894 8.24992C20.6894 8.34938 20.6696 8.44784 20.6312 8.53958C20.5928 8.63132 20.5365 8.71451 20.4656 8.78429L9.21563 20.0343C9.1466 20.103 9.06469 20.1574 8.9746 20.1944C8.88452 20.2314 8.78802 20.2503 8.69063 20.2499V20.2499Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.75 6L18 11.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.375 8.625L6.375 17.625"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.95312 20.2031L3.79688 15.0469"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PencilSimple = ({ color = "currentColor" }: any) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
  >
    <path
      d="M8.68969 20.75H4.5C4.30109 20.75 4.11032 20.671 3.96967 20.5303C3.82902 20.3897 3.75 20.1989 3.75 20V15.8103C3.75009 15.6117 3.82899 15.4212 3.96938 15.2806L15.5306 3.71937C15.6713 3.57883 15.862 3.49988 16.0608 3.49988C16.2596 3.49988 16.4503 3.57883 16.5909 3.71937L20.7806 7.90625C20.9212 8.04689 21.0001 8.23758 21.0001 8.4364C21.0001 8.63523 20.9212 8.82592 20.7806 8.96656L9.21937 20.5306C9.07883 20.671 8.88834 20.7499 8.68969 20.75Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.75 6.5L18 11.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export const SendPassword = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.5908 16.1565C19.7352 18.0233 18.2668 19.5414 16.4295 20.4587C14.5922 21.376 12.4965 21.6374 10.4901 21.1995C8.48381 20.7616 6.68757 19.6507 5.39957 18.0513C4.11157 16.4519 3.40929 14.4601 3.40929 12.4065C3.40929 10.3529 4.11157 8.36113 5.39957 6.7617C6.68757 5.16226 8.48381 4.0514 10.4901 3.6135C12.4965 3.1756 14.5922 3.43701 16.4295 4.35432C18.2668 5.27164 19.7352 6.78969 20.5908 8.6565"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18 9H21V6"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.124 10.4375H8.87402C8.70143 10.4375 8.56152 10.5774 8.56152 10.75V15.125C8.56152 15.2976 8.70143 15.4375 8.87402 15.4375H15.124C15.2966 15.4375 15.4365 15.2976 15.4365 15.125V10.75C15.4365 10.5774 15.2966 10.4375 15.124 10.4375Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.5928 10.4375V9.03125C10.5928 8.65829 10.7409 8.3006 11.0047 8.03688C11.2684 7.77316 11.6261 7.625 11.999 7.625C12.372 7.625 12.7297 7.77316 12.9934 8.03688C13.2571 8.3006 13.4053 8.65829 13.4053 9.03125V10.4375"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Delete = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.25 5.25H3.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.75 9.75V15.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.25 9.75V15.75"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18.75 5.25V19.5C18.75 19.6989 18.671 19.8897 18.5303 20.0303C18.3897 20.171 18.1989 20.25 18 20.25H6C5.80109 20.25 5.61032 20.171 5.46967 20.0303C5.32902 19.8897 5.25 19.6989 5.25 19.5V5.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.75 5.25V3.75C15.75 3.35218 15.592 2.97064 15.3107 2.68934C15.0294 2.40804 14.6478 2.25 14.25 2.25H9.75C9.35218 2.25 8.97064 2.40804 8.68934 2.68934C8.40804 2.97064 8.25 3.35218 8.25 3.75V5.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const AddUser = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.75 12.75H23.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M21 10.5V15"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.125 15C13.2316 15 15.75 12.4816 15.75 9.375C15.75 6.2684 13.2316 3.75 10.125 3.75C7.0184 3.75 4.5 6.2684 4.5 9.375C4.5 12.4816 7.0184 15 10.125 15Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.08203 18.7501C3.06758 17.5755 4.29844 16.6309 5.68809 15.9829C7.07774 15.3348 8.59246 14.999 10.1258 14.999C11.6591 14.999 13.1738 15.3348 14.5635 15.9829C15.9531 16.6309 17.184 17.5755 18.1695 18.7501"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Clear = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 9L9 15"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 15L9 9"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Eye = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 4.375C3.75 4.375 1.25 10 1.25 10C1.25 10 3.75 15.625 10 15.625C16.25 15.625 18.75 10 18.75 10C18.75 10 16.25 4.375 10 4.375Z"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 13.125C11.7259 13.125 13.125 11.7259 13.125 10C13.125 8.27411 11.7259 6.875 10 6.875C8.27411 6.875 6.875 8.27411 6.875 10C6.875 11.7259 8.27411 13.125 10 13.125Z"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EyeSlash = () => (
  <svg
    width="21"
    height="21"
    viewBox="0 0 21 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.72461 3.16016L17.2246 16.9102"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.0758 12.3475C12.5024 12.8729 11.752 13.163 10.9743 13.16C10.3435 13.16 9.7276 12.969 9.2074 12.6124C8.68721 12.2557 8.28713 11.7499 8.05974 11.1616C7.83236 10.5733 7.78833 9.92994 7.93343 9.31612C8.07853 8.70231 8.40597 8.14677 8.87271 7.72253"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.75586 5.39465C3.56836 7.00403 2.22461 10.0353 2.22461 10.0353C2.22461 10.0353 4.72461 15.6603 10.9746 15.6603C12.4391 15.6722 13.8854 15.3348 15.1934 14.6759"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.2715 13.2461C18.9746 11.7227 19.7246 10.0352 19.7246 10.0352C19.7246 10.0352 17.2246 4.41018 10.9746 4.41018C10.4328 4.40911 9.89182 4.45353 9.35742 4.54299"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.5605 6.96484C12.2251 7.09076 12.8307 7.42943 13.2859 7.92972C13.7411 8.43002 14.0212 9.06481 14.084 9.73828"
      stroke="#160B2A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ClockTimer = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12 7.5L12 12L15.75 14.25"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.75 9.75L3 9.75L3 6"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.3375 18C7.51684 19.1128 8.99798 19.8535 10.5958 20.1294C12.1937 20.4052 13.8374 20.2041 15.3217 19.5512C16.8059 18.8982 18.0648 17.8224 18.9411 16.458C19.8173 15.0937 20.2721 13.5014 20.2486 11.8801C20.2251 10.2587 19.7244 8.68026 18.8089 7.3419C17.8934 6.00354 16.6039 4.96463 15.1014 4.35498C13.5988 3.74532 11.95 3.59195 10.3608 3.91404C8.77157 4.23612 7.31253 5.01938 6.16594 6.16594C5.0625 7.28344 4.15125 8.33719 3 9.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Pause = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_16432_44821)">
      <path
        d="M18.75 3.75H15C14.5858 3.75 14.25 4.08579 14.25 4.5V19.5C14.25 19.9142 14.5858 20.25 15 20.25H18.75C19.1642 20.25 19.5 19.9142 19.5 19.5V4.5C19.5 4.08579 19.1642 3.75 18.75 3.75Z"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M9 3.75H5.25C4.83579 3.75 4.5 4.08579 4.5 4.5V19.5C4.5 19.9142 4.83579 20.25 5.25 20.25H9C9.41421 20.25 9.75 19.9142 9.75 19.5V4.5C9.75 4.08579 9.41421 3.75 9 3.75Z"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_16432_44821">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Play = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.75 3.73876V20.2613C6.75245 20.3931 6.78962 20.522 6.85776 20.6349C6.9259 20.7478 7.0226 20.8407 7.13812 20.9043C7.25364 20.9679 7.38388 20.9999 7.51572 20.9972C7.64756 20.9944 7.77634 20.9569 7.88906 20.8884L21.3966 12.6272C21.5045 12.5619 21.5937 12.4699 21.6556 12.36C21.7175 12.2501 21.7501 12.1261 21.7501 12C21.7501 11.8739 21.7175 11.7499 21.6556 11.64C21.5937 11.5302 21.5045 11.4381 21.3966 11.3728L7.88906 3.11157C7.77634 3.04314 7.64756 3.00564 7.51572 3.00285C7.38388 3.00007 7.25364 3.03209 7.13812 3.0957C7.0226 3.1593 6.9259 3.25224 6.85776 3.36514C6.78962 3.47804 6.75245 3.60691 6.75 3.73876Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Calendar = ({ color = "currentColor" }: any) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.5 3.75H4.5C4.08579 3.75 3.75 4.08579 3.75 4.5V19.5C3.75 19.9142 4.08579 20.25 4.5 20.25H19.5C19.9142 20.25 20.25 19.9142 20.25 19.5V4.5C20.25 4.08579 19.9142 3.75 19.5 3.75Z"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.5 2.25V5.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.5 2.25V5.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.75 8.25H20.25"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CaretUp = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.5 15L12 7.5L19.5 15"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CaretDown = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M19.5 9L12 16.5L4.5 9"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Power = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_292_1359)">
      <path
        d="M10 3.75V10"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.75 4.375C15.6312 5.60156 16.875 7.58672 16.875 10C16.875 11.8234 16.1507 13.572 14.8614 14.8614C13.572 16.1507 11.8234 16.875 10 16.875C8.17664 16.875 6.42795 16.1507 5.13864 14.8614C3.84933 13.572 3.125 11.8234 3.125 10C3.125 7.58672 4.36875 5.60156 6.25 4.375"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_292_1359">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Mobile = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_292_965)">
      <path
        d="M15.666 16.875L15.666 3.125C15.666 2.43464 15.1064 1.875 14.416 1.875L6.91602 1.875C6.22566 1.875 5.66602 2.43464 5.66602 3.125L5.66602 16.875C5.66602 17.5654 6.22566 18.125 6.91602 18.125H14.416C15.1064 18.125 15.666 17.5654 15.666 16.875Z"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.666 5.625C11.1838 5.625 11.6035 5.20527 11.6035 4.6875C11.6035 4.16973 11.1838 3.75 10.666 3.75C10.1482 3.75 9.72852 4.16973 9.72852 4.6875C9.72852 5.20527 10.1482 5.625 10.666 5.625Z"
        fill="#474747"
      />
    </g>
    <defs>
      <clipPath id="clip0_292_965">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0.666016)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const ArrowsClockwise = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_292_1192)">
      <path
        d="M5.33398 5C5.33398 5 7.20898 3.125 10.334 3.125C14.709 3.125 17.209 7.5 17.209 7.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.334 15C15.334 15 13.459 16.875 10.334 16.875C5.95898 16.875 3.45898 12.5 3.45898 12.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.459 7.5H17.209V3.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.20898 12.5H3.45898V16.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_292_1192">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0.333984)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const SimCard = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_292_1900)">
      <path
        d="M15.625 17.5H4.375C4.20924 17.5 4.05027 17.4342 3.93306 17.3169C3.81585 17.1997 3.75 17.0408 3.75 16.875V3.125C3.75 2.95924 3.81585 2.80027 3.93306 2.68306C4.05027 2.56585 4.20924 2.5 4.375 2.5H11.875L16.25 6.875V16.875C16.25 17.0408 16.1842 17.1997 16.0669 17.3169C15.9497 17.4342 15.7908 17.5 15.625 17.5Z"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.75 9.375H6.25V15H13.75V9.375Z"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_292_1900">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Receipt = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_16432_44815)">
      <path
        d="M7.5 9.75H16.5"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7.5 12.75H16.5"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3 19.5V5.25C3 5.05109 3.07902 4.86032 3.21967 4.71967C3.36032 4.57902 3.55109 4.5 3.75 4.5H20.25C20.4489 4.5 20.6397 4.57902 20.7803 4.71967C20.921 4.86032 21 5.05109 21 5.25V19.5L18 18L15 19.5L12 18L9 19.5L6 18L3 19.5Z"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_16432_44815">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ArrowsLeftRight = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_292_1940)">
      <path
        d="M14.084 11.25L16.584 13.75L14.084 16.25"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.08398 13.75H16.584"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.58398 8.75L4.08398 6.25L6.58398 3.75"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.584 6.25H4.08398"
        stroke="#474747"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_292_1940">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0.333984)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const X = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.75 5.25L5.25 18.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M18.75 18.75L5.25 5.25"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const MapPin = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_310_10132)">
      <path
        d="M12 12.75C13.6569 12.75 15 11.4069 15 9.75C15 8.09315 13.6569 6.75 12 6.75C10.3431 6.75 9 8.09315 9 9.75C9 11.4069 10.3431 12.75 12 12.75Z"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.5 9.75C19.5 16.5 12 21.75 12 21.75C12 21.75 4.5 16.5 4.5 9.75C4.5 7.76088 5.29018 5.85322 6.6967 4.4467C8.10322 3.04018 10.0109 2.25 12 2.25C13.9891 2.25 15.8968 3.04018 17.3033 4.4467C18.7098 5.85322 19.5 7.76088 19.5 9.75Z"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_310_10132">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Envelope = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_310_10135)">
      <path
        d="M3 5.25H21V18C21 18.1989 20.921 18.3897 20.7803 18.5303C20.6397 18.671 20.4489 18.75 20.25 18.75H3.75C3.55109 18.75 3.36032 18.671 3.21967 18.5303C3.07902 18.3897 3 18.1989 3 18V5.25Z"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21 5.25L12 13.5L3 5.25"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_310_10135">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Phone = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_310_10138)">
      <path
        d="M15.4116 13.6256C15.5154 13.5565 15.6349 13.5144 15.7591 13.5031C15.8834 13.4918 16.0085 13.5117 16.1231 13.5609L20.5444 15.5419C20.6934 15.6055 20.8177 15.7158 20.8989 15.856C20.98 15.9963 21.0135 16.1591 20.9944 16.32C20.8487 17.4085 20.3127 18.407 19.486 19.1299C18.6593 19.8528 17.5982 20.2508 16.5 20.25C13.1185 20.25 9.87548 18.9067 7.48439 16.5156C5.0933 14.1245 3.75 10.8815 3.75 7.49998C3.74916 6.4018 4.1472 5.34068 4.87009 4.51398C5.59298 3.68728 6.59152 3.15126 7.68 3.0056C7.84091 2.98649 8.00368 3.02 8.14395 3.10112C8.28422 3.18224 8.39444 3.30661 8.45813 3.4556L10.4391 7.8806C10.4877 7.99426 10.5076 8.11818 10.4968 8.24134C10.486 8.36451 10.4449 8.48309 10.3772 8.58654L8.37375 10.9687C8.30269 11.076 8.26066 11.1998 8.25179 11.3281C8.24291 11.4565 8.26749 11.5849 8.32313 11.7009C9.09844 13.2881 10.7391 14.909 12.3309 15.6769C12.4475 15.7322 12.5766 15.7563 12.7053 15.7466C12.834 15.7369 12.958 15.6938 13.065 15.6215L15.4116 13.6256Z"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_310_10138">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Sparkle = () => (
  <svg
    width="19"
    height="19"
    viewBox="0 0 19 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.57876 11.9816L1.73563 10.2003C1.59322 10.1478 1.47035 10.0528 1.38356 9.92826C1.29677 9.80372 1.25024 9.65556 1.25024 9.50376C1.25024 9.35196 1.29677 9.20381 1.38356 9.07926C1.47035 8.95472 1.59322 8.85977 1.73563 8.8072L6.57876 7.02595C6.6796 6.98903 6.77118 6.93056 6.84711 6.85462C6.92305 6.77868 6.98152 6.68711 7.01844 6.58626L8.79969 1.74314C8.85226 1.60073 8.94721 1.47785 9.07176 1.39107C9.1963 1.30428 9.34446 1.25775 9.49626 1.25775C9.64806 1.25775 9.79621 1.30428 9.92076 1.39107C10.0453 1.47785 10.1403 1.60073 10.1928 1.74314L11.9741 6.58626C12.011 6.68711 12.0695 6.77868 12.1454 6.85462C12.2213 6.93056 12.3129 6.98903 12.4138 7.02595L17.2569 8.8072C17.3993 8.85977 17.5222 8.95472 17.609 9.07926C17.6957 9.20381 17.7423 9.35196 17.7423 9.50376C17.7423 9.65556 17.6957 9.80372 17.609 9.92826C17.5222 10.0528 17.3993 10.1478 17.2569 10.2003L12.4138 11.9816C12.3129 12.0185 12.2213 12.077 12.1454 12.1529C12.0695 12.2288 12.011 12.3204 11.9741 12.4213L10.1928 17.2644C10.1403 17.4068 10.0453 17.5297 9.92076 17.6165C9.79621 17.7032 9.64806 17.7498 9.49626 17.7498C9.34446 17.7498 9.1963 17.7032 9.07176 17.6165C8.94721 17.5297 8.85226 17.4068 8.79969 17.2644L7.01844 12.4213C6.98152 12.3204 6.92305 12.2288 6.84711 12.1529C6.77118 12.077 6.6796 12.0185 6.57876 11.9816Z"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const MagicWand = () => (
  <svg
    width="25"
    height="24"
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_736_3152)">
      <path
        d="M20.75 12V16.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.5 14.25H23"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 3.75V8.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.75 6H10.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.25 17.25V20.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.75 18.75H17.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14 7.5L17 10.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.5305 3.96951L4.46978 17.0302C4.17689 17.3231 4.17689 17.798 4.46978 18.0909L6.4088 20.0299C6.70169 20.3228 7.17657 20.3228 7.46946 20.0299L20.5302 6.96919C20.8231 6.6763 20.8231 6.20143 20.5302 5.90853L18.5911 3.96951C18.2983 3.67662 17.8234 3.67662 17.5305 3.96951Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_736_3152">
        <rect width="24" height="24" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export const CheckCircle = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_546_19822)">
      <path
        d="M11 17L14 20L21 13"
        stroke="#3A8E5C"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16 28C22.6274 28 28 22.6274 28 16C28 9.37258 22.6274 4 16 4C9.37258 4 4 9.37258 4 16C4 22.6274 9.37258 28 16 28Z"
        stroke="#3A8E5C"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_546_19822">
        <rect width="32" height="32" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Info = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_435_14716)">
      <path
        d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.25 11.25C11.4489 11.25 11.6397 11.329 11.7803 11.4697C11.921 11.6103 12 11.8011 12 12V15.75C12 15.9489 12.079 16.1397 12.2197 16.2803C12.3603 16.421 12.5511 16.5 12.75 16.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.625 9C12.2463 9 12.75 8.49632 12.75 7.875C12.75 7.25368 12.2463 6.75 11.625 6.75C11.0037 6.75 10.5 7.25368 10.5 7.875C10.5 8.49632 11.0037 9 11.625 9Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_435_14716">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Reports = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.75 21H5.25C5.05109 21 4.86032 20.921 4.71967 20.7803C4.57902 20.6397 4.5 20.4489 4.5 20.25V6.75C4.5 6.55109 4.57902 6.36032 4.71967 6.21967C4.86032 6.07902 5.05109 6 5.25 6H12.75L16.5 9.75V20.25C16.5 20.4489 16.421 20.6397 16.2803 20.7803C16.1397 20.921 15.9489 21 15.75 21Z"
      stroke="#1A1A1A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.5 6V3.75C7.5 3.55109 7.57902 3.36032 7.71967 3.21967C7.86032 3.07902 8.05109 3 8.25 3H15.75L19.5 6.75V17.25C19.5 17.4489 19.421 17.6397 19.2803 17.7803C19.1397 17.921 18.9489 18 18.75 18H16.5"
      stroke="#1A1A1A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.25 14.25H12.75"
      stroke="#1A1A1A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.25 17.25H12.75"
      stroke="#1A1A1A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PlayButton = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="68"
    height="68"
    viewBox="0 0 68 68"
    fill="none"
  >
    <g clipPath="url(#clip0_4215_21307)">
      <path
        d="M19.125 10.5931V57.4069C19.1319 57.7804 19.2373 58.1456 19.4303 58.4654C19.6234 58.7853 19.8974 59.0486 20.2247 59.2289C20.552 59.4091 20.921 59.4998 21.2945 59.4919C21.6681 59.484 22.033 59.3778 22.3523 59.1839L60.6236 35.777C60.9293 35.592 61.1821 35.3312 61.3576 35.0199C61.533 34.7086 61.6252 34.3573 61.6252 34C61.6252 33.6426 61.533 33.2913 61.3576 32.98C61.1821 32.6687 60.9293 32.408 60.6236 32.2229L22.3523 8.81607C22.033 8.62219 21.6681 8.51594 21.2945 8.50804C20.921 8.50015 20.552 8.59088 20.2247 8.7711C19.8974 8.95131 19.6234 9.21463 19.4303 9.53452C19.2373 9.8544 19.1319 10.2195 19.125 10.5931Z"
        fill="#F47D27"
      />
    </g>
    <defs>
      <clipPath id="clip0_4215_21307">
        <rect width="68" height="68" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Lifebuoy = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <g clipPath="url(#clip0_4238_4659)">
      <path
        d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 15.75C14.0711 15.75 15.75 14.0711 15.75 12C15.75 9.92893 14.0711 8.25 12 8.25C9.92893 8.25 8.25 9.92893 8.25 12C8.25 14.0711 9.92893 15.75 12 15.75Z"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.34922 9.34873L5.63672 5.63623"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.6523 9.34873L18.3648 5.63623"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.6523 14.6512L18.3648 18.3637"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.34922 14.6512L5.63672 18.3637"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_4238_4659">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Bell = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M20.52 15.2099L18.72 13.3999V8.93989C18.7439 7.28372 18.1678 5.6748 17.0981 4.41023C16.0283 3.14566 14.5372 2.31083 12.9 2.05989C11.9498 1.93476 10.9838 2.01371 10.0666 2.29147C9.1493 2.56923 8.30182 3.03942 7.58068 3.67066C6.85953 4.30189 6.2813 5.07967 5.88456 5.95209C5.48782 6.82451 5.2817 7.7715 5.27995 8.72989V13.3999L3.47995 15.2099C3.25386 15.4398 3.10053 15.7312 3.03915 16.0477C2.97777 16.3642 3.01105 16.6918 3.13483 16.9895C3.25861 17.2872 3.4674 17.5419 3.7351 17.7216C4.0028 17.9013 4.31753 17.9981 4.63995 17.9999H7.99995V18.3399C8.04666 19.3551 8.4939 20.3104 9.24368 20.9964C9.99345 21.6825 10.9846 22.0433 12 21.9999C13.0153 22.0433 14.0065 21.6825 14.7562 20.9964C15.506 20.3104 15.9532 19.3551 16 18.3399V17.9999H19.36C19.6824 17.9981 19.9971 17.9013 20.2648 17.7216C20.5325 17.5419 20.7413 17.2872 20.8651 16.9895C20.9889 16.6918 21.0221 16.3642 20.9608 16.0477C20.8994 15.7312 20.746 15.4398 20.52 15.2099ZM14 18.3399C13.9445 18.8209 13.7056 19.262 13.333 19.5712C12.9605 19.8804 12.4829 20.034 12 19.9999C11.517 20.034 11.0395 19.8804 10.6669 19.5712C10.2943 19.262 10.0554 18.8209 9.99995 18.3399V17.9999H14V18.3399ZM5.50995 15.9999L6.68995 14.8199C6.87712 14.6338 7.02561 14.4126 7.12687 14.1689C7.22813 13.9251 7.28016 13.6638 7.27995 13.3999V8.72989C7.2805 8.05529 7.42533 7.38862 7.70474 6.7746C7.98416 6.16059 8.39167 5.61345 8.89995 5.16989C9.40137 4.71558 9.99553 4.37564 10.6413 4.17363C11.2871 3.97161 11.969 3.91233 12.64 3.99989C13.7964 4.18767 14.8461 4.78691 15.5958 5.68727C16.3455 6.58764 16.7447 7.72853 16.72 8.89989V13.3999C16.7184 13.6631 16.7689 13.924 16.8684 14.1677C16.9679 14.4114 17.1146 14.633 17.3 14.8199L18.49 15.9999H5.50995Z"
      fill="#1B2023"
    />
  </svg>
);

export const BellNew = () => (
  <svg
    width="24"
    height="26"
    viewBox="0 0 24 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20.52 17.2099L18.72 15.3999V10.9399C18.744 9.28372 18.1679 7.6748 17.0981 6.41023C16.0284 5.14566 14.5372 4.31083 12.9 4.05989C11.9498 3.93476 10.9839 4.01371 10.0666 4.29147C9.14936 4.56923 8.30188 5.03942 7.58074 5.67066C6.85959 6.30189 6.28136 7.07967 5.88462 7.95209C5.48788 8.82451 5.28176 9.7715 5.28001 10.7299V15.3999L3.48001 17.2099C3.25392 17.4398 3.1006 17.7312 3.03921 18.0477C2.97783 18.3642 3.01111 18.6918 3.13489 18.9895C3.25867 19.2872 3.46746 19.5419 3.73516 19.7216C4.00286 19.9013 4.31759 19.9981 4.64001 19.9999H8.00001V20.3399C8.04672 21.3551 8.49396 22.3104 9.24374 22.9964C9.99351 23.6825 10.9847 24.0433 12 23.9999C13.0154 24.0433 14.0065 23.6825 14.7563 22.9964C15.5061 22.3104 15.9533 21.3551 16 20.3399V19.9999H19.36C19.6824 19.9981 19.9972 19.9013 20.2649 19.7216C20.5326 19.5419 20.7414 19.2872 20.8651 18.9895C20.9889 18.6918 21.0222 18.3642 20.9608 18.0477C20.8994 17.7312 20.7461 17.4398 20.52 17.2099ZM14 20.3399C13.9446 20.8209 13.7057 21.262 13.3331 21.5712C12.9605 21.8804 12.483 22.034 12 21.9999C11.517 22.034 11.0395 21.8804 10.6669 21.5712C10.2944 21.262 10.0555 20.8209 10 20.3399V19.9999H14V20.3399ZM5.51001 17.9999L6.69001 16.8199C6.87718 16.6338 7.02567 16.4126 7.12694 16.1689C7.2282 15.9251 7.28022 15.6638 7.28001 15.3999V10.7299C7.28056 10.0553 7.4254 9.38862 7.70481 8.7746C7.98422 8.16059 8.39174 7.61345 8.90001 7.16989C9.40143 6.71558 9.99559 6.37564 10.6414 6.17363C11.2871 5.97161 11.9691 5.91233 12.64 5.99989C13.7965 6.18767 14.8462 6.78691 15.5959 7.68727C16.3456 8.58764 16.7448 9.72853 16.72 10.8999V15.3999C16.7185 15.6631 16.7689 15.924 16.8685 16.1677C16.968 16.4114 17.1146 16.633 17.3 16.8199L18.49 17.9999H5.51001Z"
      fill="#1B2023"
    />
    <circle cx="16.5" cy="5.5" r="4.5" fill="#F37121" stroke="#F2F2F2" />
  </svg>
);

export const Clipboard = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 14.25H15"
      stroke="#1A1A1A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9 11.25H15"
      stroke="#1A1A1A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15 3.75H18.75C18.9489 3.75 19.1397 3.82902 19.2803 3.96967C19.421 4.11032 19.5 4.30109 19.5 4.5V20.25C19.5 20.4489 19.421 20.6397 19.2803 20.7803C19.1397 20.921 18.9489 21 18.75 21H5.25C5.05109 21 4.86032 20.921 4.71967 20.7803C4.57902 20.6397 4.5 20.4489 4.5 20.25V4.5C4.5 4.30109 4.57902 4.11032 4.71967 3.96967C4.86032 3.82902 5.05109 3.75 5.25 3.75H9"
      stroke="#1A1A1A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.25 6.75V6C8.25 5.00544 8.64509 4.05161 9.34835 3.34835C10.0516 2.64509 11.0054 2.25 12 2.25C12.9946 2.25 13.9484 2.64509 14.6517 3.34835C15.3549 4.05161 15.75 5.00544 15.75 6V6.75H8.25Z"
      stroke="#1A1A1A"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const UploadCloud = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9 19.5H6.75C5.35761 19.5 4.02226 18.9469 3.03769 17.9623C2.05312 16.9777 1.5 15.6424 1.5 14.25C1.5 12.8576 2.05312 11.5223 3.03769 10.5377C4.02226 9.55312 5.35761 9 6.75 9C7.18925 8.99984 7.62687 9.05336 8.05313 9.15938"
      stroke="#45474F"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.5 12C7.5 10.8116 7.7824 9.64023 8.32393 8.5824C8.86545 7.52456 9.6506 6.61055 10.6147 5.91568C11.5787 5.22082 12.6941 4.765 13.8689 4.58578C15.0437 4.40656 16.2443 4.50907 17.3717 4.88488C18.4991 5.26068 19.5211 5.89901 20.3534 6.74726C21.1857 7.59552 21.8045 8.62941 22.1589 9.76374C22.5132 10.8981 22.5929 12.1004 22.3914 13.2716C22.1899 14.4427 21.713 15.5493 21 16.5"
      stroke="#45474F"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.0718 15.1781L14.2499 12L17.428 15.1781"
      stroke="#45474F"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.25 19.5V12"
      stroke="#45474F"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Upload = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_10678_25232)">
      <path
        d="M12 14.25V3.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.25 14.25V19.5C20.25 19.6989 20.171 19.8897 20.0303 20.0303C19.8897 20.171 19.6989 20.25 19.5 20.25H4.5C4.30109 20.25 4.11032 20.171 3.96967 20.0303C3.82902 19.8897 3.75 19.6989 3.75 19.5V14.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.25 7.5L12 3.75L15.75 7.5"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_10678_25232">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Mountains = () => {
  return (
    <svg
      width="41"
      height="26"
      viewBox="0 0 41 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M25.8213 5.28904C27.1057 5.28904 28.147 4.2478 28.147 2.96337C28.147 1.67893 27.1057 0.637695 25.8213 0.637695C24.5368 0.637695 23.4956 1.67893 23.4956 2.96337C23.4956 4.2478 24.5368 5.28904 25.8213 5.28904Z"
        fill="#B5B5B5"
      />
      <path
        d="M24.1372 15.3673L29.1161 10.3942C29.4068 10.1036 29.801 9.94043 30.2121 9.94043C30.6231 9.94043 31.0173 10.1036 31.308 10.3942L39.0002 18.0922"
        stroke="#B5B5B5"
        strokeWidth="3.1009"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M1.78906 16.2752L12.3205 5.74183C12.4645 5.59768 12.6355 5.48332 12.8237 5.4053C13.0119 5.32727 13.2137 5.28711 13.4174 5.28711C13.6212 5.28711 13.8229 5.32727 14.0112 5.4053C14.1994 5.48332 14.3704 5.59768 14.5144 5.74183L32.6643 23.8937"
        stroke="#B5B5B5"
        strokeWidth="3.1009"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Box = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_10353_18191)">
      <path
        d="M12 12.1022V21.7472"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.06641 7.2113L12.0008 12.1013L20.9352 7.2113"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.61 17.1413L12.36 21.6581C12.2496 21.7185 12.1258 21.7502 12 21.7502C11.8742 21.7502 11.7504 21.7185 11.64 21.6581L3.39 17.1413C3.2722 17.0768 3.17386 16.9819 3.10526 16.8665C3.03666 16.751 3.0003 16.6193 3 16.485V7.51688C3.0003 7.3826 3.03666 7.25086 3.10526 7.13543C3.17386 7.01999 3.2722 6.92509 3.39 6.86063L11.64 2.34376C11.7504 2.28336 11.8742 2.25171 12 2.25171C12.1258 2.25171 12.2496 2.28336 12.36 2.34376L20.61 6.86063C20.7278 6.92509 20.8261 7.01999 20.8947 7.13543C20.9633 7.25086 20.9997 7.3826 21 7.51688V16.4831C21 16.6177 20.9638 16.7499 20.8952 16.8657C20.8266 16.9815 20.7281 17.0766 20.61 17.1413Z"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.64453 4.52905L16.4983 9.37499V14.25"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_10353_18191">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Storefront = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_15028_41795)">
      <path
        d="M4.5 13.0865V20.25H19.5V13.0865"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.0625 3.75H18.9375C19.1004 3.75003 19.2589 3.80311 19.389 3.90122C19.519 3.99932 19.6136 4.13711 19.6584 4.29375L21 9H3L4.34438 4.29375C4.38904 4.13757 4.48321 4.00012 4.61272 3.90206C4.74222 3.804 4.90006 3.75064 5.0625 3.75Z"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 9V10.5C9 11.2956 8.68393 12.0587 8.12132 12.6213C7.55871 13.1839 6.79565 13.5 6 13.5C5.20435 13.5 4.44129 13.1839 3.87868 12.6213C3.31607 12.0587 3 11.2956 3 10.5V9"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 9V10.5C15 11.2956 14.6839 12.0587 14.1213 12.6213C13.5587 13.1839 12.7956 13.5 12 13.5C11.2044 13.5 10.4413 13.1839 9.87868 12.6213C9.31607 12.0587 9 11.2956 9 10.5V9"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21 9V10.5C21 11.2956 20.6839 12.0587 20.1213 12.6213C19.5587 13.1839 18.7956 13.5 18 13.5C17.2044 13.5 16.4413 13.1839 15.8787 12.6213C15.3161 12.0587 15 11.2956 15 10.5V9"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_15028_41795">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Swap = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M7.5 15H19.5C19.6989 15 19.8897 14.921 20.0303 14.7803C20.171 14.6397 20.25 14.4489 20.25 14.25V4.5C20.25 4.30109 20.171 4.11032 20.0303 3.96967C19.8897 3.82902 19.6989 3.75 19.5 3.75H9C8.80109 3.75 8.61032 3.82902 8.46967 3.96967C8.32902 4.11032 8.25 4.30109 8.25 4.5V5.25"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.75 12.75L7.5 15L9.75 17.25"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.5 9H4.5C4.30109 9 4.11032 9.07902 3.96967 9.21967C3.82902 9.36032 3.75 9.55109 3.75 9.75V19.5C3.75 19.6989 3.82902 19.8897 3.96967 20.0303C4.11032 20.171 4.30109 20.25 4.5 20.25H15C15.1989 20.25 15.3897 20.171 15.5303 20.0303C15.671 19.8897 15.75 19.6989 15.75 19.5V18.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.25 11.25L16.5 9L14.25 6.75"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const GiftBox = () => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    // {...props}
  >
    <g clipPath="url(#clip0_16570_1798)">
      <path
        d="M20.25 7.5H3.75C3.33579 7.5 3 7.83579 3 8.25V11.25C3 11.6642 3.33579 12 3.75 12H20.25C20.6642 12 21 11.6642 21 11.25V8.25C21 7.83579 20.6642 7.5 20.25 7.5Z"
        stroke="#1A1A1A"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.5 12V18.75C19.5 18.9489 19.421 19.1397 19.2803 19.2803C19.1397 19.421 18.9489 19.5 18.75 19.5H5.25C5.05109 19.5 4.86032 19.421 4.71967 19.2803C4.57902 19.1397 4.5 18.9489 4.5 18.75V12"
        stroke="#1A1A1A"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 7.5V19.5"
        stroke="#1B2023"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.5741 2.92619C17.4497 3.80182 17.5012 5.27557 16.5741 6.09682C14.9887 7.50026 12 7.50026 12 7.50026C12 7.50026 12 4.51151 13.4062 2.92619C14.2247 1.99901 15.6984 2.05057 16.5741 2.92619Z"
        stroke="#1B2023"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.42595 2.92619C6.55032 3.80182 6.49876 5.27557 7.42595 6.09682C9.01126 7.50026 12 7.50026 12 7.50026C12 7.50026 12 4.51151 10.5938 2.92619C9.77532 1.99901 8.30157 2.05057 7.42595 2.92619Z"
        stroke="#1B2023"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_16570_1798">
        <rect width={24} height={24} fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const HourGlassIcon = (props: any) => (
  <svg
    width={16}
    height={17}
    viewBox="0 0 16 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_16100_27949)">
      <path
        d="M8 8.5L4.2 5.65C4.1379 5.60343 4.0875 5.54303 4.05279 5.47361C4.01807 5.40418 4 5.32762 4 5.25V3C4 2.86739 4.05268 2.74021 4.14645 2.64645C4.24021 2.55268 4.36739 2.5 4.5 2.5H11.5C11.6326 2.5 11.7598 2.55268 11.8536 2.64645C11.9473 2.74021 12 2.86739 12 3V5.2275C11.9998 5.30459 11.9817 5.38059 11.9472 5.44955C11.9127 5.5185 11.8628 5.57855 11.8013 5.625L8 8.5Z"
        stroke="#1B2023"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 8.5L4.2 11.35C4.1379 11.3966 4.0875 11.457 4.05279 11.5264C4.01807 11.5958 4 11.6724 4 11.75V14C4 14.1326 4.05268 14.2598 4.14645 14.3536C4.24021 14.4473 4.36739 14.5 4.5 14.5H11.5C11.6326 14.5 11.7598 14.4473 11.8536 14.3536C11.9473 14.2598 12 14.1326 12 14V11.7725C12 11.6952 11.982 11.619 11.9475 11.5498C11.913 11.4806 11.8629 11.4203 11.8013 11.3737L8 8.5Z"
        stroke="#1B2023"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_16100_27949">
        <rect
          width={16}
          height={16}
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);
export default HourGlassIcon;

export const FailReason = () => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_16609_19408)">
      <path
        d="M9.375 9.875C9.54076 9.875 9.69973 9.94085 9.81694 10.0581C9.93415 10.1753 10 10.3342 10 10.5V13.625C10 13.7908 10.0658 13.9497 10.1831 14.0669C10.3003 14.1842 10.4592 14.25 10.625 14.25"
        stroke="#EA3D5C"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.6875 7.84375C10.119 7.84375 10.4688 7.49397 10.4688 7.0625C10.4688 6.63103 10.119 6.28125 9.6875 6.28125C9.25603 6.28125 8.90625 6.63103 8.90625 7.0625C8.90625 7.49397 9.25603 7.84375 9.6875 7.84375Z"
        fill="#EA3D5C"
      />
      <path
        d="M10 18C14.1421 18 17.5 14.6421 17.5 10.5C17.5 6.35786 14.1421 3 10 3C5.85786 3 2.5 6.35786 2.5 10.5C2.5 14.6421 5.85786 18 10 18Z"
        stroke="#EA3D5C"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_16609_19408">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const MasterCardIcon = () => (
  <svg
    width={26}
    height={17}
    viewBox="0 0 26 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    // {...props}
  >
    <path
      d="M16.5149 1.86328H9.48438V14.5981H16.5149V1.86328Z"
      fill="#FF5F00"
    />
    <path
      d="M9.93135 8.22986C9.93024 7.0034 10.206 5.79276 10.7376 4.68959C11.2693 3.58642 12.043 2.61964 13.0002 1.86243C11.8149 0.923313 10.3914 0.339291 8.89233 0.177115C7.3933 0.0149386 5.87925 0.281146 4.52322 0.945322C3.16719 1.6095 2.0239 2.64484 1.22404 3.93301C0.424169 5.22117 0 6.71019 0 8.22986C0 9.74953 0.424169 11.2385 1.22404 12.5267C2.0239 13.8149 3.16719 14.8502 4.52322 15.5144C5.87925 16.1786 7.3933 16.4448 8.89233 16.2826C10.3914 16.1204 11.8149 15.5364 13.0002 14.5973C12.043 13.8401 11.2693 12.8733 10.7377 11.7701C10.206 10.667 9.93025 9.45632 9.93135 8.22986Z"
      fill="#EB001B"
    />
    <path
      d="M25.9998 8.22986C25.9999 9.74951 25.5757 11.2385 24.7759 12.5267C23.9761 13.8148 22.8328 14.8502 21.4768 15.5144C20.1208 16.1786 18.6068 16.4448 17.1078 16.2826C15.6088 16.1204 14.1853 15.5364 13 14.5973C13.9563 13.8393 14.7294 12.8724 15.261 11.7694C15.7926 10.6664 16.0688 9.45612 16.0688 8.22986C16.0688 7.00359 15.7926 5.79333 15.261 4.69034C14.7294 3.58734 13.9563 2.6204 13 1.86243C14.1853 0.92331 15.6088 0.339287 17.1078 0.177113C18.6068 0.0149386 20.1208 0.281157 21.4768 0.94534C22.8328 1.60952 23.9761 2.64487 24.7759 3.93303C25.5757 5.2212 25.9999 6.71021 25.9998 8.22986Z"
      fill="#F79E1B"
    />
    <path
      d="M25.2336 13.2479V12.9872H25.3379V12.9341H25.0723V12.9872H25.1766V13.2479H25.2336ZM25.7493 13.2479V12.9336H25.6679L25.5742 13.1498L25.4805 12.9336H25.3991V13.2479H25.4566V13.0108L25.5444 13.2153H25.604L25.6918 13.0103V13.2479H25.7493Z"
      fill="#F79E1B"
    />
  </svg>
);

export const VisaCardIcon = (props: any) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    enableBackground="new 0 0 750 471"
    width={32}
    height={20}
    viewBox="0 0 750 471"
    {...props}
  >
    <g fill="#0e4595">
      <path d="m278.198 334.228 33.36-195.763h53.358l-33.384 195.763z" />
      <path d="m524.307 142.687c-10.57-3.966-27.135-8.222-47.822-8.222-52.725 0-89.863 26.551-90.18 64.604-.297 28.129 26.514 43.821 46.754 53.185 20.77 9.597 27.752 15.716 27.652 24.283-.133 13.123-16.586 19.116-31.924 19.116-21.355 0-32.701-2.967-50.225-10.274l-6.877-3.112-7.488 43.823c12.463 5.466 35.508 10.199 59.438 10.445 56.09 0 92.502-26.248 92.916-66.884.199-22.27-14.016-39.216-44.801-53.188-18.65-9.056-30.072-15.099-29.951-24.269 0-8.137 9.668-16.838 30.559-16.838 17.447-.271 30.088 3.534 39.936 7.5l4.781 2.259z" />
      <path d="m661.615 138.464h-41.23c-12.773 0-22.332 3.486-27.941 16.234l-79.244 179.402h56.031s9.16-24.121 11.232-29.418c6.123 0 60.555.084 68.336.084 1.596 6.854 6.492 29.334 6.492 29.334h49.512zm-65.417 126.408c4.414-11.279 21.26-54.724 21.26-54.724-.314.521 4.381-11.334 7.074-18.684l3.607 16.878s10.217 46.729 12.352 56.527h-44.293z" />
      <path d="m45.878906 138.46484-.68164 4.07227c21.092962 5.106 39.932007 12.49619 56.425784 21.68945l47.3457 169.6875 56.45508-.0625 84.0039-195.38672h-56.52539l-52.23828 133.4961-5.56445-27.13086c-.26068-.83823-.54407-1.67793-.83399-2.51953l-18.16015-87.31836c-3.229-12.396-12.59655-16.09535-24.18555-16.52735z" />
    </g>
  </svg>
);

export const AmericanExpressCardIcon = (props: any) => (
  <svg
    width={37}
    height={22}
    viewBox="0 0 37 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect width={37} height={22} rx={2} fill="#0070D1" />
    <path
      d="M3.32281 7.88832L2.61366 6.11758L1.90857 7.88832H3.32281ZM18.9452 7.18321C18.8028 7.27177 18.6344 7.27472 18.4327 7.27472H17.174V6.28813H18.4498C18.6304 6.28813 18.8188 6.29643 18.9411 6.36824C19.0755 6.43295 19.1587 6.57068 19.1587 6.76092C19.1587 6.95505 19.0796 7.11127 18.9452 7.18321ZM27.9243 7.88832L27.2073 6.11758L26.4942 7.88832H27.9243ZM11.1867 9.80496H10.1245L10.1206 6.32618L8.61825 9.80496H7.70855L6.20227 6.3231V9.80496H4.09497L3.69686 8.81422H1.53962L1.13746 9.80496H0.012159L1.86752 5.36317H3.40688L5.16902 9.56862V5.36317H6.86004L8.21596 8.37639L9.46154 5.36317H11.1866L11.1867 9.80496ZM15.42 9.80496H11.9588V5.36317H15.42V6.28813H12.995V7.08876H15.3618V7.99925H12.995V8.88629H15.42V9.80496ZM20.3001 6.55943C20.3001 7.26761 19.8388 7.6335 19.57 7.74336C19.7967 7.83178 19.9903 7.98799 20.0825 8.11741C20.2288 8.33834 20.254 8.53568 20.254 8.93238V9.80496H19.209L19.2051 9.24481C19.2051 8.97753 19.2301 8.59316 19.0415 8.37947C18.8901 8.22325 18.6594 8.18936 18.2864 8.18936H17.1741V9.80496H16.1381V5.36317H18.5212C19.0507 5.36317 19.4408 5.3775 19.7758 5.57592C20.1036 5.77434 20.3001 6.06399 20.3001 6.55943ZM21.9581 9.80496H20.9009V5.36317H21.9581V9.80496ZM34.2228 9.80496H32.7545L30.7907 6.47114V9.80496H28.6806L28.2774 8.81422H26.1251L25.734 9.80496H24.5216C24.018 9.80496 23.3803 9.69081 23.0192 9.31367C22.6551 8.93653 22.4657 8.42569 22.4657 7.61796C22.4657 6.95921 22.5789 6.35699 23.0242 5.88111C23.3592 5.52662 23.8837 5.36317 24.5977 5.36317H25.6007V6.31492H24.6187C24.2406 6.31492 24.0271 6.37253 23.8215 6.57805C23.6448 6.76508 23.5236 7.11864 23.5236 7.5842C23.5236 8.06007 23.6159 8.40318 23.8085 8.62732C23.968 8.80309 24.2579 8.85642 24.5306 8.85642H24.9959L26.4562 5.3633H28.0086L29.7628 9.56461V5.3633H31.3403L33.1615 8.45677V5.3633H34.2228V9.80496ZM0 10.6774H1.77038L2.16953 9.69081H3.06316L3.46126 10.6774H6.94436V9.92312L7.25527 10.6806H9.06343L9.37434 9.91187V10.6774H18.0305L18.0265 9.05791H18.1939C18.3112 9.06207 18.3455 9.07319 18.3455 9.2716V10.6774H22.8225V10.3004C23.1836 10.4987 23.7452 10.6774 24.4843 10.6774H26.3678L26.7709 9.69081H27.6645L28.0587 10.6774H31.6882V9.74025L32.2379 10.6774H35.1463V4.48242H32.2679V5.21406L31.8648 4.48242H28.9113V5.21406L28.5411 4.48242H24.5515C23.8837 4.48242 23.2967 4.57795 22.8225 4.84415V4.48242H20.0693V4.84415C19.7675 4.56977 19.3564 4.48242 18.8992 4.48242H8.84078L8.16589 6.08248L7.47282 4.48242H4.30468V5.21406L3.95665 4.48242H1.25473L0 7.42785V10.6774Z"
      fill="white"
    />
    <path
      d="M36.8618 13.9543H34.9735C34.785 13.9543 34.6597 13.9615 34.5542 14.0345C34.4449 14.1065 34.4028 14.2133 34.4028 14.3542C34.4028 14.5218 34.4951 14.6358 34.6294 14.6851C34.7387 14.7241 34.8561 14.7355 35.0287 14.7355L35.5902 14.7509C36.1569 14.7652 36.5351 14.865 36.7657 15.1085C36.8077 15.1424 36.8329 15.1804 36.8618 15.2185V13.9543ZM36.8618 16.8834C36.6102 17.2605 36.1197 17.4517 35.4558 17.4517H33.455V16.499H35.4477C35.6454 16.499 35.7837 16.4723 35.867 16.389C35.9392 16.3203 35.9895 16.2205 35.9895 16.0992C35.9895 15.9698 35.9392 15.867 35.863 15.8054C35.7878 15.7376 35.6784 15.7068 35.4979 15.7068C34.5251 15.6729 33.3114 15.7376 33.3114 14.3317C33.3114 13.6873 33.7107 13.009 34.7979 13.009H36.8618V12.125H34.9442C34.3656 12.125 33.9452 12.267 33.6475 12.4878V12.125H30.8113C30.3578 12.125 29.8254 12.2402 29.5736 12.4878V12.125H24.5089V12.4878C24.1059 12.1898 23.4257 12.125 23.1118 12.125H19.7711V12.4878C19.4522 12.1714 18.7431 12.125 18.3108 12.125H14.572L13.7164 13.0738L12.9151 12.125H7.33008V18.3243H12.81L13.6916 17.3605L14.5221 18.3243L17.8999 18.3273V16.869H18.232C18.6802 16.8761 19.2088 16.8576 19.6751 16.6511V18.3241H22.4612V16.7084H22.5957C22.7672 16.7084 22.784 16.7156 22.784 16.8913V18.324H31.2478C31.7851 18.324 32.3468 18.1831 32.6578 17.9273V18.324H35.3425C35.9011 18.324 36.4467 18.2437 36.8618 18.0382V16.8834ZM32.7289 15.1085C32.9307 15.3224 33.0388 15.5925 33.0388 16.0498C33.0388 17.0056 32.4561 17.4517 31.4112 17.4517H29.3932V16.499H31.4031C31.5996 16.499 31.739 16.4723 31.8263 16.389C31.8976 16.3203 31.9487 16.2205 31.9487 16.0992C31.9487 15.9698 31.8934 15.867 31.8222 15.8054C31.743 15.7376 31.6337 15.7068 31.4533 15.7068C30.4844 15.6729 29.2709 15.7376 29.2709 14.3317C29.2709 13.6873 29.666 13.009 30.7523 13.009H32.8294V13.9546H30.9288C30.7404 13.9546 30.6179 13.9618 30.5137 14.0348C30.4002 14.1067 30.3581 14.2135 30.3581 14.3545C30.3581 14.5221 30.4544 14.6361 30.5848 14.6854C30.6941 14.7244 30.8115 14.7358 30.988 14.7358L31.5457 14.7512C32.1082 14.7652 32.4943 14.8649 32.7289 15.1085ZM23.3798 14.8341C23.2413 14.9184 23.0699 14.9256 22.8682 14.9256H21.6096V13.9278H22.8854C23.0699 13.9278 23.2545 13.9318 23.3798 14.008C23.5141 14.08 23.5944 14.2175 23.5944 14.4077C23.5944 14.5978 23.5141 14.7509 23.3798 14.8341ZM24.0056 15.387C24.2362 15.4742 24.4247 15.6306 24.5131 15.76C24.6594 15.9769 24.6806 16.1793 24.6848 16.5709V17.4517H23.6446V16.8958C23.6446 16.6285 23.6697 16.2328 23.4771 16.0262C23.3257 15.867 23.095 15.829 22.717 15.829H21.6097V17.4517H20.5686V13.0088H22.9607C23.4852 13.0088 23.8672 13.0325 24.2073 13.2185C24.5343 13.4209 24.74 13.6983 24.74 14.2051C24.7398 14.9142 24.2783 15.2761 24.0056 15.387ZM25.3144 13.0088H28.7724V13.9276H26.3462V14.7354H28.7132V15.6418H26.3462V16.5258L28.7724 16.5298V17.4517H25.3144V13.0088ZM18.324 15.0592H16.9851V13.9278H18.3361C18.7101 13.9278 18.9698 14.084 18.9698 14.4725C18.9698 14.8567 18.7222 15.0592 18.324 15.0592ZM15.9532 17.0476L14.3624 15.2379L15.9532 13.4856V17.0476ZM11.8451 16.5258H9.29775V15.6418H11.5724V14.7354H9.29775V13.9276H11.8953L13.0286 15.2224L11.8451 16.5258ZM20.0821 14.4725C20.0821 15.7067 19.1835 15.9615 18.2779 15.9615H16.9851V17.4517H14.9713L13.6955 15.9809L12.3697 17.4517H8.26567V13.0088H12.4328L13.7075 14.4651L15.0254 13.0088H18.3361C19.1583 13.0088 20.0821 13.2421 20.0821 14.4725Z"
      fill="white"
    />
  </svg>
);
export const ClockCounterClockwise = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_18535_18893)">
      <path
        d="M12 7.5V12L15.75 14.25"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.75 9.75H3V6"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.3375 18C7.51685 19.1128 8.99798 19.8535 10.5958 20.1294C12.1937 20.4052 13.8374 20.2041 15.3217 19.5512C16.8059 18.8982 18.0648 17.8224 18.9411 16.458C19.8173 15.0937 20.2721 13.5014 20.2486 11.88C20.2251 10.2587 19.7244 8.68026 18.8089 7.3419C17.8934 6.00354 16.6039 4.96462 15.1014 4.35497C13.5988 3.74531 11.95 3.59194 10.3608 3.91403C8.77157 4.23612 7.31253 5.01937 6.16594 6.16593C5.0625 7.28343 4.15125 8.33718 3 9.75"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_18535_18893">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Globe = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_19180_45059)">
      <path
        d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.11328 11.5556L8.36203 9.375C8.42795 9.26082 8.5228 9.16604 8.63703 9.1002C8.75125 9.03437 8.88082 8.99981 9.01266 9H10.5792C10.7059 9.00021 10.8305 8.96793 10.9411 8.90625L12.0933 8.2725C12.144 8.24523 12.1909 8.21151 12.233 8.17219L14.7567 5.89125C14.8823 5.77704 14.9654 5.62362 14.9925 5.45605C15.0196 5.28849 14.989 5.11669 14.9058 4.96875L13.9214 3.20531"
        stroke="#1A1A1A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.3548 4.99031L5.25042 7.60125C5.17583 7.778 5.1718 7.97662 5.23917 8.15625L6.3173 11.0316C6.3616 11.149 6.43468 11.2535 6.52985 11.3354C6.62501 11.4173 6.73922 11.474 6.86199 11.5003L8.87105 11.9325C8.98248 11.9564 9.087 12.0054 9.17667 12.0757C9.26634 12.1461 9.3388 12.2359 9.38855 12.3384L9.7448 13.0763C9.80625 13.203 9.90216 13.3099 10.0215 13.3847C10.1409 13.4594 10.2789 13.4991 10.4198 13.4991H11.7135"
        stroke="#1B2023"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.9773 16.17L15.0667 13.1494C14.9774 13.0949 14.8778 13.0595 14.7742 13.0453L12.6348 12.7566C12.4741 12.7348 12.3107 12.7657 12.1691 12.8446C12.0275 12.9236 11.9152 13.0463 11.8492 13.1944L10.5648 16.0762C10.5051 16.2104 10.4861 16.359 10.5101 16.5038C10.5341 16.6487 10.6001 16.7833 10.6998 16.8909L12.5504 18.7969C12.6307 18.8831 12.6894 18.9871 12.722 19.1003C12.7546 19.2135 12.76 19.3327 12.7379 19.4484L12.4408 20.9897"
        stroke="#1B2023"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_19180_45059">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const NewTab = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_19180_45231)">
      <path
        d="M20.25 9.75L20.2491 3.75094L14.25 3.75"
        stroke="#646464"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.75 11.25L20.25 3.75"
        stroke="#646464"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.25 12.75V19.5C17.25 19.6989 17.171 19.8897 17.0303 20.0303C16.8897 20.171 16.6989 20.25 16.5 20.25H4.5C4.30109 20.25 4.11032 20.171 3.96967 20.0303C3.82902 19.8897 3.75 19.6989 3.75 19.5V7.5C3.75 7.30109 3.82902 7.11032 3.96967 6.96967C4.11032 6.82902 4.30109 6.75 4.5 6.75H11.25"
        stroke="#646464"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_19180_45231">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Door = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_19697_2131)">
      <path
        d="M2.25 21H21.75"
        stroke="#1B2023"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.25 21V3.75C5.25 3.55109 5.32902 3.36032 5.46967 3.21967C5.61032 3.07902 5.80109 3 6 3H18C18.1989 3 18.3897 3.07902 18.5303 3.21967C18.671 3.36032 18.75 3.55109 18.75 3.75V21"
        stroke="#1B2023"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 3C15.1989 3 15.3897 3.07902 15.5303 3.21967C15.671 3.36032 15.75 3.55109 15.75 3.75V21"
        stroke="#1B2023"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.375 13.5C12.9963 13.5 13.5 12.9963 13.5 12.375C13.5 11.7537 12.9963 11.25 12.375 11.25C11.7537 11.25 11.25 11.7537 11.25 12.375C11.25 12.9963 11.7537 13.5 12.375 13.5Z"
        fill="#1B2023"
      />
    </g>
    <defs>
      <clipPath id="clip0_19697_2131">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Sync = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.0127 6.23144H14.0127V3.23145"
      stroke="#1A1A1A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.1123 4.1124C4.62255 3.60141 5.22853 3.19603 5.89559 2.91944C6.56264 2.64285 7.27768 2.50049 7.9998 2.50049C8.72193 2.50049 9.43697 2.64285 10.104 2.91944C10.7711 3.19603 11.3771 3.60141 11.8873 4.1124L14.0123 6.23115"
      stroke="#1A1A1A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.98731 9.76855H1.9873V12.7686"
      stroke="#1A1A1A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.8873 11.8873C11.3771 12.3983 10.7711 12.8037 10.104 13.0803C9.43697 13.3569 8.72193 13.4992 7.9998 13.4992C7.27768 13.4992 6.56264 13.3569 5.89559 13.0803C5.22853 12.8037 4.62255 12.3983 4.1123 11.8873L1.9873 9.76855"
      stroke="#1A1A1A"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const GearIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_17754_30481)">
      <path
        d="M12 15.75C14.0711 15.75 15.75 14.0711 15.75 12C15.75 9.92893 14.0711 8.25 12 8.25C9.92893 8.25 8.25 9.92893 8.25 12C8.25 14.0711 9.92893 15.75 12 15.75Z"
        stroke="black"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.1925 19.3228C12.0669 19.3228 11.9403 19.3228 11.8175 19.3228L8.81283 21C7.64313 20.6065 6.55821 19.9959 5.61502 19.2L5.60377 15.825C5.53721 15.72 5.47439 15.6141 5.41627 15.5053L2.42846 13.8038C2.19312 12.6134 2.19312 11.3885 2.42846 10.1981L5.41346 8.50125C5.47439 8.39344 5.53721 8.28656 5.60096 8.18156L5.61596 4.80656C6.5583 4.00842 7.64294 3.39548 8.81283 3L11.8128 4.67719C11.9385 4.67719 12.065 4.67719 12.1878 4.67719L15.1878 3C16.3575 3.39346 17.4425 4.00414 18.3856 4.8L18.3969 8.175C18.4635 8.28 18.5263 8.38594 18.5844 8.49469L21.5703 10.1953C21.8057 11.3857 21.8057 12.6106 21.5703 13.8009L18.5853 15.4978C18.5244 15.6056 18.4616 15.7125 18.3978 15.8175L18.3828 19.1925C17.4411 19.9908 16.3571 20.604 15.1878 21L12.1925 19.3228Z"
        stroke="black"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_17754_30481">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PlugsConnectedIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_17587_18777)">
      <path
        d="M12.0903 6.65893L6.65837 12.0908C5.77969 12.9695 5.77969 14.3941 6.65837 15.2728L8.72666 17.3411C9.60534 18.2198 11.03 18.2198 11.9086 17.3411L17.3405 11.9092C18.2192 11.0305 18.2192 9.60589 17.3405 8.72721L15.2723 6.65893C14.3936 5.78025 12.969 5.78025 12.0903 6.65893Z"
        stroke="black"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M8.25 8.25L15.75 15.75"
        stroke="black"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M21.7498 2.25L16.3066 7.69312"
        stroke="black"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7.69312 16.3069L2.25 21.75"
        stroke="black"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M9 3L9.75 4.875"
        stroke="black"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M3 9L4.875 9.75"
        stroke="black"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M19.125 14.25L21 15"
        stroke="black"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14.25 19.125L15 21"
        stroke="black"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_17587_18777">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const TrashIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_16432_44976)">
      <path
        d="M20.25 5.25H3.75"
        stroke="#1B2023"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M9.75 9.75V15.75"
        stroke="#1B2023"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M14.25 9.75V15.75"
        stroke="#1B2023"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M18.75 5.25V19.5C18.75 19.6989 18.671 19.8897 18.5303 20.0303C18.3897 20.171 18.1989 20.25 18 20.25H6C5.80109 20.25 5.61032 20.171 5.46967 20.0303C5.32902 19.8897 5.25 19.6989 5.25 19.5V5.25"
        stroke="#1B2023"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15.75 5.25V3.75C15.75 3.35218 15.592 2.97064 15.3107 2.68934C15.0294 2.40804 14.6478 2.25 14.25 2.25H9.75C9.35218 2.25 8.97064 2.40804 8.68934 2.68934C8.40804 2.97064 8.25 3.35218 8.25 3.75V5.25"
        stroke="#1B2023"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_16432_44976">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PencilSimpleLine = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_16465_45942)">
      <path
        d="M9 20.2501H4.5C4.30109 20.2501 4.11032 20.1711 3.96967 20.0305C3.82902 19.8898 3.75 19.699 3.75 19.5001V15.3104C3.75009 15.1118 3.82899 14.9213 3.96938 14.7807L15.5306 3.2195C15.6713 3.07895 15.862 3 16.0608 3C16.2596 3 16.4503 3.07895 16.5909 3.2195L20.7806 7.40637C20.9212 7.54701 21.0001 7.7377 21.0001 7.93653C21.0001 8.13535 20.9212 8.32605 20.7806 8.46668L9 20.2501Z"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M20.25 20.25H9"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12.75 6L18 11.25"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_16465_45942">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PaperPlaneTilt = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_16465_45930)">
      <path
        d="M20.9706 3.9544C21.0068 3.82611 21.0081 3.6905 20.9744 3.56153C20.9408 3.43256 20.8733 3.3149 20.7791 3.22065C20.6848 3.1264 20.5672 3.05897 20.4382 3.0253C20.3092 2.99163 20.1736 2.99294 20.0453 3.02909L2.04531 8.48722C1.89836 8.52869 1.76756 8.61403 1.67041 8.73183C1.57326 8.84964 1.51438 8.99429 1.50164 9.14645C1.4889 9.29861 1.52291 9.45104 1.59912 9.58335C1.67533 9.71567 1.79011 9.82157 1.92813 9.8869L9.955 13.6875C10.1113 13.7616 10.2371 13.8875 10.3113 14.0438L14.1128 22.0697C14.1781 22.2077 14.284 22.3225 14.4164 22.3987C14.5487 22.4749 14.7011 22.5089 14.8533 22.4962C15.0054 22.4835 15.1501 22.4246 15.2679 22.3274C15.3857 22.2303 15.471 22.0995 15.5125 21.9525L20.9706 3.9544Z"
        stroke="#1B2023"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10.1641 13.8356L14.9997 9"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_16465_45930">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const UsersThree = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_17434_77808)">
      <path
        d="M18 11.25C18.8734 11.2493 19.7349 11.4524 20.516 11.843C21.2972 12.2335 21.9765 12.8009 22.5 13.5"
        stroke="#1B2023"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M1.5 13.5C2.0235 12.8009 2.70281 12.2335 3.48398 11.843C4.26514 11.4524 5.12663 11.2493 6 11.25"
        stroke="#1B2023"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M12 17.25C14.0711 17.25 15.75 15.5711 15.75 13.5C15.75 11.4289 14.0711 9.75 12 9.75C9.92893 9.75 8.25 11.4289 8.25 13.5C8.25 15.5711 9.92893 17.25 12 17.25Z"
        stroke="#1B2023"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.75 20.25C7.2884 19.3364 8.0559 18.579 8.97665 18.0529C9.8974 17.5267 10.9395 17.25 12 17.25C13.0605 17.25 14.1026 17.5267 15.0233 18.0529C15.9441 18.579 16.7116 19.3364 17.25 20.25"
        stroke="#1B2023"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M15.0938 7.5C15.234 6.95678 15.5238 6.46377 15.9302 6.07697C16.3366 5.69017 16.8433 5.42508 17.3927 5.31179C17.9422 5.19851 18.5125 5.24158 19.0387 5.43611C19.5649 5.63064 20.0261 5.96883 20.3697 6.41229C20.7134 6.85574 20.9258 7.38668 20.9829 7.9448C21.04 8.50293 20.9394 9.06587 20.6926 9.56971C20.4458 10.0735 20.0627 10.4981 19.5867 10.7951C19.1108 11.0921 18.561 11.2497 18 11.25"
        stroke="#1B2023"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.00044 11.25C5.43941 11.2497 4.88969 11.0921 4.41373 10.7951C3.93776 10.4981 3.55462 10.0735 3.30782 9.56971C3.06102 9.06587 2.96044 8.50293 3.01752 7.9448C3.0746 7.38668 3.28703 6.85574 3.63071 6.41229C3.97438 5.96883 4.43552 5.63064 4.96176 5.43611C5.48799 5.24158 6.05822 5.19851 6.60771 5.31179C7.15719 5.42508 7.66389 5.69017 8.07027 6.07697C8.47666 6.46377 8.76643 6.95678 8.90669 7.5"
        stroke="#1B2023"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_17434_77808">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const WrenchIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_17434_76848)">
      <path
        d="M9.75 11.9009C9.15044 10.8159 8.90167 9.57183 9.0379 8.33969C9.17412 7.10755 9.68865 5.94785 10.5107 5.02001C11.3328 4.09217 12.4221 3.44175 13.6289 3.15815C14.8357 2.87455 16.1007 2.9717 17.25 3.43624L13.5 7.50031L14.0306 9.96968L16.5 10.5003L20.5641 6.75031C21.0286 7.89962 21.1258 9.16461 20.8422 10.3714C20.5586 11.5782 19.9081 12.6675 18.9803 13.4896C18.0525 14.3117 16.8928 14.8262 15.6606 14.9624C14.4285 15.0986 13.1844 14.8499 12.0994 14.2503L6.84375 20.3441C6.42106 20.7667 5.84777 21.0042 5.25 21.0042C4.65222 21.0042 4.07894 20.7667 3.65625 20.3441C3.23356 19.9214 2.99609 19.3481 2.99609 18.7503C2.99609 18.1525 3.23356 17.5792 3.65625 17.1566L9.75 11.9009Z"
        stroke="#1B2023"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_17434_76848">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const PencilWriting = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_15428_43439)">
      <path
        d="M9 20.2501H4.5C4.30109 20.2501 4.11032 20.1711 3.96967 20.0305C3.82902 19.8898 3.75 19.699 3.75 19.5001V15.3104C3.75009 15.1118 3.82899 14.9213 3.96938 14.7807L15.5306 3.2195C15.6713 3.07895 15.862 3 16.0608 3C16.2596 3 16.4503 3.07895 16.5909 3.2195L20.7806 7.40637C20.9212 7.54701 21.0001 7.7377 21.0001 7.93653C21.0001 8.13535 20.9212 8.32605 20.7806 8.46668L9 20.2501Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.25 20.25H9"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.75 6L18 11.25"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_15428_43439">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Master = ({ color = "currentColor" }: any) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="33"
    height="22"
    viewBox="0 0 33 22"
    fill="none"
  >
    <path d="M20.848 3.18814H12.1523V19.3962H20.848V3.18814Z" fill="#F37121" />
    <path
      d="M12.7054 11.2931C12.704 9.73216 13.045 8.19135 13.7026 6.78731C14.3603 5.38328 15.3172 4.15283 16.5011 3.18911C15.035 1.99387 13.2744 1.25057 11.4203 1.04416C9.56622 0.837755 7.69357 1.17656 6.01638 2.02188C4.33919 2.86719 2.92512 4.1849 1.93581 5.82439C0.946505 7.46387 0.421875 9.35898 0.421875 11.2931C0.421875 13.2272 0.946505 15.1223 1.93581 16.7618C2.92512 18.4013 4.33919 19.719 6.01638 20.5643C7.69357 21.4097 9.56622 21.7485 11.4203 21.5421C13.2744 21.3357 15.035 20.5924 16.5011 19.3971C15.3172 18.4334 14.3603 17.2029 13.7027 15.7989C13.0451 14.3949 12.704 12.8541 12.7054 11.2931Z"
      fill="#EB001B"
    />
    <path
      d="M32.5807 11.2931C32.5807 13.2272 32.0561 15.1223 31.0669 16.7618C30.0776 18.4013 28.6636 19.719 26.9865 20.5643C25.3093 21.4096 23.4367 21.7485 21.5826 21.5421C19.7286 21.3357 17.968 20.5924 16.502 19.3971C17.6848 18.4324 18.641 17.2018 19.2985 15.798C19.956 14.3941 20.2976 12.8538 20.2976 11.2931C20.2976 9.73241 19.956 8.19207 19.2985 6.78826C18.641 5.38445 17.6848 4.1538 16.502 3.1891C17.968 1.99386 19.7286 1.25056 21.5826 1.04416C23.4367 0.837755 25.3093 1.17658 26.9865 2.0219C28.6636 2.86723 30.0776 4.18494 31.0669 5.82442C32.0561 7.4639 32.5807 9.359 32.5807 11.2931Z"
      fill="#F79E1B"
    />
    <path
      d="M31.6312 17.6795V17.3476H31.7602V17.28H31.4316V17.3476H31.5607V17.6795H31.6312ZM32.269 17.6795V17.2794H32.1683L32.0525 17.5546L31.9366 17.2794H31.8358V17.6795H31.9069V17.3777L32.0156 17.6379H32.0893L32.1979 17.377V17.6795H32.269Z"
      fill="#F79E1B"
    />
  </svg>
);
export const Visa = () => (
  <svg
    width="38"
    height="22"
    viewBox="0 0 38 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.3831 6.42334L13.5586 15.819H16.4766L18.3026 6.42334H15.3831Z"
      fill="#1431C2"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.0181 6.42532L8.1521 12.8419L7.84654 11.8731C7.28133 10.7057 5.67676 9.02915 3.79297 7.97258L6.41357 15.819L9.50982 15.8144L14.1181 6.42334L11.0181 6.42532Z"
      fill="#1431C2"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.73265 7.19152C6.566 6.61555 6.08304 6.44389 5.48357 6.42334H1.03882L1.00195 6.61157C4.46087 7.36583 6.7496 9.18321 7.69931 11.3684L6.73265 7.19152Z"
      fill="#1431C2"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M24.1475 8.17782C25.0906 8.16439 25.7743 8.35111 26.3052 8.54455L26.5654 8.65537L26.9554 6.57859C26.3845 6.38448 25.4896 6.17627 24.3729 6.17627C21.5242 6.17627 19.5163 7.47593 19.5007 9.33845C19.4821 10.7147 20.931 11.4831 22.0254 11.9418C23.1487 12.412 23.5254 12.7109 23.5202 13.1307C23.5113 13.7721 22.6245 14.0663 21.7963 14.0663C20.6418 14.0663 20.0286 13.9219 19.0818 13.5645L18.7103 13.4114L18.3047 15.556C18.9794 15.824 20.2244 16.0544 21.5167 16.0665C24.5471 16.0665 26.5165 14.7823 26.5373 12.7921C26.5499 11.7034 25.781 10.8725 24.1149 10.1908C23.1065 9.74615 22.4896 9.45062 22.4955 9.00195C22.4955 8.60365 23.019 8.17782 24.1475 8.17782Z"
      fill="#1431C2"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M32.329 6.42334H34.6109L37.0006 15.819H34.2607C34.2607 15.819 33.9888 14.739 33.9007 14.4108C33.6735 14.4108 32.7225 14.4097 31.8272 14.4087C31.0308 14.4077 30.2784 14.4069 30.1186 14.4069C30.0039 14.6606 29.498 15.819 29.498 15.819H26.3965L30.7817 7.20357C31.0932 6.59072 31.6203 6.42334 32.329 6.42334ZM32.1455 9.85522C32.1455 9.85522 31.2119 11.9429 30.9688 12.4826H33.4224L32.738 9.76824L32.5389 8.95769C32.4643 9.13079 32.3647 9.35545 32.2843 9.53681C32.2003 9.7262 32.1373 9.86835 32.1455 9.85522Z"
      fill="#1431C2"
    />
  </svg>
);

export const Amex = () => (
  <svg
    width="37"
    height="22"
    viewBox="0 0 37 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="37" height="22" rx="2" fill="#0070D1" />
    <path
      d="M3.32281 7.88881L2.61366 6.11807L1.90857 7.88881H3.32281ZM18.9452 7.1837C18.8028 7.27226 18.6344 7.2752 18.4327 7.2752H17.174V6.28862H18.4498C18.6304 6.28862 18.8188 6.29692 18.9411 6.36873C19.0755 6.43344 19.1587 6.57117 19.1587 6.76141C19.1587 6.95554 19.0796 7.11176 18.9452 7.1837ZM27.9243 7.88881L27.2073 6.11807L26.4942 7.88881H27.9243ZM11.1867 9.80544H10.1245L10.1206 6.32667L8.61825 9.80544H7.70855L6.20227 6.32358V9.80544H4.09497L3.69686 8.8147H1.53962L1.13746 9.80544H0.012159L1.86752 5.36366H3.40688L5.16902 9.56911V5.36366H6.86004L8.21596 8.37687L9.46154 5.36366H11.1866L11.1867 9.80544ZM15.42 9.80544H11.9588V5.36366H15.42V6.28862H12.995V7.08925H15.3618V7.99974H12.995V8.88678H15.42V9.80544ZM20.3001 6.55991C20.3001 7.2681 19.8388 7.63399 19.57 7.74385C19.7967 7.83227 19.9903 7.98848 20.0825 8.1179C20.2288 8.33883 20.254 8.53617 20.254 8.93287V9.80544H19.209L19.2051 9.2453C19.2051 8.97802 19.2301 8.59365 19.0415 8.37996C18.8901 8.22374 18.6594 8.18985 18.2864 8.18985H17.1741V9.80544H16.1381V5.36366H18.5212C19.0507 5.36366 19.4408 5.37799 19.7758 5.57641C20.1036 5.77482 20.3001 6.06448 20.3001 6.55991ZM21.9581 9.80544H20.9009V5.36366H21.9581V9.80544ZM34.2228 9.80544H32.7545L30.7907 6.47163V9.80544H28.6806L28.2774 8.8147H26.1251L25.734 9.80544H24.5216C24.018 9.80544 23.3803 9.6913 23.0192 9.31416C22.6551 8.93702 22.4657 8.42618 22.4657 7.61845C22.4657 6.95969 22.5789 6.35748 23.0242 5.8816C23.3592 5.52711 23.8837 5.36366 24.5977 5.36366H25.6007V6.31541H24.6187C24.2406 6.31541 24.0271 6.37302 23.8215 6.57854C23.6448 6.76557 23.5236 7.11912 23.5236 7.58468C23.5236 8.06056 23.6159 8.40367 23.8085 8.62781C23.968 8.80358 24.2579 8.8569 24.5306 8.8569H24.9959L26.4562 5.36379H28.0086L29.7628 9.56509V5.36379H31.3403L33.1615 8.45726V5.36379H34.2228V9.80544ZM0 10.6779H1.77038L2.16953 9.6913H3.06316L3.46126 10.6779H6.94436V9.92361L7.25527 10.6811H9.06343L9.37434 9.91236V10.6779H18.0305L18.0265 9.0584H18.1939C18.3112 9.06256 18.3455 9.07368 18.3455 9.27209V10.6779H22.8225V10.3009C23.1836 10.4992 23.7452 10.6779 24.4843 10.6779H26.3678L26.7709 9.6913H27.6645L28.0587 10.6779H31.6882V9.74073L32.2379 10.6779H35.1463V4.48291H32.2679V5.21454L31.8648 4.48291H28.9113V5.21454L28.5411 4.48291H24.5515C23.8837 4.48291 23.2967 4.57843 22.8225 4.84464V4.48291H20.0693V4.84464C19.7675 4.57026 19.3564 4.48291 18.8992 4.48291H8.84078L8.16589 6.08297L7.47282 4.48291H4.30468V5.21454L3.95665 4.48291H1.25473L0 7.42834V10.6779Z"
      fill="white"
    />
    <path
      d="M36.8618 13.9543H34.9735C34.785 13.9543 34.6597 13.9615 34.5542 14.0345C34.4449 14.1065 34.4028 14.2133 34.4028 14.3542C34.4028 14.5218 34.4951 14.6358 34.6294 14.6851C34.7387 14.7241 34.8561 14.7355 35.0287 14.7355L35.5902 14.7509C36.1569 14.7652 36.5351 14.865 36.7657 15.1085C36.8077 15.1424 36.8329 15.1804 36.8618 15.2185V13.9543ZM36.8618 16.8834C36.6102 17.2605 36.1197 17.4517 35.4558 17.4517H33.455V16.499H35.4477C35.6454 16.499 35.7837 16.4723 35.867 16.389C35.9392 16.3203 35.9895 16.2205 35.9895 16.0992C35.9895 15.9698 35.9392 15.867 35.863 15.8054C35.7878 15.7376 35.6784 15.7068 35.4979 15.7068C34.5251 15.6729 33.3114 15.7376 33.3114 14.3317C33.3114 13.6873 33.7107 13.009 34.7979 13.009H36.8618V12.125H34.9442C34.3656 12.125 33.9452 12.267 33.6475 12.4878V12.125H30.8113C30.3578 12.125 29.8254 12.2402 29.5736 12.4878V12.125H24.5089V12.4878C24.1059 12.1898 23.4257 12.125 23.1118 12.125H19.7711V12.4878C19.4522 12.1714 18.7431 12.125 18.3108 12.125H14.572L13.7164 13.0738L12.9151 12.125H7.33008V18.3243H12.81L13.6916 17.3605L14.5221 18.3243L17.8999 18.3273V16.869H18.232C18.6802 16.8761 19.2088 16.8576 19.6751 16.6511V18.3241H22.4612V16.7084H22.5957C22.7672 16.7084 22.784 16.7156 22.784 16.8913V18.324H31.2478C31.7851 18.324 32.3468 18.1831 32.6578 17.9273V18.324H35.3425C35.9011 18.324 36.4467 18.2437 36.8618 18.0382V16.8834ZM32.7289 15.1085C32.9307 15.3224 33.0388 15.5925 33.0388 16.0498C33.0388 17.0056 32.4561 17.4517 31.4112 17.4517H29.3932V16.499H31.4031C31.5996 16.499 31.739 16.4723 31.8263 16.389C31.8976 16.3203 31.9487 16.2205 31.9487 16.0992C31.9487 15.9698 31.8934 15.867 31.8222 15.8054C31.743 15.7376 31.6337 15.7068 31.4533 15.7068C30.4844 15.6729 29.2709 15.7376 29.2709 14.3317C29.2709 13.6873 29.666 13.009 30.7523 13.009H32.8294V13.9546H30.9288C30.7404 13.9546 30.6179 13.9618 30.5137 14.0348C30.4002 14.1067 30.3581 14.2135 30.3581 14.3545C30.3581 14.5221 30.4544 14.6361 30.5848 14.6854C30.6941 14.7244 30.8115 14.7358 30.988 14.7358L31.5457 14.7512C32.1082 14.7652 32.4943 14.8649 32.7289 15.1085ZM23.3798 14.8341C23.2413 14.9184 23.0699 14.9256 22.8682 14.9256H21.6096V13.9278H22.8854C23.0699 13.9278 23.2545 13.9318 23.3798 14.008C23.5141 14.08 23.5944 14.2175 23.5944 14.4077C23.5944 14.5978 23.5141 14.7509 23.3798 14.8341ZM24.0056 15.387C24.2362 15.4742 24.4247 15.6306 24.5131 15.76C24.6594 15.9769 24.6806 16.1793 24.6848 16.5709V17.4517H23.6446V16.8958C23.6446 16.6285 23.6697 16.2328 23.4771 16.0262C23.3257 15.867 23.095 15.829 22.717 15.829H21.6097V17.4517H20.5686V13.0088H22.9607C23.4852 13.0088 23.8672 13.0325 24.2073 13.2185C24.5343 13.4209 24.74 13.6983 24.74 14.2051C24.7398 14.9142 24.2783 15.2761 24.0056 15.387ZM25.3144 13.0088H28.7724V13.9276H26.3462V14.7354H28.7132V15.6418H26.3462V16.5258L28.7724 16.5298V17.4517H25.3144V13.0088ZM18.324 15.0592H16.9851V13.9278H18.3361C18.7101 13.9278 18.9698 14.084 18.9698 14.4725C18.9698 14.8567 18.7222 15.0592 18.324 15.0592ZM15.9532 17.0476L14.3624 15.2379L15.9532 13.4856V17.0476ZM11.8451 16.5258H9.29775V15.6418H11.5724V14.7354H9.29775V13.9276H11.8953L13.0286 15.2224L11.8451 16.5258ZM20.0821 14.4725C20.0821 15.7067 19.1835 15.9615 18.2779 15.9615H16.9851V17.4517H14.9713L13.6955 15.9809L12.3697 17.4517H8.26567V13.0088H12.4328L13.7075 14.4651L15.0254 13.0088H18.3361C19.1583 13.0088 20.0821 13.2421 20.0821 14.4725Z"
      fill="white"
    />
  </svg>
);

export const GearSix = () => (
  <svg
    width="16"
    height="17"
    viewBox="0 0 16 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_15428_43006)">
      <path
        d="M8 11C9.38071 11 10.5 9.88071 10.5 8.5C10.5 7.11929 9.38071 6 8 6C6.61929 6 5.5 7.11929 5.5 8.5C5.5 9.88071 6.61929 11 8 11Z"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.12802 13.3819C8.04427 13.3819 7.95989 13.3819 7.87802 13.3819L5.87489 14.5C5.0951 14.2377 4.37181 13.8306 3.74302 13.3L3.73552 11.05C3.69114 10.98 3.64927 10.9094 3.61052 10.8369L1.61864 9.7025C1.46175 8.90893 1.46175 8.09232 1.61864 7.29875L3.60864 6.1675C3.64927 6.09563 3.69114 6.02437 3.73364 5.95437L3.74364 3.70438C4.37187 3.17228 5.09497 2.76365 5.87489 2.5L7.87489 3.61812C7.95864 3.61812 8.04302 3.61812 8.12489 3.61812L10.1249 2.5C10.9047 2.7623 11.628 3.16943 12.2568 3.7L12.2643 5.95C12.3086 6.02 12.3505 6.09063 12.3893 6.16313L14.3799 7.29688C14.5368 8.09044 14.5368 8.90706 14.3799 9.70063L12.3899 10.8319C12.3493 10.9037 12.3074 10.975 12.2649 11.045L12.2549 13.295C11.6271 13.8272 10.9044 14.236 10.1249 14.5L8.12802 13.3819Z"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_15428_43006">
        <rect
          width="16"
          height="16"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);
