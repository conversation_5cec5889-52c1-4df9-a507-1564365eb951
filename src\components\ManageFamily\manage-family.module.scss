@use "../../styles/theme.scss" as *;
@use "../../styles/mixins.module.scss" as *;

.container {
  padding: 32px;
  background: white;
  border-radius: 24px;
  min-height: 90vh;
}

.sectionWrapperWithAddMemberButton {
  position: relative;
}

// 🙂
.addMemberBtn {
  position: absolute;
  top: -62px;
  right: 0;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.errorMessage {
  color: $error;
  text-align: center;
  padding: 24px;
  font-size: 16px;
}

.membersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));
  gap: 24px;
}

.memberCard {
  border-radius: 24px;
  padding: 28px 24px 18px 24px;
  border: 1px solid black;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.memberHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.memberName {
  font-size: 22px;
  font-weight: 600;
  line-height: 1;
}

.memberEmail {
  font-size: 14px;
  margin-bottom: 20px;
}

.subscriptionTitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.planName {
  font-size: 14px;
  color: #000;
}

.manageBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f1f1f1;
  border-radius: 16px;
  padding: 10px 16px;
  border: none;
  cursor: pointer;
  font-weight: 600;
  color: #000;
  font-size: 15px;

  &:hover {
    background-color: #e8e8e8;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.accountBtn {
  width: 100%;
  margin-top: 32px;
}

:global {
  .manage-family-sub-dropdown .szh-menu {
    width: max-content;
    border-radius: 16px;
  }
}

.manageMenuItems {
  display: flex;
  flex-direction: column;
  gap: 4px;

  button {
    @include resetBtn;
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 12px 24px;
    color: black;

    &:hover {
      background: #f1f1f1;
      color: black;
    }
  }
}

.confirmModalContent {
  margin: auto;
  text-align: center;

  .info {
    line-height: 21px;
    margin-top: 24px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.confirmMainText {
  font-weight: 700;
  font-size: 24px;
  line-height: 36px;
}
