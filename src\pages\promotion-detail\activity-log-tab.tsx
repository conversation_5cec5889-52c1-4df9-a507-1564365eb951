import {
  forwardRef,
  useCallback,
  useEffect,
  useImperative<PERSON><PERSON>le,
  useState,
} from "react";
import { ApiGet } from "../api/api";
import { useDispatch } from "react-redux";
import styles from "../../styles/promotion-detail.module.scss";
import UserSkeleton from "../../components/UserSkeleton";
import Pagination from "../../components/Pagination";
import { formatDateWithTime } from "../../components/utils/formatDate";
import { ArrowRight } from "../../components/svgs";

type ActivityLogTabProps = {
  promoId: string;
};

const ActivityLogTab = forwardRef<{ refresh: () => void }, ActivityLogTabProps>(
  (props: ActivityLogTabProps, ref) => {
    const { promoId } = props;
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(true);

    const [currentPageItems, setCurrentPageItems] = useState<
      Array<any> | undefined
    >();
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);

    const fetchActivityLogs = useCallback(
      (pageNo: number) => {
        setLoading(true);
        ApiGet(`/promotions/${promoId}/activity-logs?page=${pageNo - 1}`)
          .then((response) => {
            setCurrentPageItems(response.data.content);
            setTotalPages(response.data.totalPages);
          })
          .catch(() => {
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: "Error fetching promotions",
              },
            });
          })
          .finally(() => {
            setLoading(false);
          });
      },
      [promoId],
    );

    useEffect(() => {
      fetchActivityLogs(1);
    }, []);

    useImperativeHandle(ref, () => ({
      refresh: () => {
        fetchActivityLogs(1);
      },
    }));

    return (
      <div className={`${styles.tableContainer} table-scroll`}>
        <table>
          <thead>
            <tr>
              <th>Email</th>
              <th>Timestamp</th>
              <th>Change</th>
            </tr>
          </thead>
          <tbody>
            {!loading ? (
              currentPageItems?.length !== 0 ? (
                currentPageItems?.map((item) => (
                  <tr key={item.id} className={styles.activityLogRow}>
                    <td>{item.performedBy}</td>
                    <td>{formatDateWithTime(new Date(item.timeStamp))}</td>
                    <td className={styles.changesCell}>
                      {renderChangeSet(item.changes)}
                    </td>
                  </tr>
                ))
              ) : (
                <tr style={{ background: "none" }}>
                  <td colSpan={100}>
                    <div className={styles.noneFound}>
                      <img src="/none_found.svg" />
                      <h3>No activity log</h3>
                    </div>
                  </td>
                </tr>
              )
            ) : (
              Array.from({ length: 8 }, (v, i) => i).map((i) => (
                <UserSkeleton key={"user-skeleton-" + i} noOfStandard={8} />
              ))
            )}
          </tbody>
        </table>

        <div className={styles.pagination}>
          <Pagination
            currentPage={page}
            setCurrentPage={(currentPage: number) => {
              setPage(currentPage);
              fetchActivityLogs(currentPage);
            }}
            numberOfPages={totalPages}
          />
        </div>
      </div>
    );
  },
);

export default ActivityLogTab;

function renderChangeSet(changes: any[]) {
  // const filteredChanges = filterChangeSet(changes)

  return changes.map((c) => {
    const field = formatChangeSetField(c.fieldChanged);
    const values = formatChangeSetValues(c);

    return (
      <div className={styles.changesCellItem}>
        <p className={styles.changesCellItemField}>{field}</p>
        <div className={styles.changesCellItemValues}>
          <span>{values.oldValue}</span>
          <span>
            <ArrowRight width={16} height={16} />
          </span>
          <span>{values.newValue}</span>
        </div>
      </div>
    );
  });
}

function formatChangeSetField(fieldName: string) {
  switch (fieldName) {
    case "type":
      return "Type";
    case "name":
      return "Name";
    case "amount":
      return "Amount";
    case "amountType":
      return "Amount Type";
    case "promoCode":
      return "Promo Code";
    case "feeToDiscount":
      return "Fee to Discount";
    case "expiryDate":
      return "Expiry Date";
    case "startDate":
      return "Start Date";
    case "status":
      return "Status";
    case "isReusable":
      return "Is Reusable";
    case "maxCodeUses":
      return "Maximum Code Uses";
    default:
      return fieldName;
  }
}

function formatChangeSetValues(change: any): {
  oldValue: string;
  newValue: string;
} {
  switch (change.fieldChanged) {
    case "startDate":
    case "expiryDate":
      return {
        oldValue: formatDateWithTime(new Date(change.oldValue)),
        newValue: formatDateWithTime(
          new Date(change.newValue.replace(/\[UTC\]$/, "")),
          true,
        ),
      };

    case "status":
      return {
        oldValue: change.oldValue === "true" ? "Active" : "Inactive",
        newValue: change.newValue === "true" ? "Active" : "Inactive",
      };

    default:
      return {
        oldValue: change.oldValue,
        newValue: change.newValue,
      };
  }
}

// workaround until backend fixes
// remove invalid changesets like empty array changesets
// and changsets for expiryDate where the new and old value are the same
// function filterChangeSet(changes: any[]) {
//   return changes.filter(c => {
//     if (c.fieldChanged === 'expiryDate') {
//       const normalizedNewValueDateString = c.newValue.replace(/\[UTC\]$/, "");
//       return c.oldValue !== normalizedNewValueDateString
//     } else {
//       return true
//     }
//   })
// }
