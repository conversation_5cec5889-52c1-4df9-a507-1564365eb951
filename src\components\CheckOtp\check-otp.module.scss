@use "../../styles/theme.scss" as *;

.form {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  h2 {
    font-size: 48px;
    font-weight: 700;
    line-height: 72px;
    margin: 0;
    margin-bottom: 12px;
  }
  .tag {
    margin: 0 0 24px 0;
  }
  .notReceived {
    margin: 31px 0 0 0;
  }
}

.notReceived {
  display: flex;
  align-items: center;
}
.resetTime {
  color: $black;
  font-size: 16px;
  line-height: 24px;
  margin: 0;
  margin-left: 10px;
  display: flex;
  align-items: center;
}

.resend {
  background: none;
  border: none;
  color: $black;
  font-weight: 600;
  margin-left: 10px;
  padding: 0;
  font-size: 16px;
  line-height: 24px;
  cursor: pointer;
  transition: color 0.1 ease;
  &:hover {
    color: $orange;
  }
  &:disabled {
    opacity: 0.5;
  }
}
