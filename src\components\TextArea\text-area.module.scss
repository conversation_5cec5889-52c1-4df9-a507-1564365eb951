@use "../../styles/theme.scss" as *;

.input {
  width: 100%;
  resize: none;
  height: 150px;
  border-radius: 8px;
  border: 1px solid $grey;
  padding: 16px 48px 16px 16px;
  transition: border 0.1s ease;
  font-size: 16px;
  color: $black;
  background: none;
  &.autoHeight {
    height: 275px;
  }
  &::placeholder {
    color: transparent;
  }
  &:focus {
    border: 2px solid $orange !important;
    caret-color: $orange;
    outline: none;
    &::placeholder {
      color: $grey;
    }
    & + .label {
      top: -5px;
      left: 12px;
      font-size: 12px;
      line-height: 16px;
      padding: 0 4px;
      color: $orange;
    }
    & ~ .errorIcon {
      display: none;
    }
    & ~ .clearIcon {
      display: block;
    }
  }
  &.error {
    border: 2px solid $error !important;
  }
  &:hover {
    border: 1px solid $black;
  }
  &:disabled {
    border: 1px solid $disabled;
    color: $disabled-text;
    background: none;
    &.readonly {
      color: $black;
      border: 1px solid #cacaca;
      & + .label {
        color: $placeholder;
      }
    }
    & + .label {
      color: $disabled-text;
    }
    & ~ .eyeIcon {
      opacity: 0.38;
    }
  }
}

.password {
  padding-right: 80px;
}

.inputContainer {
  width: 100%;
  margin-bottom: 12px;
}

.label {
  position: absolute;
  z-index: 10;
  background: $off-white;
  left: 16px;
  top: 16px;
  line-height: 24px;
  transition: all 0.3s ease;
  font-size: 16px;
  pointer-events: none;
  color: $placeholder;
  &.hasValue {
    top: -5px;
    left: 12px;
    font-size: 12px;
    line-height: 16px;
    padding: 0 4px;
  }
  &.labelError {
    color: $error !important;
  }
  &.white {
    background: #fff;
  }
}

.inputWrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.eyeIcon,
.clearIcon,
.errorIcon {
  position: absolute;
  right: 15px;
  top: 16px;
}

.clearIcon,
.eyeIcon {
  cursor: pointer;
}

.clearIcon {
  display: none;
}

.errorText {
  margin: 0px 0px 0px 5px;
  font-size: 12px;
  color: $error;
  text-align: start;
}
