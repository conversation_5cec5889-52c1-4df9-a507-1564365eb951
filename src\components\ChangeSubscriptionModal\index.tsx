import { useCallback, useEffect, useState } from "react";
import Button from "../Button";
import Modal from "../Modal";
import SingleDatePicker from "../SingleDatePicker";
import { ArrowsClockwise, ClockCounterClockwise, Pencil } from "../svgs";
import styles from "./change-subscription-modal.module.scss";
import SelectPlanModal from "../SelectPlanModal";
import { ApiGetWithId, ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import formatDate from "../utils/formatDate";
import Spinner from "../Spinner";
import ToggleControl from "../ToggleControl";
import { clsx } from "clsx";
import { AxiosResponse } from "axios";
import qs from "qs";
import SubscriptionChangeHistoryModal from "../SubscriptionChangeHistoryModal";

const ChangeSubscriptionModal = ({ show, setShow, plan, repopulate }: any) => {
  const dispatch = useDispatch();

  const { mvnoId } = useParams();

  const [showSelectPlan, setShowSelectPlan] = useState(false);

  const [selectedPlan, setSelectedPlan] = useState(null as any);
  const [effectiveDate, setEffectiveDate] = useState(null as any);
  const [immediate, setImmediate] = useState(true);

  const [immediateError, setImmediateError] = useState("");

  const [loading, setLoading] = useState(false);

  const [newPlans, setNewPlans] = useState([] as any);

  const [view, setView] = useState<"default" | "change-or-create" | "cancel">(
    "default",
  );
  const [isEditing, setIsEditing] = useState(false);

  const [upcomingChange, setUpcomingChange] = useState<any>(null);
  const [upcomingChangeLoading, setUpcomingChangeLoading] = useState(true);
  const [upcomingChangeError, setUpcomingChangeError] = useState("");

  const getUpcomingChange = () => {
    setUpcomingChangeLoading(true);
    setUpcomingChangeError("");

    const params = qs.stringify({
      status: "pending",
      subscriptionId: plan.id,
    });
    ApiGetWithId(`/accounts/schedule-rateplan?${params}`, mvnoId)
      .then((response) => {
        const upcomingChange = response.data.content[0];
        setUpcomingChange(upcomingChange);
        if (!upcomingChange) {
          setView("change-or-create");
        } else {
          setView("default");
          setEffectiveDate(new Date(upcomingChange.scheduleDate));
          setImmediate(false);
        }
      })
      .catch((error) => {
        console.log(error);
        setUpcomingChangeError(
          error.response.data.message || "Something went wrong",
        );
      })
      .finally(() => {
        setUpcomingChangeLoading(false);
      });
  };

  useEffect(() => {
    // reset view and fetch change history when modal is opened
    if (show) {
      setView("default");
      getUpcomingChange();
    }
  }, [show]);

  const renderUpcomingSubscriptionChange = useCallback(() => {
    return (
      <div className={styles.upcomingChange}>
        <div className={styles.label}>Upcoming Subscription Change</div>
        <UpcomingSubscriptionChange upcomingChange={upcomingChange} />

        <div className={styles.buttons}>
          <Button onClick={() => setView("cancel")}>Cancel Change</Button>
          <Button
            onClick={() => {
              setIsEditing(true);
              setView("change-or-create");
            }}
          >
            Edit Change
          </Button>
        </div>
      </div>
    );
  }, [upcomingChange]);

  const renderChangeSubscriptionHeader = useCallback(() => {
    return (
      <>
        <h3>Change Subscription</h3>
        <p className={styles.tag}>
          Current Subscription: {plan && plan.product.product}
        </p>
      </>
    );
  }, [plan]);

  const reset = () => {
    setNewPlans([]);
    setShowSelectPlan(false);
    setSelectedPlan(null as any);
    setLoading(false);
    setImmediate(true);
    setEffectiveDate(null as any);
    setImmediateError("");
  };

  const getAvailablePlans = () => {
    setLoading(true);
    ApiPostAuth("/products/availableupdatedproducts", {
      subscriber: plan.mdn,
    })
      .then((response) => {
        if (response.data.length === 0) {
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: "No plans available",
            },
          });
        } else {
          setLoading(false);
          setNewPlans(response.data);
          setShowSelectPlan(true);
        }
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message:
              error.response.data.message ||
              "Something went wrong, please try again",
          },
        });
      });
  };

  const handleChangePlan = () => {
    const checkCanSubmit = () => {
      if (!isEditing) {
        // if not editing, must have selected plan
        return selectedPlan && (immediate || effectiveDate);
      } else {
        // if editing, a plan is already available so no need to check for it
        return immediate || effectiveDate;
      }
    };

    if (checkCanSubmit()) {
      setLoading(true);

      let apiCallPromise: Promise<AxiosResponse<any, any>> = Promise.reject(
        "Not implemented",
      ) as any;

      if (isEditing) {
        apiCallPromise = ApiPostAuth("/accounts/update-schedule/rateplan", {
          subscriber: plan.mdn,
          subscriptionId: plan.id,
          immediate,
          ...(selectedPlan
            ? {
                product: {
                  deviceType: selectedPlan.DeviceType,
                  offerId: selectedPlan.OfferId,
                  offerName: selectedPlan.Name,
                  productFamily: selectedPlan.ProductFamily,
                  size: selectedPlan.Size,
                  serviceType: selectedPlan.TalkAndText,
                },
              }
            : {}),
          ...(effectiveDate
            ? { scheduledTime: getCurrentTimezoneDateString(effectiveDate) }
            : {}),
          // backend requires existing scheduled/upcoming change id if editing scheduled change
          scheduledPlanId: upcomingChange.id,
        });
      } else if (!isEditing && immediate) {
        apiCallPromise = ApiPostAuth("/accounts/updaterateplan", {
          subscriber: plan.mdn,
          product: {
            deviceType: selectedPlan.DeviceType,
            offerId: selectedPlan.OfferId,
            offerName: selectedPlan.Name,
            productFamily: selectedPlan.ProductFamily,
            size: selectedPlan.Size,
            serviceType: selectedPlan.TalkAndText,
          },
          subscriptionId: plan.id,
        });
      } else if (!isEditing && !immediate) {
        apiCallPromise = ApiPostAuth("/accounts/create-schedule/rateplan", {
          subscriber: plan.mdn,
          product: {
            deviceType: selectedPlan.DeviceType,
            offerId: selectedPlan.OfferId,
            offerName: selectedPlan.Name,
            productFamily: selectedPlan.ProductFamily,
            size: selectedPlan.Size,
            serviceType: selectedPlan.TalkAndText,
          },
          subscriptionId: plan.id,
          scheduledTime: getCurrentTimezoneDateString(effectiveDate),
        });
      }

      apiCallPromise
        .then((response) => {
          dispatch({
            type: "notify",
            payload: {
              error: false,
              message: response.data.message,
            },
          });
          setShow();
          setTimeout(reset, 300);
          repopulate();
        })
        .catch((error) => {
          if (immediate) {
            setImmediateError(
              "Immediate change failed. If error persists, please try scheduling at a later date instead.",
            );
          }
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message:
                error.response.data.message ||
                "Something went wrong, please try again",
            },
          });
        });
    } else {
      dispatch({
        type: "notify",
        payload: {
          error: true,
          message: "Please enter all required fields",
        },
      });
    }
  };

  const handleCancelPlanChange = () => {
    setLoading(true);
    ApiPostAuth("/accounts/cancel-schedule/rateplan", {
      scheduledPlanId: upcomingChange.id,
    })
      .then((response) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        setShow();
        setTimeout(reset, 300);
        repopulate();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message:
              error.response?.data.message ||
              "Something went wrong, please try again",
          },
        });
      });
  };

  const getModalSaveButtonProps = () => {
    if (upcomingChange && view === "default") {
      return {
        btnColor: "secondary",
        btnContent: (
          <>
            <ClockCounterClockwise />
            Change History
          </>
        ),
      };
    }

    if (view === "change-or-create") {
      return {
        btnContent: (
          <>
            <ArrowsClockwise />
            {immediate ? "Apply Change" : "Schedule Change"}
          </>
        ),
      };
    }

    if (view === "cancel") {
      return {
        btnContent: "Yes, Cancel Change",
      };
    }

    return {};
  };

  const getModalCancelButtonProps = () => {
    if (view === "cancel") {
      return {
        btnContent: "No",
      };
    }

    return {};
  };

  const getModalSecondaryButtonProps = () => {
    if (view === "change-or-create") {
      return {
        btnContent: (
          <>
            <ClockCounterClockwise />
            Change History
          </>
        ),
        onclick: () => {
          setShowChangeHistoryModal(true);
        },
      };
    }

    return {};
  };

  const getModalOnProceed = () => {
    if (view === "default") {
      return () => {
        setShowChangeHistoryModal(true);
      };
    }

    if (view === "cancel") {
      return handleCancelPlanChange;
    } else {
      return handleChangePlan;
    }
  };

  const getModalOnCancel = () => {
    if (view === "cancel") {
      return () => {
        setView("default");
      };
    } else {
      return () => {
        setShow("");
        reset();
      };
    }
  };

  const [showChangeHistoryModal, setShowChangeHistoryModal] = useState(false);

  return (
    <Modal
      show={show}
      close={() => {
        setShow("");
      }}
      noCloseOnCancel
      image="/bulk_edit_confirm_graphic.svg"
      saveButton={getModalSaveButtonProps().btnContent}
      saveButtonColor={getModalSaveButtonProps().btnColor}
      cancelButton={getModalCancelButtonProps().btnContent}
      fullSize
      loading={loading}
      proceed={getModalOnProceed()}
      onCancel={getModalOnCancel()}
      secondary={getModalSecondaryButtonProps().btnContent}
      onSecondary={getModalSecondaryButtonProps().onclick}
    >
      <SelectPlanModal
        show={showSelectPlan}
        setShow={setShowSelectPlan}
        setSelectedPlan={setSelectedPlan}
        plans={newPlans}
      />
      <SubscriptionChangeHistoryModal
        show={showChangeHistoryModal}
        onClose={() => setShowChangeHistoryModal(false)}
        subscriptionId={plan?.id}
      />
      <div className={styles.main}>
        {view === "default" && (
          <>
            {renderChangeSubscriptionHeader()}
            {upcomingChangeLoading ? (
              <Loading />
            ) : (
              <>
                {/* view is switched to change-or-create when there is no upcoming change */}
                {upcomingChange && renderUpcomingSubscriptionChange()}
                {upcomingChangeError && (
                  <div className={styles.error}>
                    <div className={styles.title}>
                      Error Loading Change History!
                    </div>
                    <div className={styles.message}>{upcomingChangeError}</div>
                    <Button onClick={getUpcomingChange} color="secondary">
                      Retry
                    </Button>
                  </div>
                )}
              </>
            )}
          </>
        )}

        {view === "change-or-create" && (
          <>
            {renderChangeSubscriptionHeader()}
            <div className={styles.newSub}>
              <div className={styles.label}>New Subscription</div>
              <Button
                disabled={loading}
                onClick={getAvailablePlans}
                style={{
                  marginTop: 4,
                  width: "100%",
                  height: "auto",
                  padding: "10px 28px",
                }}
                color="secondary"
              >
                {/* if plan  */}
                {selectedPlan || upcomingChange ? (
                  <>
                    {selectedPlan?.Name || upcomingChange?.schedulePlanName}
                    <span style={{ flexShrink: 0 }}>
                      <Pencil />
                    </span>
                  </>
                ) : (
                  "Select A New Subscription"
                )}
              </Button>

              {/* Show error notice if immediate plan change fails to inform user they can schedule instead */}
              {immediate && immediateError && (
                <div className={styles.error}>
                  <p className={styles.message}>{immediateError}</p>
                </div>
              )}
              <div className={clsx(styles.label, styles.effectiveDate)}>
                Effective Date
              </div>
              <div className={styles.toggleControl}>
                <ToggleControl
                  options={[
                    { label: "Immediate", value: "immediate" },
                    { label: "Future ", value: "future" },
                  ]}
                  selectedOption={immediate ? "immediate" : "future"}
                  onChange={() => {
                    setImmediate(!immediate);
                  }}
                />
              </div>
              {!immediate && (
                <div className={styles.datePicker}>
                  <SingleDatePicker
                    date={effectiveDate}
                    setDate={setEffectiveDate}
                    future
                  />
                </div>
              )}
            </div>
          </>
        )}

        {view === "cancel" && (
          <div className={styles.cancelChange}>
            <h2 className={styles.title}>
              Cancel Upcoming Subscription Change?
            </h2>
            <UpcomingSubscriptionChange upcomingChange={upcomingChange} />
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ChangeSubscriptionModal;

const Loading = () => {
  return (
    <div className={styles.loadingContainer}>
      <Spinner />
    </div>
  );
};

const UpcomingSubscriptionChange = ({
  upcomingChange,
}: {
  upcomingChange: any;
}) => {
  return (
    <div className={styles.upcomingSubDetails}>
      <table>
        <tbody>
          <tr>
            <td>New Subscription</td>
            <td style={{ paddingBottom: "8px" }}>
              {upcomingChange?.schedulePlanName}
            </td>
          </tr>
          <tr>
            <td>Effective Date</td>
            <td>{formatDate(upcomingChange?.scheduleDate)}</td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

// date picker currently returns date in UTC and for positive time zones (>+1), this results in a date(DD) that is 1 day behind.
// this function ensures the date(DD) is as chosen for the current timezone
// i.e. if user selects 30th, it doesn't return 29th
const getCurrentTimezoneDateString = (date: any): string => {
  const parsedDate = date instanceof Date ? date : new Date(date);

  if (isNaN(parsedDate.getTime())) {
    throw new Error("Invalid date provided");
  }

  const year = parsedDate.getFullYear();
  const month = String(parsedDate.getMonth() + 1).padStart(2, "0");
  const day = String(parsedDate.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};
