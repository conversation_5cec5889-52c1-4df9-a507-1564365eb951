import { faker } from "@faker-js/faker";

const generateChannel = () => {
  return {
    name: faker.location.city(),
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    //email: faker.internet.email(),
    //phoneNumber: faker.phone.number(),
    subscribers: faker.number.int({ min: 10, max: 100 }),
    subscriptions: faker.number.int({ min: 100, max: 1000 }),
    status: "Active",
  };
};

export const getChannels = () => {
  return Array.from({ length: 100 }).map((i) => generateChannel());
};
