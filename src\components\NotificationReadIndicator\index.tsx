import { ApiPost } from "../../pages/api/api";
import Tooltip from "../Tooltip";
import styles from "./notification-read-indicator.module.scss";
import { useDispatch } from "react-redux";
import { useState } from "react";

const NotificationReadIndicator = ({ read, setRead, id, repopulate }: any) => {
  const dispatch = useDispatch();

  const [disabled, setDisabled] = useState(false);

  const handleChange = () => {
    setDisabled(true);
    const current = read;
    setRead((prev: boolean) => !prev);
    ApiPost(`/notifications/read/${id}`, {
      readFlag: !current,
    })
      .then((response) => {
        setDisabled(false);
        repopulate();
      })
      .catch((error) => {
        setDisabled(false);
        setRead(current);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: "Something went wrong, please try again",
          },
        });
      });
  };

  return (
    <Tooltip show text={read ? "Mark as unread" : "Mark as read"}>
      <div
        onClick={() => {
          if (!disabled) {
            handleChange();
          }
        }}
        className={`${styles.container} ${read && styles.read}`}
      >
        <div className={`${styles.inner} ${read && styles.read}`} />
      </div>
    </Tooltip>
  );
};

export default NotificationReadIndicator;
