.note {
  border-radius: 8px;
  background: #f7f6f6;
  padding: 16px;
  font-size: 14px;
  line-height: 21px;
  display: flex;
  flex-direction: column;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    color: #4d4d4d;
  }
  .viewButton {
    background: none;
    border: none;
    font-size: 14px;
    line-height: 21px;
    color: #4d4d4d;
    text-decoration: underline;
    padding: 0;
    cursor: pointer;
    &:hover {
      color: #000;
    }
  }
  .noteContent {
    margin-bottom: 16px;
    white-space: pre-line;
  }
  .date {
    color: #4d4d4d;
    margin-top: auto;
  }
}
