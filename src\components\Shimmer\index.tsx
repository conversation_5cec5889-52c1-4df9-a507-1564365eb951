import styles from "./shimmer.module.scss";
import { useRef, useEffect, useState } from "react";

const Shimmer = () => {
  const container = useRef(null);

  const [offset, setOffset] = useState(0);

  useEffect(() => {
    if (container.current) {
      setOffset((container.current as any).getBoundingClientRect().left);
    }
  }, []);

  return (
    <div className={styles.shimmerWrapper} ref={container}>
      <div className={styles.shimmer} />
    </div>
  );
};

export default Shimmer;
