import styles from "./multi-select.module.scss";
import { ControlledMenu, useMenuState } from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/slide.css";
import { ChevronDown } from "../svgs";
import { useEffect, useRef, useState } from "react";
import Checkbox from "../Checkbox";
import Button from "../Button";
import SearchBar from "../SearchBar";
import { submitFilterSelectSearch } from "../utils/searchAndFilter";
import Spinner from "../Spinner";
import { MultiSelectOption, MultiSelectProps } from "./types";

const MultiSelect = ({
  label,
  options,
  optionsLoading = false,
  onOpen,
  selected,
  setSelected,
  grid = false,
  twoColumnGrid = false,
  noClear = false,
  search = false,
  searchPlaceholder = "Search",
  darkerBg = false,
}: MultiSelectProps) => {
  const ref = useRef<HTMLDivElement>(null);

  const [menuProps, toggleMenu] = useMenuState({ transition: true });

  const [stagedChanges, setStagedChanges] = useState<(string | number)[]>([...selected]);

  useEffect(() => {
    setStagedChanges([...selected]);
  }, [selected]);

  const reset = () => {
    toggleMenu(false);
    setQuery("");
    setFilteredOptions(options);
  };

  const [query, setQuery] = useState("");

  const [filteredOptions, setFilteredOptions] = useState<MultiSelectOption[]>([]);

  useEffect(() => {
    setFilteredOptions(options);
  }, [options]);

  return (
    <div className={`${styles.box} multi-select select`}>
      <div
        ref={ref}
        className={`${styles.menuButton} ${menuProps.state === "open" || menuProps.state === "opening"
            ? styles.iconOpen
            : styles.iconClosed
          } ${darkerBg && styles.darkerBg}`}
        onClick={() => {
          if (menuProps.state === "closing") {
            toggleMenu(false);
          } else {
            onOpen?.();
            toggleMenu(true);
          }
        }}
      >
        {label}
        <ChevronDown />
      </div>
      <ControlledMenu
        {...menuProps}
        anchorRef={ref}
        onClose={() => {
          reset();
          setStagedChanges([...selected]);
        }}
        align="start"
        position="auto"
        viewScroll="close"
        onItemClick={(e) => (e.keepOpen = true)}
      >
        {search && (
          <div style={{ marginBottom: 32 }}>
            <SearchBar
              onSubmit={() => {
                submitFilterSelectSearch(
                  options,
                  setFilteredOptions,
                  query,
                  setQuery,
                );
              }}
              query={query}
              setQuery={setQuery}
              placeholder={searchPlaceholder}
              small
              grey
            />
          </div>
        )}
        {optionsLoading ? (
          <div className={styles.loading}>
            <Spinner size={32} />
          </div>
        )
          :
          (<div
            className={`${styles.container} ${grid && styles.grid} ${twoColumnGrid && styles.twoColumnGrid
              } modal-scroll`}
          >
            {filteredOptions.map((item: MultiSelectOption) => (
              <div
                className={`${styles.menuItem} ${selected.includes(item.key) && styles.selected
                  }`}
                onClick={() => {
                  if (stagedChanges.includes(item.key)) {
                    setStagedChanges(
                      stagedChanges.filter((change) => change !== item.key),
                    );
                  } else {
                    setStagedChanges([...stagedChanges, item.key]);
                  }
                }}
                key={item.key}
              >
                <Checkbox
                  checked={stagedChanges.includes(item.key)}
                  onClick={() => {
                    if (stagedChanges.includes(item.key)) {
                      setStagedChanges(
                        stagedChanges.filter((change) => change !== item.key),
                      );
                    } else {
                      setStagedChanges([...stagedChanges, item.key]);
                    }
                  }}
                />
                {item.label}
              </div>
            ))}
          </div>)}
        <div className={styles.buttons}>
          {!noClear && (
            <Button
              color="quaternary"
              style={{
                marginRight: "auto",
                opacity:
                  stagedChanges.length !== 0 || selected.length !== 0 ? 1 : 0,
                pointerEvents:
                  stagedChanges.length !== 0 || selected.length !== 0
                    ? "all"
                    : "none",
              }}
              onClick={() => {
                setStagedChanges([]);
              }}
            >
              Clear All
            </Button>
          )}
          <Button
            onClick={() => {
              setStagedChanges(selected);
              reset();
            }}
            style={{ marginRight: 12, marginLeft: 20, minWidth: 0 }}
            color="secondary"
          >
            Cancel
          </Button>
          <Button
            style={{ minWidth: 0 }}
            onClick={() => {
              setSelected(stagedChanges);
              reset();
            }}
          >
            Apply
          </Button>
        </div>
      </ControlledMenu>
    </div>
  );
};

export default MultiSelect;

// Re-export types for convenience
export type { MultiSelectOption, MultiSelectProps, MultiSelectValue, MultiSelectSetSelectedCallback, MultiSelectOnOpenCallback } from "./types";
