import styles from "./delete-user-modal.module.scss";
import Modal from "../Modal";
import { Delete } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { ApiDelete } from "../../pages/api/api";

const DeleteUserModal = ({ show, setShow, user, repopulateUsers }: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  const deleteUser = () => {
    setLoading(true);
    ApiDelete(`/users/${user.userId}`, {})
      .then((response) => {
        setLoading(false);
        setShow(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulateUsers();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Modal
      saveButton={
        <>
          <Delete />
          Delete Forever
        </>
      }
      image="/delete_user_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={deleteUser}
      loading={loading}
    >
      <div className={styles.main}>
        <h3>
          Are you sure you want to delete
          <br />
          {user && user.firstName} {user && user.lastName}?
        </h3>
      </div>
    </Modal>
  );
};

export default DeleteUserModal;
