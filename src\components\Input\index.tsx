/*
==========================================================================================

                                        Input

Description: Input component for use accross the whole app
             with label, input and inline errors
==========================================================================================
*/

import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import styles from "./input.module.scss";
import { Fade, Collapse } from "@mui/material";

type InputProps = {
  label?: string;
  placeholder?: string;
  value?: string | number;
  onChange?: (e: any) => void;
  error?: string;
  onKeyDown?: () => void;
  password?: boolean;
  clear?: () => void;
  disabled?: boolean;
  readonly?: boolean;
  white?: boolean;
  id?: string | null;
  number?: boolean;
  leftComponent?: React.ReactNode;
  rightComponent?: React.ReactNode;
};

export const Input = forwardRef(
  (
    {
      label,
      placeholder,
      value,
      onChange,
      error,
      onKeyDown,
      password,
      clear,
      disabled,
      readonly,
      white,
      id = null,
      number,
      leftComponent,
      rightComponent,
    }: InputProps,
    ref: any,
  ) => {
    const [type, setType] = useState(
      number ? "number" : password ? "password" : "text",
    );
    const [show, setShow] = useState(false);

    // Handles change from password to text on 'eye' icon click
    const handleShowChange = () => {
      if (show) {
        setType("password");
        setShow(false);
      } else {
        setType("text");
        setShow(true);
      }
      //ref.current!.focus();
    };

    const inputRef = useRef(null) as any;
    useImperativeHandle(ref, () => ({
      focus: () => {
        inputRef.current.focus();
      },
    }));

    useEffect(() => {
      setTimeout(() => {
        if (inputRef.current && inputRef.current.value.length > 0) {
          inputRef.current.selectionStart = inputRef.current.value.length;
          inputRef.current.selectionEnd = inputRef.current.value.length;
        }
      }, 0);
    }, [type]);

    return (
      <form
        style={{ width: "100%" }}
        autoComplete="none"
        onKeyPress={(event: any) => {
          return event.keyCode != 13;
        }}
        onSubmit={(e) => {
          e.preventDefault();
        }}
      >
        <div className={styles.inputContainer}>
          <div
            className={`${styles.inputWrapper} ${leftComponent ? styles.hasLeftComponent : ""}`}
          >
            {leftComponent ? (
              <span className={styles.leftComponent}>{leftComponent}</span>
            ) : null}
            <input
              id={id || undefined}
              className={`${styles.input} ${readonly && styles.readonly} ${password && styles.password
                } ${error && styles.error} ${disabled && "input-disabled"}`}
              value={value}
              placeholder={placeholder}
              onChange={(e: any) => {
                onChange?.(e);
              }}
              onKeyDown={(event) => {
                if (event.key === "Enter") {
                  event.preventDefault();
                  onKeyDown?.();
                }
              }}
              type={type}
              disabled={disabled}
              readOnly={readonly}
              ref={inputRef}
            />
            {rightComponent ? (
              <span className={styles.rightComponent}>{rightComponent}</span>
            ) : null}
            <div
              className={`${styles.label} ${readonly && styles.readonly} ${value && styles.hasValue
                } ${error && styles.labelError} ${white && styles.white}`}
            >
              {label}
            </div>
            {!readonly && (<img
              src="/input_clear.svg"
              className={styles.clearIcon}
              onMouseDown={(e) => {
                e.preventDefault();
              }}
              onClick={clear}
              style={{ right: password ? 45 : 20 }}
            />)}
            {password && (
              <img
                src={show ? "/password_show.svg" : "/password_hide.svg"}
                className={styles.eyeIcon}
                onClick={handleShowChange}
                onMouseDown={(e) => {
                  e.preventDefault();
                }}
              />
            )}
            <Fade in={error ? true : false}>
              <img
                src="/input_error.svg"
                className={styles.errorIcon}
                onMouseDown={(e) => {
                  e.preventDefault();
                }}
                style={{ right: password ? 45 : 20 }}
              />
            </Fade>
          </div>
          <Collapse in={error ? true : false}>
            <p className={styles.errorText} id={`${id}-error`}>
              {error || <br />}
            </p>
          </Collapse>
        </div>
      </form>
    );
  },
);
