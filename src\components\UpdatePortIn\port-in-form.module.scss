@use "../../styles/theme.scss" as *;

.main {
  margin: auto;
  width: 100%;
  // max-width: 350px;
  display: flex;
  flex-direction: column;
}

.backButton {
  position: absolute;
  top: 35px;
  left: 15px;
  cursor: pointer;
  transition: all 0.1s ease;
  padding: 6px;
  border-radius: 6px;
  outline: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  svg {
    vertical-align: middle;
  }
  &:hover {
    background: #f6f6f6;
  }
}

.imeiMain {
  width: 350px;
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  h3 {
    margin: 0 0 12px 0;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
  }
  h4 {
    margin: 0 0 40px 0;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
  }
}

.section {
  h3 {
    font-size: 24px;
    line-height: 36px;
    font-weight: 700;
    margin: 0;
  }
  .info {
    text-align: center;
    font-size: 14px;
    line-height: 21px;
    margin: 12px 0px 35px 0px;
  }
}

.inputTitle {
  color: $placeholder;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 12px;
}

.inputsGrid {
  display: grid;
  width: 100%;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(5, auto);
  grid-column-gap: 16px;
  grid-row: 16px;
  form:nth-child(1) {
    grid-area: 1/1/2/2;
  }
  form:nth-child(2) {
    grid-area: 1/2/2/3;
  }
  form:nth-child(3) {
    grid-area: 2/1/3/3;
  }
  form:nth-child(4) {
    grid-area: 3/1/4/3;
  }
}
.oldInputsGrid {
  display: grid;
  align-items: end;
  width: 100%;
  max-width: 430px;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(5, auto);
  grid-column-gap: 16px;
  grid-row: 16px;
}
.oldNumber {
  width: 430px;
}

.subscriberCont {
  display: grid;
  width: 389px;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(6, auto);
  grid-column-gap: 16px;
  grid-row: 16px;
  align-items: end;
  form:nth-child(1) {
    grid-area: 1 / 1 / 2 / 2;
  }
  form:nth-child(2) {
    grid-area: 1/2/2/3;
  }
  form:nth-child(3) {
    grid-area: 2/1/3/2;
  }
  form:nth-child(4) {
    grid-area: 2/2/3/3;
  }
  form:nth-child(5) {
    grid-area: 3/1/4/2;
  }
  form:nth-child(6) {
    grid-area: 3 / 2 / 4 / 3;
  }
  form:nth-child(7) {
    grid-area: 4 / 1 / 5 / 2;
  }
  form:nth-child(8) {
    grid-area: 4 / 2 / 5 / 3;
  }
  form:nth-child(9) {
    grid-area: 5 / 1 / 6 / 3;
  }
  form:nth-child(10) {
    grid-area: 6 / 1 / 7 / 3;
  }
}
.subscriberBox {
  display: flex;
  padding: 16px 24px;
  align-items: flex-start;
  gap: 10px;
  border-radius: 16px;
  background: #f7f6f6;
  width: fit-content;
  .titles {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

.notEligible {
  border: 2px solid $urgent;
  margin-bottom: 24px;
  color: $urgent;
  padding: 12px 16px;
  border-radius: 8px;
  display: grid;
  grid-template-columns: 32px 1fr;
  grid-column-gap: 12px;
  text-align: start;
  .topText {
    margin-bottom: 6px;
    color: $placeholder;
  }
  .bottomText {
    color: $placeholder;
    font-size: 12px;
    line-height: 18px;
  }
  svg {
    width: 32px;
    height: 32px;
  }
}

.eligible {
  border: 2px solid $success;
  color: $success;
  margin-bottom: 12px;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  display: grid;
  grid-template-columns: 32px 1fr;
  grid-column-gap: 12px;
  width: 100%;
  justify-items: start;
  align-items: center;
}

.plansMain {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.plans {
  margin-right: 12px;
}

.planType {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.planSwitch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px 0 35px;
  height: 50px;
  width: 326px;
  border-radius: 1000px;
  position: relative;
  user-select: none;
  overflow: hidden;
  transition: all 0.3s ease;
  .label {
    position: relative;
    z-index: 1000;
    font-weight: 600;
    transition: color 0.3s ease;
  }
  .thumb {
    position: absolute;
    height: 100%;
    border-radius: 1000px;
    width: 163px;
    transition: all 0.3s ease;
    z-index: 900;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.15);
  }
}

.noPlans {
  width: 100%;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 150px;
  img {
    width: 60%;
    position: absolute;
    z-index: 1000;
  }
  span {
    z-index: 2000;
    font-weight: 700;
  }
}
