import styles from "./resubmit-activation.module.scss";
import Modal from "../Modal";
import { Delete } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { ApiDelete, ApiPostAuth } from "../../pages/api/api";
import AddSubscriberErrorModal from "../AddSubscriberErrorModal";

const ResubmitActivationNoUpdate = ({
  show,
  setShow,
  subData,
  repopulate,
}: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const resubmit = () => {
    setLoading(true);
    ApiPostAuth("/accounts/attactivation/resubmit", {
      subscriptionId: subData?.id?.toString(),
      imei: subData?.imei,
      iccid: subData?.iccid,
      product: {
        deviceType: subData.product.deviceType,
        offerId: subData.product.offerId,
        offerName: subData.product.product,
        productFamily: subData.product.productFamily,
        size: subData.product.productSize,
        soc: subData.product.soc,
        serviceType: subData.product.serviceType,
        retailName: subData.product.retailName,
        retailPrice: subData.product.retailPrice,
      },
    })
      .then((response) => {
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulate();
        setShow(false);
        setLoading(false);
      })
      .catch((error) => {
        setLoading(false);
        setShowError(true);
        setErrorMessage(
          error.response.data.attResponse?.errorDescription ||
            error.response.data.message,
        );
      });
  };

  return (
    <Modal
      saveButton="Re-Submit"
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={resubmit}
      loading={loading}
    >
      <AddSubscriberErrorModal
        show={showError}
        setShow={setShowError}
        error={errorMessage}
      />
      <div className={styles.main}>
        <h3>
          Are you sure you want to resubmit activation for
          <br />
          {subData?.mdn || subData?.cancelledMdn}?
        </h3>
      </div>
    </Modal>
  );
};

export default ResubmitActivationNoUpdate;
