@use "./theme.scss" as *;

.main {
  padding: 50px 40px;
  transition: all 0.5s ease;
  flex-grow: 1;
  &.ticketOpen {
    padding: 24px;
    padding-right: 624px;
    min-width: 1206px;
  }
}

.selectionWrapper {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  margin-top: -23px;
  h2 {
    margin: 0;
    margin-right: 24px;
    font-size: 20px;
    font-weight: 700;
  }
}

.selection {
  height: 47px;
  border-radius: 1000px;
  color: $black;
  font-size: 14px;
  font-weight: 600;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 24px;
  &:hover {
    color: $dark-orange;
  }
  span {
    position: relative;
    z-index: 6;
  }
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 1000px;
  background-color: $mid-orange;
  z-index: 5;
  left: 0;
}

.activeSelection {
  cursor: auto;
  &:hover {
    color: $black;
  }
}

.singleMain {
  padding: 50px 40px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.ticketsLink {
  color: inherit;
  text-decoration: none;
  margin-right: 12px;
  font-weight: 400;
  &:hover {
    color: $orange;
  }
}

.breadcrumbs {
  display: flex;
  align-items: center;
  color: $black;
  font-size: 14px;
  font-weight: 600;
  .ticketId {
    white-space: nowrap;
  }
}

.content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 32px;
  margin-top: 21px;
  @media (max-width: 1300px) {
    grid-template-columns: 1fr;
  }
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.prevNext {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: flex-end;
}

.fullWidth {
  grid-column: 1 / 3;
}
