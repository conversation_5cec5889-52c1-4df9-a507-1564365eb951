import Modal from "../Modal";
import styles from "./port-in-error-modal.module.scss";

const PortInErrorModal = ({
  show,
  setShow,
  handleFinish,
  error,
  msisdn,
  zipCode,
  portingLineView,
  setShowPlans = null,
}: any) => {
  return (
    <Modal
      show={show}
      close={handleFinish}
      proceed={() => {
        if (portingLineView) {
          setShow("update-port");
        } else {
          if (setShowPlans) {
            setShowPlans(false);
          }
          setShow(false);
        }
      }}
      image="/robot_error.svg"
      saveButton="Update Port-in"
      clearContainer={!portingLineView}
      title="Port-in Failed"
      fullSize
      cancelButton="Close window"
    >
      <div className={styles.main}>
        <div className={styles.summaryContainer}>
          <div className={styles.summarySection}>
            <div className={styles.label}>Phone Number:</div>
            <div className={styles.data}>{msisdn}</div>
          </div>
          <div className={styles.summarySection}>
            <div className={styles.label}>ZIP Code:</div>
            <div className={styles.data}>{zipCode}</div>
          </div>
        </div>
        <div className={styles.message}>
          <div className={styles.heading} style={{ marginBottom: 8 }}>
            Fail Reason:
          </div>
          <div className={styles.text} style={{ marginBottom: 24 }}>
            {error || "No fail reason"}
          </div>
          <div className={styles.heading} style={{ marginBottom: 8 }}>
            Common fail reasons:
          </div>
          <div className={styles.text}>
            <ol>
              <li>Account ID from the other carrier is not correct.</li>
              <li>PIN (password) is not correct.</li>
              <li>Device is locked.</li>
              <li>Line has not been released by the other carrier.</li>
              <li>Subscriber has a combined account with another carrier.</li>
              <li>
                Subscriber is still paying for the device and must pay the
                remaining balance, so the other carrier can release the account.
              </li>
              <li>Device is registered as stolen</li>
            </ol>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default PortInErrorModal;
