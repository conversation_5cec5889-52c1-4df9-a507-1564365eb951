@use "../../styles/theme.scss" as *;

.portingProgress {
  padding: 24px;
  background: #f6f6f6;
  border-radius: 24px;
  margin-bottom: 12px;
  @media (max-width: 1420px) {
    margin-right: 12px;
  }
}

.box {
  height: 21px;
  width: 50px;
  border-radius: 4px;
}

.button {
  height: 50px;
  width: 100%;
  border-radius: 1000px;
}

.badge {
  height: 28px;
  width: 100px;
  border-radius: 6px;
}

.heading {
  width: 77px;
  height: 25px;
  border-radius: 4px;
  margin-right: 12px;
}

.box,
.button,
.badge,
.heading {
  background-color: $skeleton;
  position: relative;
  overflow: hidden;
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  .heading {
    font-weight: 600;
  }
}
.detailsGrid {
  display: grid;
  grid-template-columns: auto auto;
  grid-column-gap: 9px;
  grid-row-gap: 6px;
  justify-content: start;
}
.buttons {
  display: flex;
  margin-top: 12px;
}
