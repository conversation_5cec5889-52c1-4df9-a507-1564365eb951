@use "../../styles/theme.scss" as *;

.container {
  position: relative;
  display: flex;
  justify-content: center;
  overflow: visible;
  max-width: 100%;
  &:hover {
    .tooltip {
      opacity: 1;
    }
  }
}

.tooltip {
  position: absolute;
  padding: 7px 24px;
  z-index: 1001;
  background: $orange;
  color: $off-white;
  font-size: 14px;
  line-height: 21px;
  border-radius: 4px;
  top: -52px;
  //opacity: 0;
  pointer-events: none;
  transition: opacity 0.1s ease;
  text-align: center;
  white-space: nowrap;
  filter: drop-shadow(0px 4px 10px rgba(0, 0, 0, 0.15));
  .triangle {
    color: #f47d27;
    svg {
      position: absolute;
      bottom: -11.5px;
      margin-left: auto;
      margin-right: auto;
      left: 0;
      right: 0;
    }
  }
  &.white {
    background: #fff;
    color: #000;
    .triangle {
      color: #fff;
    }
  }
}
