@use "../../styles/theme.scss" as *;

.container {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  background: rgba(22, 11, 42, 0.6);
  @media (max-width: 768px) {
    align-items: flex-start;
    padding: 75px 0;
  }
}

.modal {
  max-height: 80vh;
  border-radius: 24px;
  background: #fff;
  display: flex;
  position: relative;
}

.main {
  width: 100%;
  padding: 81px 81px 74px 81px;
  overflow: auto;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  @media (max-width: 768px) {
    padding: 34px 24px 22px 24px;
  }
  .heading {
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    margin: 0 0 24px 0;
  }
  p {
    font-size: 16px;
    line-height: 24px;
    margin: 0 0 41px 0;
    text-align: center;
    max-width: 475px;
    width: 100%;
  }
}

.buttons {
  display: flex;
  width: 100%;
  justify-content: center;
  @media (max-width: 768px) {
    flex-direction: column-reverse;
    button {
      width: 100%;
      margin: 0 0 12px 0 !important;
    }
  }
}
