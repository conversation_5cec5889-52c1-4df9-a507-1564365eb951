import Shimmer from "../Shimmer";
import styles from "./plan-tile-loading.module.scss";

const PlanTileLoading = ({ numOfFields }: any) => {
  return (
    <>
      {Array.from({ length: numOfFields }).map(() => (
        <div>
          <div className={styles.name}>
            <Shimmer />
          </div>
          <div className={styles.data}>
            <Shimmer />
          </div>
        </div>
      ))}
    </>
  );
};

export default PlanTileLoading;
