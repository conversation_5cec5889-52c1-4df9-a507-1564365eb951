import Shimmer from "../Shimmer";
import styles from "./porting-loading.module.scss";

const PortingLoading = () => {
  return (
    <div className={styles.portingProgress}>
      <div className={styles.top}>
        <div className={styles.heading}>
          <Shimmer />
        </div>
        <div className={styles.badge}>
          <Shimmer />
        </div>
      </div>
      <div className={styles.detailsGrid}>
        <div className={styles.box}>
          <Shimmer />
        </div>
        <div className={styles.box}>
          <Shimmer />
        </div>
        <div className={styles.box}>
          <Shimmer />
        </div>
        <div className={styles.box}>
          <Shimmer />
        </div>
      </div>
      <div className={styles.buttons}>
        <div className={styles.button}>
          <Shimmer />
        </div>
      </div>
    </div>
  );
};

export default PortingLoading;
