import Button from "../Button";
import SendEmailModal from "../SendEmailModal";
import { PaperPlane } from "../svgs";
import styles from "./correspondence-tile.module.scss";
import { useState } from "react";

const CorrespondenceTile = () => {
  const [showSendEmail, setShowSendEmail] = useState(false);

  return (
    <>
      <SendEmailModal show={showSendEmail} setShow={setShowSendEmail} />
      <div className={styles.notes}>
        <div className={styles.title}>
          <h4>Correspondence</h4>
          <Button
            style={{ padding: 0, height: 24, fontSize: 14 }}
            color="tertiary"
            onClick={() => {
              setShowSendEmail(true);
            }}
          >
            <PaperPlane />
            Send New Email
          </Button>
        </div>
        <div className={styles.notesContainer}>
          <div className={`${styles.note} ${styles.from}`}>
            <div className={styles.top}>
              <div className={styles.subject}>Problem logging in</div>
              <div className={styles.date}>12:31 3 Apr 2023</div>
            </div>
            <div className={styles.noteContent}>
              I am writing to report an issue with logging in to my account. I
              have been using the same login credentials, but I am unable to
              access my account. I have also tried resetting my password several
              times, but I have not received any email with the password reset
              link. Can you please help me resolve this issue as soon as
              possible? I need to access my account urgently.
            </div>
          </div>
          <div className={`${styles.note} ${styles.to}`}>
            <div className={styles.top}>
              <div className={styles.subject}>Re: Problem logging in</div>
              <div className={styles.date}>14:03 4 Apr 2023</div>
            </div>
            <div className={styles.noteContent}>
              Hello Omar, <br /> <br />
              Thank you for contacting us regarding the login issue you are
              experiencing with your account. We apologize for the inconvenience
              caused and we will be happy to assist you with resolving this
              issue.
              <br />
              <br />
              Regards, Customer Support
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CorrespondenceTile;
