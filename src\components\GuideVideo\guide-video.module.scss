@use "../../styles/theme.scss" as *;

.videoCard {
  border-radius: 12px;
  background: #f2f2f2;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
  overflow: hidden;
  transition: box-shadow 0.15s ease;
  &:hover {
    box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.2);
  }
  .thumbnail {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    height: 171px;
    position: relative;
    .playButton {
      position: absolute;
      top: 32px;
      right: 32px;
    }
    video {
      width: 100%;
      height: 171px;
      object-fit: cover;
    }
  }
  .title {
    width: 100%;
    background: $orange;
    border-radius: 0px 0px 8px 8px;
    padding: 32px;
    font-size: 14px;
    line-height: 21px;
    font-weight: 600;
  }
}

.modalContainer {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 0 30px;
  background: rgba(0, 0, 0, 0.6);
  .modal {
    width: 90%;
    max-width: 1350px;
    border-radius: 24px;
    position: relative;
    .close {
      position: absolute;
      top: -21px;
      right: -21px;
      background-color: $orange;
      width: 42px;
      height: 42px;
      color: #fff;
      border-radius: 1000px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.1s ease;
      box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
      &:hover {
        background-color: $dark-orange;
      }
    }
    video {
      width: 100%;
      border-radius: 24px;
      box-shadow: rgba(0, 0, 0, 0.5) 0px 5px 15px;
      max-height: 90vh;
    }
  }
}
