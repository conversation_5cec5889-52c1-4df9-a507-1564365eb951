import Button from "../Button";
import styles from "./did-tile.module.scss";

const DIDTile = ({ singleTicket }: any) => {
  return (
    <>
      <div className={styles.notes}>
        <div className={styles.title}>
          <h4>Documents</h4>
          {singleTicket && <Buttons />}
        </div>
        <div className={styles.docsContainer}>
          <div className={styles.doc}>
            <div className={styles.docTitle}>Proof of Address</div>
            <img src="/bill_example.png" className={styles.docImage} />
          </div>
          <div className={styles.doc}>
            <div className={styles.docTitle}>Identity Document</div>
            <img src="/id_example.png" className={styles.docImage} />
          </div>
        </div>
      </div>
      {!singleTicket && <Buttons marginTop />}
    </>
  );
};

const Buttons = ({ marginTop }: any) => (
  <div
    style={{
      display: "flex",
      justifyContent: "flex-end",
      marginTop: marginTop ? 24 : 0,
    }}
  >
    <Button style={{ minWidth: "initial", marginRight: 12 }} color="secondary">
      Deny
    </Button>
    <Button style={{ minWidth: "initial" }}>Approve</Button>
  </div>
);

export default DIDTile;
