@use "../../styles/theme.scss" as *;

.main {
  box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.1);
  background: $off-white;
  min-height: 86px;
  display: grid;
  grid-template-columns: 104px 1fr 24px;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  max-width: 756px;
  padding-right: 37px;
  border-radius: 4px;
  z-index: 3000;
  pointer-events: all;
  .image {
    width: 100%;
  }
}

.imgContainer {
  height: 100%;
  width: 104px;
  display: flex;
  align-items: flex-end;
}

.close {
  background: none;
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
}

.message {
  font-size: 16px;
  line-height: 24px;
  color: $black;
  margin-right: auto;
}
