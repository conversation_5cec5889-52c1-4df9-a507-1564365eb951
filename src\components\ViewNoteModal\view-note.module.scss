@use "../../styles/theme.scss" as *;
@import "../../styles/mixins.module.scss";

@include animatedSelection;

.main {
  margin: auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-right: 15px;
  h3 {
    margin-bottom: 51px;
  }
}

.modalContent {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 450px;
  justify-content: center;
  background-color: #f7f6f6;
  border-radius: 10px;
  padding: 24px;
  margin-bottom: 12px;

  p {
    margin: 0 0 12px 0;
  }
}

.details {
  color: var(--primary-palette-black, #000014);
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}
.subdetails {
  color: #4d4d4d;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

// .contentContainer {
//   width: 100%;
//   display: flex;
//   flex-direction: column;
//   align-items: center;
// }

// .plansContainer {
//   display: grid;
//   // width: 100%;
//   grid-template-columns: 1fr 1fr 1fr;
//   grid-column-gap: 12px;
//   grid-row-gap: 12px;
//   align-items: center;
// }
