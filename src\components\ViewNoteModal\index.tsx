import styles from "./view-note.module.scss";
import Modal from "../Modal";
import { formatDateWithTime } from "../utils/formatDate";
import { useState, useEffect } from "react";

const ViewNoteModal = ({ show, setShow, item, clearContainer }: any) => {
  const [formatted, setFormatted] = useState({
    name: "",
    body: "",
  });

  useEffect(() => {
    if (item) {
      let split, name, body;

      if (item.text.includes(" || ")) {
        split = item.text.split(" || ");
        name = split[0];
        body = split.slice(1).join(" || ");
      } else {
        name = "";
        body = item.text;
      }

      setFormatted({
        name: name,
        body: body,
      });
    }
  }, [item]);

  return (
    <Modal
      cancelButton="Close Window"
      image="/view-user-Illustration.svg"
      show={show}
      setShow={setShow}
      close={setShow}
      onClose={() => setShow(false)}
      clearContainer={clearContainer}
    >
      <div className={`${styles.main} `}>
        <h3>Note details</h3>
        <div className={styles.modalContent}>
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className={styles.subdetails}>
              By: <span className={styles.details}>{formatted.name}</span>
            </div>
            <div className={styles.subdetails}>
              {formatDateWithTime(new Date(item?.createdDate))}
            </div>
          </div>

          <p
            style={{
              fontSize: "14px",
              fontWeight: "400",
              whiteSpace: "pre-line",
            }}
          >
            {formatted.body}
          </p>
        </div>
      </div>
    </Modal>
  );
};

export default ViewNoteModal;
