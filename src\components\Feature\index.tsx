import Checkbox from "../Checkbox";
import styles from "./feature.module.scss";

const Feature = ({ feature, activeFeature, setActiveFeature }: any) => {
  return (
    <div className={styles.main}>
      <Checkbox
        checked={activeFeature.some(
          (item: any) => item.description === feature.featureName
        )}
        onClick={() => {
          if (
            activeFeature.some(
              (item: any) => item.description === feature.featureName
            )
          ) {
            let removed = activeFeature.filter(
              (i: any) => i.description !== feature.featureName
            );
            setActiveFeature(removed);
          } else {
            let newFeature = {
              description: feature.featureName,
              code: feature.featureCode,
            };
            setActiveFeature([...activeFeature, newFeature]);
          }
        }}
      />
      <div>
        <div className={styles.name}>{feature.featureName}</div>
        <div className={styles.description}>{feature.featureDescription}</div>
      </div>
    </div>
  );
};

export default Feature;
