@use "../../styles/theme.scss" as *;

.main {
  margin: 0 auto;
  width: 100%;
  max-width: 350px;
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
}

.twoFactor {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  border-radius: 8px;
  border: 1px solid #cacaca;
  padding: 0 9px 0 16px;
  margin-top: 12px;
}

.toggleContainer {
  display: flex;
  align-items: center;
  .onOff {
    margin-right: 8px;
  }
}
