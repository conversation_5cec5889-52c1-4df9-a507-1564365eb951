@mixin animatedSelection {
  .selectionWrapper {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
    h2 {
      font-size: 20px;
      line-height: 30px;
      font-weight: 700;
      margin-right: 24px;
    }
  }

  .selection {
    height: 47px;
    border-radius: 1000px;
    color: $black;
    font-size: 14px;
    padding: 0 24px;
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    &:hover {
      color: $dark-orange;
    }
    span {
      position: relative;
      z-index: 6;
    }
  }

  .background {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 1000px;
    background-color: $light-orange;
    z-index: 5;
    left: 0;
  }

  .activeSelection {
    text-shadow: 0px 0px 0.5px $black;
    cursor: auto;
    &:hover {
      color: $black;
    }
  }
}

@mixin resetBtn {
  border: none;
  background: none;
  padding: 0;
  color: var(--orange);
  cursor: pointer;
  font-weight: 600;
  padding: 0;
  font-size: 16px;
  line-height: 24px;
  transition: color 0.1 ease;
  &:hover {
    color: var(--dark-orange);
  }
}
