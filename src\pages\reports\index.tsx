import { useParams } from "react-router-dom";
import styles from "../../styles/reports.module.scss";
import { useDispatch } from "react-redux";
import { Calendar, Export } from "../../components/svgs";
import Button from "../../components/Button";
import { useEffect, useMemo, useState } from "react";
import { motion } from "framer-motion";
import { Fade } from "@mui/material";
import UserSkeleton from "../../components/UserSkeleton";
import { padArrayToLength } from "../../components/utils/padArray";
import {
  churnReportFields,
  mdnChangeReportFields,
  paymentsFields,
  subscriptionReportFields,
  throttleReportFields,
} from "../../components/utils/reportFields";
import Pagination from "../../components/Pagination";
import { SwitchTransition, CSSTransition } from "react-transition-group";
import { exportCsv } from "../../components/utils/exportCsv";
import { ApiPostAuth } from "../api/api";
import moment from "moment";
import DatePicker from "../../components/DatePicker";
import { formatDate } from "../../components/utils/dateHelpers";
import UserMenu from "../../components/UserMenu";
import PaymentsSection from "./payments-section";

const Reports = () => {
  const { mvnoId } = useParams();
  const dispatch = useDispatch();

  const [selection, setSelection] = useState("last-7-days");

  const [dateRange, setDateRange] = useState({
    start: null,
    end: null,
  } as any);

  useEffect(() => {
    if (dateRange.start && dateRange.end) {
      setSelection("custom");
    }
  }, [dateRange]);

  useEffect(() => {
    if (selection !== "custom") {
      setDateRange({
        start: null,
        end: null,
      });
    }
  }, [selection]);

  const [showReport, setShowReport] = useState(false);

  const itemsPerPage = 9;

  const [loading, setLoading] = useState(true);

  const [reportData, setReportData] = useState([] as any);

  const [currentPage, setCurrentPage] = useState(1);

  const getDateRange = () => {
    let start, end;
    switch (selection) {
      case "last-7-days":
        start = moment().subtract(7, "d").format("YYYY-MM-DD");
        end = moment().format("YYYY-MM-DD");
        break;
      case "last-14-days":
        start = moment().subtract(14, "d").format("YYYY-MM-DD");
        end = moment().format("YYYY-MM-DD");
        break;
      case "last-month":
        start = moment().subtract(1, "months").format("YYYY-MM-DD");
        end = moment().format("YYYY-MM-DD");
        break;
      case "custom":
        start = moment(dateRange.start).format("YYYY-MM-DD");
        end = moment(dateRange.end).format("YYYY-MM-DD");
        break;
    }
    return {
      startDate: start,
      endDate: end,
    };
  };

  const handleCreateReport = () => {
    if (showReport) {
      setShowReport(false);
      setLoading(true);
      setCurrentPage(1);
    } else {
      setShowReport(true);
      setLoading(true);
      ApiPostAuth(
        `/reporting/generate/${typeSelection}/${mvnoId}`,
        getDateRange(),
      )
        .then((response) => {
          setReportData(extractEntriesByType(response.data, typeSelection));
          setLoading(false);
        })
        .catch((error) => {
          setShowReport(false);
          setLoading(false);
          dispatch({
            type: "notify",
            payload: {
              error: true,
              message: error.response.data.message,
            },
          });
        });
    }
  };

  const [typeSelection, setTypeSelection] = useState("subscription");

  useEffect(() => {
    setLoading(true);
    setShowReport(false);
    setReportData([]);
    setCurrentPage(1);
  }, [typeSelection]);

  const reportTypes = [
    {
      label: "Subscription details",
      key: "subscription",
    },
    {
      label: "Churn",
      key: "churn",
    },
    {
      label: "Throttle",
      key: "throttle",
    },
    {
      label: "Payments",
      key: "payments",
    },
    {
      label: "MDN Change",
      key: "mdnchange",
    },
  ];

  const sortedReportData = useMemo(
    () =>
      reportData.slice().sort((a: any, b: any) => {
        if (a?.updatedAt && b?.updatedAt) {
          return (
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          );
        } else {
          return 0;
        }
      }),
    [reportData],
  );

  const getFieldsByType = (type: string) => {
    switch (type) {
      case "subscription":
        return subscriptionReportFields;
      case "churn":
        return churnReportFields;
      case "throttle":
        return throttleReportFields;
      case "mdnchange":
        return mdnChangeReportFields;
      default:
        return [];
    }
  };

  const getNoOfStandardsByType = (type: string) => {
    switch (typeSelection) {
      case "subscription":
        return subscriptionReportFields.length;
      case "churn":
        return churnReportFields.length;
      case "throttle":
        return throttleReportFields.length;
      case "mdnchange":
        return mdnChangeReportFields.length;
      default:
        return 7;
    }
  };

  return (
    <div className={styles.main}>
      <div className={styles.topBar}>
        <h2>Reports</h2>
        <UserMenu />
      </div>
      <div className={styles.selectionWrapper}>
        {reportTypes.map((type: any) => (
          <div
            className={`${styles.selection} ${
              typeSelection === type.key && styles.activeSelection
            }`}
            onClick={() => {
              setTypeSelection(type.key);
              setReportData([]);
            }}
          >
            <span>{type.label}</span>
            {typeSelection === type.key && (
              <motion.div className={styles.background} layoutId="underline" />
            )}
          </div>
        ))}
      </div>
      <SwitchTransition>
        <CSSTransition
          key={typeSelection === "payment" ? "payments" : "other"}
          addEndListener={(node, done) =>
            node.addEventListener("transitionend", done, false)
          }
          classNames="fade"
        >
          {typeSelection === "payments" ? (
            <PaymentsSection typeSelection={typeSelection} />
          ) : (
            <div className={styles.mainTile}>
              <div className={styles.utilityBar}>
                <div
                  className={`${styles.fadeButtons} ${showReport && styles.hide}`}
                >
                  <div
                    className={`${styles.button} ${
                      selection === "last-7-days" && styles.active
                    }`}
                    onClick={() => {
                      setSelection("last-7-days");
                    }}
                    color="secondary"
                  >
                    Last 7 days
                  </div>
                  <div
                    className={`${styles.button} ${
                      selection === "last-14-days" && styles.active
                    }`}
                    onClick={() => {
                      setSelection("last-14-days");
                    }}
                    style={{ marginLeft: 12 }}
                    color="secondary"
                  >
                    Last 14 days
                  </div>
                  <div
                    className={`${styles.button} ${
                      selection === "last-month" && styles.active
                    }`}
                    onClick={() => {
                      setSelection("last-month");
                    }}
                    style={{ marginLeft: 12, marginRight: 12 }}
                    color="secondary"
                  >
                    Last month
                  </div>
                  <DatePicker
                    label={
                      <div
                        className={`${styles.button} ${
                          selection === "custom" && styles.active
                        }`}
                        color="secondary"
                      >
                        Custom <Calendar />{" "}
                        {selection === "custom" &&
                        dateRange.start &&
                        dateRange.end
                          ? `${formatDate(dateRange.start)} - ${formatDate(
                              dateRange.end,
                            )}`
                          : "DD/MM/YY - DD/MM/YY"}
                      </div>
                    }
                    masterFrom={dateRange.start}
                    masterUntil={dateRange.end}
                    onChange={(newFrom: Date, newUntil: Date) => {
                      setDateRange({
                        start: newFrom,
                        end: newUntil,
                      });
                    }}
                    reports
                  />
                </div>
                <Button
                  style={{
                    height: "43px",
                    minWidth: "initial",
                    padding: "0px 24px",
                    fontSize: "16px",
                    lineHeight: "initial",
                  }}
                  onClick={handleCreateReport}
                >
                  {showReport ? "Clear Report" : "Run Report"}
                </Button>
                <Fade in={showReport && !loading}>
                  <div style={{ marginLeft: "auto" }}>
                    <Button
                      disabled={
                        !showReport || loading || reportData.length === 0
                      }
                      style={{ minWidth: "initial" }}
                      onClick={() => {
                        if (typeSelection === "subscription") {
                          exportCsv(
                            subscriptionReportFields,
                            reportData,
                            dispatch,
                          );
                        } else if (typeSelection === "churn") {
                          exportCsv(churnReportFields, reportData, dispatch);
                        } else if (typeSelection === "payments") {
                          exportCsv(paymentsFields, reportData, dispatch);
                        } else if (typeSelection === "throttle") {
                          exportCsv(throttleReportFields, reportData, dispatch);
                        } else if (typeSelection === "mdnchange") {
                          exportCsv(
                            mdnChangeReportFields,
                            reportData,
                            dispatch,
                            "mdn_change_report",
                          );
                        } else {
                          console.error("Report Export: Selection not valid!");
                        }
                      }}
                    >
                      <Export /> Export
                    </Button>
                  </div>
                </Fade>
              </div>
              <SwitchTransition>
                <CSSTransition
                  key={showReport ? "showing-report" : "not-showing"}
                  addEndListener={(node, done) =>
                    node.addEventListener("transitionend", done, false)
                  }
                  classNames="fade"
                >
                  {showReport ? (
                    <div className={styles.content}>
                      <div className={`${styles.tableContainer} table-scroll`}>
                        <table>
                          <thead>
                            <tr>
                              {getFieldsByType(typeSelection).map((field) => (
                                <th>{field.label}</th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {!loading ? (
                              sortedReportData.length !== 0 ? (
                                padArrayToLength(
                                  sortedReportData.slice(
                                    (currentPage - 1) * itemsPerPage,
                                    currentPage * itemsPerPage,
                                  ),
                                  itemsPerPage,
                                  null,
                                ).map((item: any, index: number) => {
                                  if (item === null) {
                                    return (
                                      <tr
                                        key={`blank-row-${index}`}
                                        style={{
                                          visibility: "hidden",
                                          pointerEvents: "none",
                                        }}
                                      ></tr>
                                    );
                                  } else {
                                    return (
                                      <tr key={"report-row-" + index}>
                                        {typeSelection === "subscription"
                                          ? subscriptionReportFields.map(
                                              (field: any) => {
                                                let value = item[field.key];
                                                if (
                                                  field.key === "offerName" ||
                                                  field.key === "offerSize"
                                                ) {
                                                  if (
                                                    item?.reportPlans.length
                                                  ) {
                                                    value =
                                                      item?.reportPlans[0][
                                                        field.key
                                                      ];
                                                  } else {
                                                    value = item[field.key];
                                                  }
                                                }
                                                if (
                                                  value &&
                                                  ["usageMb", "usageGb"].some(
                                                    (k) => k === field.key,
                                                  )
                                                ) {
                                                  value =
                                                    parseFloat(value).toFixed(
                                                      2,
                                                    );
                                                }
                                                return <td>{value || "-"}</td>;
                                              },
                                            )
                                          : typeSelection === "churn"
                                            ? churnReportFields.map(
                                                (field: any) => (
                                                  <td>
                                                    {item[field.key] || "-"}
                                                  </td>
                                                ),
                                              )
                                            : typeSelection === "mdnchange"
                                              ? mdnChangeReportFields.map(
                                                  (field: any) => (
                                                    <td>
                                                      {item[field.key] || "-"}
                                                    </td>
                                                  ),
                                                )
                                              : throttleReportFields.map(
                                                  (field: any) => {
                                                    let value = item[field.key];
                                                    if (
                                                      value &&
                                                      [
                                                        "usageMb",
                                                        "usageGb",
                                                      ].some(
                                                        (k) => k === field.key,
                                                      )
                                                    ) {
                                                      value =
                                                        parseFloat(
                                                          value,
                                                        ).toFixed(2);
                                                    }
                                                    return (
                                                      <td>{value || "-"}</td>
                                                    );
                                                  },
                                                )}
                                      </tr>
                                    );
                                  }
                                })
                              ) : (
                                <tr style={{ background: "none" }}>
                                  <td colSpan={12}>
                                    <div className={styles.noneFound}>
                                      <img src="/none_found.svg" />
                                      <h3>
                                        No entries found for this time period
                                      </h3>
                                    </div>
                                  </td>
                                </tr>
                              )
                            ) : (
                              Array.from(
                                { length: itemsPerPage },
                                (v, i) => i,
                              ).map((i) => (
                                <UserSkeleton
                                  key={"report-skeleton-" + i}
                                  noOfStandard={getNoOfStandardsByType(
                                    typeSelection,
                                  )}
                                />
                              ))
                            )}
                          </tbody>
                        </table>
                      </div>
                      <div className={styles.pagination}>
                        <Pagination
                          currentPage={currentPage}
                          setCurrentPage={setCurrentPage}
                          numberOfPages={Math.ceil(
                            reportData.length / itemsPerPage,
                          )}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className={styles.content}>
                      <img src="/desk_scene.svg" className={styles.office} />
                    </div>
                  )}
                </CSSTransition>
              </SwitchTransition>
            </div>
          )}
        </CSSTransition>
      </SwitchTransition>
    </div>
  );
};

export default Reports;

function extractEntriesByType(data: any, type: string) {
  if (type === "mdnchange") {
    return data;
  } else {
    return data.entries;
  }
}
