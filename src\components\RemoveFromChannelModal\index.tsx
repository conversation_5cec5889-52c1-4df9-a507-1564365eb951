import styles from "./remove-from-channel-modal.module.scss";
import Modal from "../Modal";
import { Delete } from "../svgs";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { ApiDelete } from "../../pages/api/api";
import { useParams } from "react-router-dom";

const RemoveFromChannelModal = ({
  show,
  setShow,
  subscriber,
  repopulate,
}: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);

  const deleteUser = () => {
    setLoading(true);
    ApiDelete(
      `/channels/${subscriber?.channelId}/subscribers/${subscriber.mid}`,
      {},
    )
      .then((response) => {
        setLoading(false);
        setShow(false);
        dispatch({
          type: "notify",
          payload: {
            error: false,
            message: response.data.message,
          },
        });
        repopulate();
      })
      .catch((error) => {
        setLoading(false);
        dispatch({
          type: "notify",
          payload: {
            error: true,
            message: error.response.data.message,
          },
        });
      });
  };

  return (
    <Modal
      saveButton="Yes, remove from channel"
      image="/delete_user_graphic.svg"
      show={show}
      close={() => {
        setShow(false);
      }}
      proceed={deleteUser}
      loading={loading}
    >
      <div className={styles.main}>
        <h3>
          Are you sure you want to remove{" "}
          {subscriber && subscriber.subscriberName} from the{" "}
          {subscriber && subscriber.channelName} Channel?
        </h3>
      </div>
    </Modal>
  );
};

export default RemoveFromChannelModal;
