import { useEffect, useState } from "react";
import { Input } from "../Input";
import Modal from "../Modal";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import styles from "./create-ticket-modal.module.scss";
import Assignee from "../Assignee";
import Category from "../Category";
import Priority from "../Priority";
import SelectDropdown from "../SelectDropdown";
import StatusBadge from "../StatusBadge";
import SingleDatePicker from "../SingleDatePicker";
import { validateAll } from "indicative/validator";
import { ApiGet, ApiPost, ApiPostAuth } from "../../pages/api/api";
import { useParams } from "react-router-dom";
import TextArea from "../TextArea";
import FileInput from "../FileInput";
import { Collapse } from "@mui/material";
import ColouredSelect from "../ColouredSelect";
import TicketType from "../TicketType";
import { useDispatch } from "react-redux";
import { isDateNow, zeroPad } from "../utils/dateHelpers";
import { useAgents } from "../utils/getAgents";
import { categories, priorities } from "../utils/tickets";
import moment from "moment";

const leftFields = ["subject", "description"];

const rightFields = ["agentId", "category", "status", "priority", "dueDate"];

const fields = [...leftFields, ...rightFields];
const rules = getRules(fields);
const messages = getMessages(fields);

const selectFields = ["agentId", "priority", "category"];
const colouredSelectFields = ["status"];

const CreateTicketModal = ({ show, setShow, populate, ticket, mode }: any) => {
  const [data, setData] = useState(createStateObject(fields));
  const dispatch = useDispatch();
  const { agents, GetAgents } = useAgents();
  const { mvnoId } = useParams();

  const options = {
    agentId: agents.map((agent: any) => {
      return {
        label: agent.name,
        value: agent.id,
      };
    }),
    category: categories.map((item: string) => ({ label: item, value: item })),
    status: [
      {
        label: "Open",
        key: 2,
      },
      {
        label: "Pending",
        key: 3,
      },
      {
        label: "Resolved",
        key: 4,
      },
      {
        label: "Closed",
        key: 5,
      },
    ],
    priority: [
      {
        label: <Priority priority="Urgent" />,
        value: 4,
      },
      {
        label: <Priority priority="High" />,
        value: 3,
      },
      {
        label: <Priority priority="Medium" />,
        value: 2,
      },
      {
        label: <Priority priority="Low" />,
        value: 1,
      },
    ],
  } as any;
  useEffect(() => {
    setLoading(true);
    show && !agents.length && GetAgents();
    if (mode === "edit") {
      setData({
        ...data,
        subject: ticket.subject,
        description: ticket.description,
        status: ticket.status,
        category: { label: ticket.category, value: ticket.category },

        agentId: {
          label: agents?.find((agent: any) => agent.id === ticket.agentId)
            ?.name,
          value: ticket.agentId,
        },
        priority: {
          label: (
            <Priority
              priority={priorities.find((p) => p.id === ticket.priority)?.name}
            />
          ),
          value: ticket.priority,
        },
        dueDate: new Date(ticket.dueDate),
      });
    }
    setLoading(false);
  }, [show, mode, agents]);

  const [loading, setLoading] = useState(false);

  const createTicket = () => {
    const testData = {
      subject: data.subject,
      description: data.description,
      status: data.status,
      category: data.category.value,
      agentId: data.agentId.value,
      priority: data.priority.value,
      dueBy:
        data.status === 3 ||
        data.status === 4 ||
        data.status === 5 ||
        ticket?.status === 3 ||
        ticket?.status === 4 ||
        ticket?.status === 5
          ? mode === "edit"
            ? ticket.dueDate
            : null
          : data.dueDate,
      mvnoId: parseInt(mvnoId as any),
    };

    if (
      testData.dueBy &&
      typeof testData.dueBy !== "string" &&
      isDateNow(testData.dueBy)
    ) {
      const now = new Date();
      const day = now.getUTCDate();
      const month = now.getUTCMonth() + 1;
      const year = now.getUTCFullYear();
      testData.dueBy = new Date(
        `${year}-${zeroPad(month)}-${zeroPad(day)}T23:59:59Z`,
      );
    }

    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        {
          mode === "edit"
            ? ApiPostAuth(`/tickets/update/${ticket.id}`, testData)
                .then((response: any) => {
                  setLoading(false);
                  populate();
                  dispatch({
                    type: "notify",
                    payload: {
                      message: "Your changes have been saved.",
                      error: false,
                    },
                  });
                  setShow(false);
                })
                .catch((error) => {
                  setLoading(false);
                  dispatch({
                    type: "notify",
                    payload: {
                      error: true,
                      message: error.response.data.message,
                    },
                  });
                })
            : ApiPostAuth("/tickets/create", testData)
                .then((response: any) => {
                  setLoading(false);
                  setShow(false);
                  setTimeout(reset, 300);
                  populate();
                  dispatch({
                    type: "notify",
                    payload: {
                      error: false,
                      message:
                        "Ticket created successfully! It may take a few minutes to appear in the ticket list",
                    },
                  });
                })
                .catch((error) => {
                  setLoading(false);
                  dispatch({
                    type: "notify",
                    payload: {
                      error: true,
                      message: error.response.data.message,
                    },
                  });
                });
        }
      })
      .catch((errors) => {
        displayErrors(errors, setData);
        setLoading(false);
      });
  };

  const handleSelectChange = (prop: string, option: any) => {
    setData({
      ...data,
      [prop]: option,
      errors: {
        ...data.errors,
        [prop]: "",
      },
    });
  };

  const reset = () => {
    setData(createStateObject(fields));
  };

  const [shouldShowDueDate, setShouldShowDueDate] = useState(true);

  useEffect(() => {
    if (ticket) {
      if (ticket.status === 2 && data.status === 2) {
        setShouldShowDueDate(true);
      } else {
        setShouldShowDueDate(false);
      }
    } else {
      setShouldShowDueDate(true);
    }
  }, [data, ticket]);

  return (
    <Modal
      show={show}
      setShow={setShow}
      image="/orange_robot_narrow.svg"
      fullSize
      title={mode === "edit" ? <>Edit Ticket</> : <>Create New Ticket</>}
      saveButton={mode === "edit" ? "Save" : "Create Ticket"}
      close={() => {
        setShow(false);
        setTimeout(reset, 300);
      }}
      proceed={createTicket}
      loading={loading}
      imageStyle
    >
      <div className={styles.main}>
        <div className={styles.col}>
          {leftFields.map((prop: string) => {
            if (prop === "subject") {
              return (
                <Input
                  white
                  key={prop}
                  label={labels[prop]}
                  placeholder={placeholders[prop]}
                  value={data[prop]}
                  onChange={(e: any) => {
                    handleInputChange(prop, e, data, setData);
                  }}
                  error={data.errors[prop]}
                  clear={() => {
                    clearInput(prop, setData);
                  }}
                  disabled={loading}
                  onKeyDown={createTicket}
                />
              );
            } else {
              return (
                <TextArea
                  white
                  key={prop}
                  label={labels[prop]}
                  placeholder={placeholders[prop]}
                  value={data[prop]}
                  onChange={(e: any) => {
                    handleInputChange(prop, e, data, setData);
                  }}
                  error={data.errors[prop]}
                  clear={() => {
                    clearInput(prop, setData);
                  }}
                  disabled={loading}
                  autoHeight
                />
              );
            }
          })}
        </div>
        <div className={styles.col}>
          {rightFields.map((prop: string) => {
            if (selectFields.includes(prop)) {
              return (
                <SelectDropdown
                  dropDownMaxHeight={
                    prop === "priority" ? 150 : prop === "category" ? 250 : ""
                  }
                  key={prop}
                  value={data[prop]}
                  error={data.errors[prop]}
                  onChange={(option: any) => {
                    handleSelectChange(prop, option);
                  }}
                  options={options[prop]}
                  disabled={loading}
                  placeholder={placeholders[prop]}
                />
              );
            } else if (colouredSelectFields.includes(prop)) {
              return (
                <ColouredSelect
                  error={data.errors[prop]}
                  label={placeholders[prop]}
                  options={options[prop]}
                  onChange={(option: any) => {
                    handleSelectChange(prop, option);
                  }}
                  selected={data[prop]}
                  disabled={loading}
                  readonly={prop === "status" && mode !== "edit"}
                  status={prop === "status"}
                />
              );
            } else if (prop === "dueDate") {
              return (
                <Collapse in={shouldShowDueDate}>
                  <SingleDatePicker
                    style={{
                      justifyContent: "space-between",
                      flexDirection: "row-reverse",
                    }}
                    key={prop}
                    date={data[prop]}
                    setDate={(date: any) => {
                      handleSelectChange(prop, date);
                    }}
                    label={labels[prop]}
                    future
                    large
                    error={data.errors[prop]}
                    disabled={loading}
                  />
                  <div style={{ height: 12 }} />
                </Collapse>
              );
            } else {
              return (
                <Input
                  key={prop}
                  label={labels[prop]}
                  placeholder={placeholders[prop]}
                  value={data[prop]}
                  onChange={(e: any) => {
                    handleInputChange(prop, e, data, setData);
                  }}
                  error={data.errors[prop]}
                  clear={() => {
                    clearInput(prop, setData);
                  }}
                  disabled={loading}
                  onKeyDown={createTicket}
                />
              );
            }
          })}
          {/*<FileInput />*/}
        </div>
      </div>
    </Modal>
  );
};

export default CreateTicketModal;
