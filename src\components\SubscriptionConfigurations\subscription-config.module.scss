@use "../../styles/theme.scss";

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

.grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 18px;
  grid-row-gap: 18px;
}

.panel {
  min-height: 420px;
  width: 100%;
  background: #fff;
  border-radius: 24px;
  padding: 32px;
}

.top {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 22px;
}

.featuresContainer {
  display: flex;
  flex-wrap: wrap;
  .feature {
    margin: 0px 8px 8px 0px;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 14px;
    line-height: 21px;
    background-color: #f0f5ff;
    color: #1a1a1a;
  }

  .bolton {
    cursor: pointer;
  }

  .bolton:hover {
    background-color: #dbe7ff;
  }
}

.title {
  font-weight: 600;
}
