import Button from "../Button";
import TicketSidebarTile from "../TicketSidebarTile";
import { ArrowRight } from "../svgs";
import styles from "./ticket-page-summary.module.scss";

const TicketPageSummary = ({
  ticket,
  horizontal,
  handleTicketUpdate,
  assignees,
}: any) => {
  return (
    <div className={`${styles.main} ${horizontal && styles.horizontal}`}>
      <div className={styles.title}>
        {/*<div className={styles.customerInfo}>
          <div>
            <div className={styles.name}>{ticket.name}</div>
            <div className={styles.email}>{ticket.email}</div>
          </div>
          <div className={styles.customerSummary} id="customer-summary">
            Subscriber Details <ArrowRight />
          </div>
  </div>*/}
      </div>
      <div className={styles.contentContainer}>
        <TicketSidebarTile
          ticket={ticket}
          noBackground
          handleTicketUpdate={handleTicketUpdate}
          assignees={assignees}
        />
      </div>
    </div>
  );
};

export default TicketPageSummary;
