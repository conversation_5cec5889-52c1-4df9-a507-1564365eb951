import FilePreview from "../FilePreview";
import { UploadCloud } from "../svgs";
import styles from "./file-input.module.scss";
import { useRef, useState, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";
import { useDispatch } from "react-redux";

const FileInput = () => {
  const fileInput = useRef(null);
  const dispatch = useDispatch();

  const maxFileSizeMb = 20;

  const preventDefaults = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const FileToBase64 = (file: any) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  };

  const handleFileChange = (files: any) => {
    let oversizedFile = false;
    Array.from(files).forEach(async (fileItem: any) => {
      if (fileItem.size > maxFileSizeMb * 1024 * 1024) {
        oversizedFile = true;
      } else {
        let base64File = await FileToBase64(fileItem);
        const fileObj = {
          id: uuidv4(),
          name: fileItem.name,
          file: base64File,
          body: fileItem,
        };
        setFileStore((prev: any) => [...prev, fileObj]);
      }
    });

    if (oversizedFile) {
      dispatch({
        type: "notify",
        payload: {
          error: true,
          message: "Maximum file size of 20MB, some files were not uploaded",
        },
      });
    }
  };

  const [fileStore, setFileStore] = useState([] as any);

  useEffect(() => {
    console.log(fileStore);
  }, [fileStore]);

  const triggerFileClick = () => {
    (fileInput.current as any)?.click();
  };

  return (
    <div>
      <div
        className={`${styles.container} ${
          fileStore.length > 0 && styles.filled
        }`}
      >
        <input
          ref={fileInput}
          type="file"
          multiple
          onChange={(e: any) => {
            handleFileChange(e.target.files);
          }}
          style={{ display: "none" }}
        />
        {fileStore.map((fileData: any) => (
          <FilePreview file={fileData} setFileStore={setFileStore} />
        ))}
        <div
          className={`${styles.inputMain} ${
            fileStore.length > 0 && styles.shrink
          }`}
          onDrop={(e: any) => {
            preventDefaults(e);
            handleFileChange(e.dataTransfer.files);
          }}
          onDragOver={preventDefaults}
          onClick={triggerFileClick}
        >
          <UploadCloud />
          Drag & drop or <span className={styles.bold}>
            &nbsp;browse&nbsp;
          </span>{" "}
          your files
        </div>
      </div>
      <div className={styles.info}>
        <div />
        <div>Maximum size: 20MB</div>
      </div>
    </div>
  );
};

export default FileInput;
