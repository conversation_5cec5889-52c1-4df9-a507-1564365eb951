@use "./theme.scss" as *;

.main {
  padding: 23px 40px 50px 40px;
  @media (max-width: 1250px) {
    padding: 24px;
  }
}

.logsMain {
  padding: 50px 40px;
  height: 100vh;
  overflow: hidden;
}

.topBar {
  display: flex;
  margin-bottom: 47px;
}

.headingSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
  margin-bottom: 24px;
  @media (max-width: 1263px) {
    flex-direction: column;
    align-items: flex-start;
    .leftWrapper {
      margin-bottom: 24px;
    }
    .buttons {
      margin-left: auto;
    }
  }
  .leftWrapper {
    .stats {
      display: flex;
      align-items: center;
      .item {
        display: flex;
        align-items: center;
        margin-right: 40px;
        font-size: 16px;
        line-height: 24px;
        .label {
          color: #666666;
          margin-right: 8px;
        }
      }
    }
  }
  h2 {
    margin: 0;
    margin-bottom: 4px;
    font-weight: 700;
    font-size: 20px;
    line-height: 30px;
  }
  .activeSubCount {
    font-size: 14px;
    font-weight: 500;
    margin-left: 8px;
    margin-right: 12px;
    color: #666666;
  }
  .activeCountSkel {
    height: 24px;
    width: 60px;
    background-color: $skeleton;
    border-radius: 6px;
    position: relative;
    overflow: hidden;
    margin-left: 8px;
    margin-right: 12px;
  }
  .buttons {
    display: flex;
    align-items: center;
  }
}

.tableTile,
.tableContainer {
  overflow-x: auto;
  padding-bottom: 5px;
}

.tableTile,
.logsTile {
  background: #fff;
  padding: 24px 14px;
  border-radius: 24px;
  @media (max-width: 1250px) {
    padding: 24px;
  }
}

.logsTile {
  overflow-y: scroll;
  height: auto;
  max-height: calc(100vh - 228px);
  padding: 40px !important;
  border-radius: 24px 12px 12px 24px;
}

.subscriberTable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0px;
  white-space: nowrap;
  tbody {
    tr {
      height: 45px;
      background: #f2f2f2;
      cursor: pointer;
      position: relative;
      &:nth-child(odd) {
        background: #fff;
      }
      &.hidden {
        background: none;
        visibility: hidden;
      }
      td {
        font-size: 14px;
        font-weight: 400;
        line-height: 21px;
        padding: 0 10px;
        vertical-align: top;
      }
      td:first-child {
        border-radius: 16px 0 0 16px;
        padding-left: 24px;
      }
      td:last-child {
        border-radius: 0 16px 16px 0;
        padding-right: 24px;
      }
    }
    &:before {
      content: "@";
      display: block;
      line-height: 4px;
      text-indent: -99999px;
    }
  }
  thead {
    tr {
      th {
        vertical-align: top;
        padding-left: 10px;
        padding-right: 10px;
      }
      th:first-child {
        padding-left: 24px;
      }
    }
  }
  th {
    font-size: 14px;
    font-weight: 600;
    line-height: 21px;
    text-align: start;
    border-bottom: 1px solid #0000141f;
    padding-bottom: 8px;
  }
}

.tdBox {
  height: 45px;
  display: flex;
  align-items: center;
}

.paginationContainer {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 25px;
  margin-bottom: 55px;
}

.nameHeading {
  display: flex;
  align-items: center;
  h3 {
    margin: 0 22px 0 0;
    font-size: 24px;
    line-height: 36px;
  }
}

.actionButton {
  background: none;
  border: none;
  width: 44px;
  height: 41px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.15s ease;
  border-radius: 8px;
  &:hover {
    background: #fff;
  }
}

.noneFound {
  display: grid;
  width: 100%;
  min-height: 510px;
  align-items: center;
  justify-content: center;
  padding-top: 55px;
  img,
  .content {
    grid-area: 1 / 1 / 2 / 2;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
  }
  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  h3 {
    width: 100%;
    text-align: center;
    margin-bottom: 24px;
  }
}

.backLink {
  display: flex;
  justify-content: flex-start;
}

.subscriberTile {
  width: 100%;
  background: #fff;
  border-radius: 24px;
  padding: 50px;
}

.dataGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-row-gap: 21px;
  grid-column-gap: 21px;
  margin-top: 21px;
  margin-bottom: 30px;
}

.plansGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 29px;
}

.plansTile {
  width: 100%;
  background: #f7f6f6;
  border-radius: 24px;
  padding: 34px 32px;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    h4 {
      margin: 0;
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
    }
  }
}

.removeFilters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: -38px;
  margin-bottom: 24px;
  padding-left: 24px;
}

.breadcrumbs {
  display: flex;
  align-items: center;
  color: $black;
  font-size: 16px;
  font-weight: 700;
  margin: auto 0;
  .channelName {
    white-space: nowrap;
  }
}

.channelsLink {
  color: inherit;
  text-decoration: none;
  margin-right: 6px;
  font-weight: 400;

  &:hover {
    color: $orange;
  }
}

.filters {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  padding: 0px 18px;
  .label {
    margin-right: 13px;
    font-size: 14px;
    line-height: 21px;
  }
}
