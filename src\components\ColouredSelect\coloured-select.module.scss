@use "../../styles/theme.scss" as *;

.menuButton {
  height: 47px;
  width: 100%;
  color: $black;
  padding: 4px 12px;
  border-radius: 8px;
  font-size: 14px;
  line-height: 21px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: background 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &.error {
    border: 2px solid $error !important;
  }
  &.disabled {
    cursor: auto;
    pointer-events: none;
    opacity: 0.5;
  }
  svg {
    transition: all 0.2s ease;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }

  &.coloured- {
    background-color: #f2f2f2;
    color: #000;
    font-weight: 500;
  }
  &.coloured-invoice {
    background-color: $invoice;
    color: $invoice-text;
  }
  &.coloured-payment {
    background-color: $payment;
    color: $payment-text;
  }
  &.coloured-billingissues {
    background-color: $billing;
    color: $billing-text;
  }
  &.coloured-maintenanceanddowntime {
    background-color: $maintenance;
    color: $maintenance-text;
  }
  &.coloured-complaintsandfeedback {
    background-color: $complaints;
    color: $complaints-text;
  }

  &.coloured-2 {
    background: $open;
  }
  &.coloured-3 {
    background: $pending;
  }
  &.coloured-4 {
    background: $resolved;
    color: $off-white;
  }
  &.coloured-5 {
    background: $closed;
    color: $off-white;
  }
}

.inputLabel {
  font-size: 12px;
  line-height: 16px;
  color: $placeholder;
  margin-bottom: 8px;
}

.expand {
  display: flex;
  align-items: center;
}

.box {
  margin-bottom: 12px;
  position: relative;
}

.menuItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.1s ease;
  color: $black;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  margin-bottom: 8px;
  border: 2px solid;
  &:last-of-type {
    margin-bottom: 0px;
  }
  &:hover {
    background: none;
  }
  &.cat-selectcategory {
    border: 1px solid;
    background-color: #f2f2f2;
    border-color: #f2f2f2;
    color: #000;
    font-weight: 500;
    &.active {
      border-color: #000;
    }
  }
  &.cat-invoice {
    background-color: $invoice;
    border-color: $invoice;
    color: $invoice-text;
    &.active {
      border-color: $invoice-text;
    }
  }
  &.cat-payment {
    background-color: $payment;
    border-color: $payment;
    color: $payment-text;
    &.active {
      border-color: $payment-text;
    }
  }
  &.cat-billingissues {
    background-color: $billing;
    border-color: $billing;
    color: $billing-text;
    &.active {
      border-color: $billing-text;
    }
  }
  &.cat-maintenanceanddowntime {
    background-color: $maintenance;
    border-color: $maintenance;
    color: $maintenance-text;
    &.active {
      border-color: $maintenance-text;
    }
  }
  &.cat-complaintsandfeedback {
    background-color: $complaints;
    border-color: $complaints;
    color: $complaints-text;
    &.active {
      border-color: $complaints-text;
    }
  }

  &.cat-open {
    background: $open;
    border: none;
  }
  &.cat-pending {
    background: $pending;
    border: none;
  }
  &.cat-resolved {
    background: $resolved;
    color: $off-white;
    border: none;
  }
  &.cat-closed {
    background: $closed;
    color: $off-white;
    border: none;
  }
}

.tooltip {
  font-size: 14px;
  line-height: 21px;
}

.errorText {
  margin: 0px 0px 0px 5px;
  font-size: 12px;
  color: $error;
  text-align: start;
}
