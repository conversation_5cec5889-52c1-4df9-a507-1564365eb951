@use "../../styles/theme.scss" as *;

.menuButton {
  border: none;
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  gap: 4px;
  cursor: pointer;
  transition: color 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &:hover {
    color: $orange;
  }
  svg {
    display: inline;
    vertical-align: middle;
    transition: transform 0.2s ease;
    margin-left: 5px;
  }
  &.iconOpen {
    svg {
      transform: rotate(180deg);
    }
  }
}

.box {
  height: 100%;
}

.container {
  display: grid;
  grid-template-columns: 1fr;
  grid-row-gap: 18px;
  max-height: 318px;
  overflow: auto;
  grid-column-gap: 32px;
  padding-right: 12px;
  grid-template-columns: repeat(2, auto);
}

.menuItem {
  display: flex;
  align-items: center;
  border-radius: 6px;
  transition: all 0.1s ease;
  color: $black;
  padding: 0;
  line-height: 21px;
  padding: 0;
  gap: 11px;
  cursor: auto;
  &:hover {
    background: none;
  }
}

.buttons {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  margin-top: 32px;
}
