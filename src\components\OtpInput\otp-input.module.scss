@use "../../styles/theme.scss" as *;

.container {
  display: grid;
  grid-template-rows: 1fr;
  align-items: center;
  color: $placeholder;
  font-size: 32px;
  position: relative;
  width: 100%;
  max-width: 313px;
  grid-column-gap: 12px;
  .numberDisplay {
    border: 1px solid $grey;
    width: 100%;
    height: 74px;
    line-height: 74px;
    cursor: text;
    text-align: center;
    border-radius: 8px;
  }
}

.input {
  border: none;
  font-size: 26px;
  width: 100%;
  height: 74px;
  line-height: 30px;
  text-align: center;
  background-color: transparent;
  outline: none;
  top: 1px;
  border-radius: 8px;
  &:focus {
    border: 3px solid $grey;
  }
}
