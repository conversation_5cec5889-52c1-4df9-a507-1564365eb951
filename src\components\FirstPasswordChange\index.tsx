import { useState } from "react";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import styles from "./first-password-change.module.scss";
import { Input } from "../Input";
import Toggle from "../Toggle";
import Button from "../Button";
import { validateAll } from "indicative/validator";
import { ApiPostAuth } from "../../pages/api/api";
import { useDispatch } from "react-redux";

const fields = ["password", "confirmPassword"];
const rules = getRules(fields);
const messages = getMessages(fields);

const FirstPasswordChange = ({ proceed }: any) => {
  const dispatch = useDispatch();
  const [data, setData] = useState(createStateObject(fields));

  const [loading, setLoading] = useState(false);

  const [twoFactorEnabled, setTwoFactorEnabled] = useState(true);

  const handleSubmit = () => {
    const input = {
      password: data.password,
      confirmPassword: data.confirmPassword,
    };
    validateAll(input, rules, messages)
      .then((response) => {
        if (input.password !== input.confirmPassword) {
          setData({
            ...data,
            errors: {
              password: " ",
              confirmPassword: "Passwords do not match",
            },
          });
        } else {
          setLoading(true);
          ApiPostAuth("/users/setpassword", {
            password: data.password,
            enable2fa: twoFactorEnabled,
          })
            .then((response) => {
              dispatch({
                type: "notify",
                payload: {
                  error: false,
                  message: response.data.message,
                },
              });
              proceed();
            })
            .catch((error) => {
              setLoading(false);
              dispatch({
                type: "notify",
                payload: {
                  error: true,
                  message: error.response.data.message,
                },
              });
            });
        }
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  return (
    <div className={styles.form}>
      <h2>Login</h2>
      <p className={styles.tag}>
        Set up a password to finish your registration.
      </p>
      <div className={styles.inputContainer}>
        {fields.map((key: string) => (
          <Input
            label={labels[key]}
            placeholder={placeholders[key]}
            value={data[key]}
            disabled={loading}
            onChange={(e: any) => {
              handleInputChange(key, e, data, setData);
            }}
            error={data.errors[key]}
            clear={() => {
              clearInput(key, setData);
            }}
            id={"change-password-" + key}
            onKeyDown={handleSubmit}
            password
          />
        ))}
        <div className={styles.twoFactor}>
          <div>Two-factor authentication</div>
          <div className={styles.toggle}>
            <span style={{ marginRight: 8 }}>
              {twoFactorEnabled ? "On" : "Off"}
            </span>
            <Toggle
              on={twoFactorEnabled}
              onChange={() => {
                setTwoFactorEnabled((prev: boolean) => !prev);
              }}
            />
          </div>
        </div>
      </div>
      <Button
        style={{ marginTop: 32 }}
        loading={loading}
        color="primary"
        onClick={handleSubmit}
      >
        Log in
      </Button>
    </div>
  );
};

export default FirstPasswordChange;
