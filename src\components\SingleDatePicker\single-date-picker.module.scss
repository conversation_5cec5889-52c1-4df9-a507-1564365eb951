@use "../../styles/theme.scss" as *;

.box {
  width: 100%;
  // max-width: 300px;
  &.large {
    max-width: 100%;
  }
}

.main {
  width: 388px;
  background: #fff;
  border-radius: 12px;
}

.label {
  position: absolute;
  background: #fff;
  color: $placeholder;
  font-size: 12px;
  line-height: 16px;
  top: -8px;
  left: 12px;
  padding: 0 6px;
  &.error {
    color: $error;
  }
}

.menuButton {
  border: 1px solid #74767e;
  color: $placeholder;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  height: 56px;
  display: flex;
  align-items: center;
  padding: 0 12px 0 16px;
  width: 100%;
  position: relative;
  cursor: text;
  transition:
    color 0.2s ease,
    background-color 0.2s ease;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none;
  &.disabled {
    color: $disabled-text;
    border-color: $disabled;

    .label {
      color: $disabled-text;
    }
  }
  &.hasValue {
    color: $black;
    &.disabled {
      color: $disabled-text;
    }
  }
  &.error {
    border: 2px solid $error !important;
  }
  &.background {
    height: 32px;
    background-color: #f7f6f6;
    padding: 5.5px 6px 5.5px 12px;
    border-radius: 6px;
    font-weight: 500;
    margin-right: 12px;
  }
  svg {
    display: inline;
    vertical-align: middle;
  }
}

.prevNext {
  display: flex;
  align-items: center;
  svg {
    vertical-align: middle;
  }
  .prev {
    margin-right: 16px;
  }
  .prev,
  .next {
    cursor: pointer;
    transition: color 0.1s ease;
    &:hover {
      color: $orange;
    }
  }
}

.mainDatePicker {
  width: 100%;
  border-radius: 8px;
}

.calendar {
  padding: 16px 0;
  user-select: none;
  .days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    width: 100%;
    justify-items: center;
    .letter {
      color: $black;
      font-size: 12px;
      line-height: 24px;
      padding: 9px 16px;
    }
  }
}

.datesGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(6, 42px);
  width: 100%;
  justify-content: stretch;
  align-items: center;
  .cellContainer {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    &.disabled {
      cursor: auto;
    }
    &:hover {
      .day:not(.active):not(.disabled) {
        background-color: $light-orange;
      }
    }
  }
  .day {
    font-size: 12px;
    line-height: 24px;
    height: 32px;
    width: 32px;
    border-radius: 1000px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    z-index: 20;
    &.now {
      border: 1px solid $orange;
    }
    &.disabled {
      opacity: 0.2;
      cursor: auto;
    }
    &.active {
      color: #fff;
      background-color: $orange;
      cursor: auto;
      &:hover {
        background-color: $orange;
      }
    }
    &.pad {
      color: #74767e;
      &.active {
        color: #fff;
      }
    }
  }
}

.errorText {
  margin: 0px 0px 0px 5px;
  font-size: 12px;
  color: $error;
  text-align: start;
}
