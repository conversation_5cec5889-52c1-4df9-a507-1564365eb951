import styles from "./header.module.scss";
import Menu from "../Menu";
import { LogOut, User, Users, Ticketing, Sliders } from "../svgs";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { logOut } from "../utils/logOut";

const Header = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { userInfo } = useSelector((state: any) => state);

  return (
    <header className={styles.header}>
      <img src="/Logo.png" className={styles.logo} />
      <div className={styles.menus}>
        <Menu
          data={{
            label: userInfo ? userInfo.firstName : "",
            items: [
              {
                label: "Profile",
                icon: <User />,
                link: "/user-profile",
              },
              {
                label: "Logout",
                icon: <LogOut />,
                link: "/login",
                onClick: () => {
                  logOut(dispatch, navigate);
                },
              },
            ],
          }}
        />
      </div>
    </header>
  );
};

export default Header;
