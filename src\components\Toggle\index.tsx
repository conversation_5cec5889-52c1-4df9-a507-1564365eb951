import { useState } from "react";
import Shimmer from "../Shimmer";
import styles from "./toggle.module.scss";

const Toggle = ({
  style,
  on,
  onChange = () => {},
  disabled,
  id = "",
  readonly,
  blue,
}: any) => {
  const root = getComputedStyle(document.getElementById("root")!);
  const [transitioning, setTransitioning] = useState(false);
  return (
    <div
      className={`${styles.main} ${id}`}
      style={{
        ...style,
        background: on
          ? blue
            ? "#B5DBF0"
            : root.getPropertyValue("--orange")
          : blue
          ? "#CDCDD3"
          : "#D4D3D3",
        cursor: disabled || readonly ? "auto" : "pointer",
        opacity: disabled ? 0.5 : 1,
      }}
      onClick={(e) => {
        e.stopPropagation();
        if (!disabled && !readonly) {
          setTransitioning(true);
          onChange();
          setTimeout(() => {
            setTransitioning(false);
          }, 350);
        }
      }}
    >
      {disabled && <Shimmer />}
      <div
        className={styles.thumb}
        style={{
          backgroundColor: blue ? (on ? "#0887CD" : "#FFF") : "",
          right: on ? 2 : 18,
          transition: transitioning
            ? "right 0.35s ease, background-color 0.1s ease"
            : "",
        }}
      />
    </div>
  );
};

export default Toggle;
