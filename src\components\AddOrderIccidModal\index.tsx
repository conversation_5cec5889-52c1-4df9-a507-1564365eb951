import styles from "./add-order-iccid-modal.module.scss";
import Modal from "../Modal";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { Input } from "../Input";
import {
  clearInput,
  createStateObject,
  displayErrors,
  getMessages,
  getRules,
  handleInputChange,
  labels,
  placeholders,
} from "../utils/InputHandlers";
import { validateAll } from "indicative/validator";
import { ApiPatch, ApiPostAuth } from "../../pages/api/api";

const fields = ["iccid"];
const rules = getRules(fields);
const messages = getMessages(fields);

const AddOrderIccidModal = ({ show, setShow, order, repopulate }: any) => {
  const dispatch = useDispatch();

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(createStateObject(fields));

  const addIccid = () => {
    validateAll(data, rules, messages)
      .then((response) => {
        setLoading(true);
        ApiPatch(
          "tempPortInId" in order
            ? "/accounts/portin/temp/iccid"
            : "/accounts/tempsubscription/iccid",
          "tempPortInId" in order
            ? {
                tempPortInId: order.tempPortInId,
                iccid: data.iccid,
              }
            : {
                tempSubscriptionId: order.tempSubscriptionId,
                iccid: data.iccid,
              }
        )
          .then((response) => {
            setShow(false);
            reset();
            repopulate();
            dispatch({
              type: "notify",
              payload: {
                error: false,
                message: "ICCID allocated successfully",
              },
            });
          })
          .catch((error) => {
            setLoading(false);
            dispatch({
              type: "notify",
              payload: {
                error: true,
                message: error.response.data.message,
              },
            });
          });
      })
      .catch((errors) => {
        displayErrors(errors, setData);
      });
  };

  const reset = () => {
    setTimeout(() => {
      setLoading(false);
      setData(createStateObject(fields));
    }, 300);
  };

  return (
    <Modal
      saveButton="Add ICCID"
      image="/bulk_edit_confirm_graphic.svg"
      show={show}
      close={() => {
        reset();
        setShow(false);
      }}
      proceed={addIccid}
      loading={loading}
    >
      <div className={styles.main}>
        <h3 style={{ marginBottom: 40 }}>Add ICCID</h3>
        <div className={styles.summaryContainer}>
          <div className={styles.summarySection}>
            <div className={styles.label}>Subscriber:</div>
            <div className={styles.data}>{order?.subscriberName}</div>
          </div>
          <div className={styles.summarySection}>
            <div className={styles.label}>Product Name:</div>
            <div className={styles.data}>{order?.product.offerName}</div>
          </div>
        </div>
        <Input
          label={labels.iccid}
          placeholder={placeholders.iccid}
          value={data.iccid}
          onChange={(e: any) => {
            handleInputChange("iccid", e, data, setData);
          }}
          error={data.errors.iccid}
          clear={() => {
            clearInput("iccid", setData);
          }}
          disabled={loading}
        />
      </div>
    </Modal>
  );
};

export default AddOrderIccidModal;
